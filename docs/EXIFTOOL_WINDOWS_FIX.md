# Windows ExifTool 修复方案

## 问题描述

在Windows平台上，特别是ARM64架构，ExifTool执行时出现ENOENT错误，导致GPS数据提取失败。错误信息显示：

```
❌ 进程执行错误: spawn C:\Program Files (x86)\Video\MEEA-VIOFO-DEBUG\resources\app.asar.unpacked\node_modules\exiftool-vendored.pl\bin\exiftool ENOENT (ENOENT)
```

## 根本原因

1. **路径空格问题**: ExifTool路径包含空格（`Program Files (x86)`），在Windows命令行执行时被错误解析
2. **Perl脚本执行**: Windows版本的ExifTool是Perl脚本，需要Perl解释器才能执行
3. **架构匹配**: ARM64和x64需要使用对应架构的ExifTool和Perl解释器

## 解决方案

### 1. 更新ExifTool路径查找逻辑

修改 `electron/windows-exiftool-fix.js` 中的 `findExifToolPath()` 方法：

- 优先查找自定义ExifTool目录中的Perl脚本 (`exiftool/win-arm64/exiftool_files/exiftool.pl`)
- 根据当前架构 (`process.arch`) 选择正确的目录
- 支持开发环境和打包环境的不同路径

### 2. 添加Perl解释器支持

新增 `findPerlInterpreter()` 方法：

- 查找打包的Perl解释器 (`exiftool/win-arm64/exiftool_files/perl.exe`)
- 支持系统Perl作为备选方案
- 根据架构选择正确的Perl解释器

### 3. 改进命令执行逻辑

更新 `executeWithMethod()` 方法：

- 自动检测Perl脚本并使用Perl解释器执行
- 正确处理包含空格的路径
- 提供多种执行方式作为备选

### 4. 更新打包配置

修改 `package.json` 的 `extraResources` 配置：

```json
"extraResources": [
  {
    "from": "exiftool/win-x64",
    "to": "exiftool/win-x64",
    "filter": ["**/*"]
  },
  {
    "from": "exiftool/win-arm64",
    "to": "exiftool/win-arm64",
    "filter": ["**/*"]
  }
]
```

## 文件结构

```
exiftool/
├── win-x64/
│   └── exiftool_files/
│       ├── perl.exe          # Perl解释器
│       ├── exiftool.pl       # ExifTool Perl脚本
│       └── ...               # 其他依赖文件
└── win-arm64/
    └── exiftool_files/
        ├── perl.exe          # ARM64 Perl解释器
        ├── exiftool.pl       # ExifTool Perl脚本
        └── ...               # 其他依赖文件
```

## 执行流程

1. **初始化**: 根据架构查找ExifTool Perl脚本路径
2. **验证**: 测试ExifTool是否可执行
3. **执行**: 使用Perl解释器执行ExifTool脚本
4. **备选**: 如果主要方法失败，尝试其他执行方式

## 测试方法

运行测试脚本验证修复效果：

```bash
node scripts/test-exiftool-fix.js
```

## 诊断信息

如果仍然出现问题，可以查看详细的诊断信息：

- 平台和架构信息
- ExifTool和Perl路径检查
- 文件存在性验证
- 执行权限检查

## 注意事项

1. **架构匹配**: 确保使用与当前系统架构匹配的ExifTool和Perl
2. **路径处理**: 正确处理包含空格的Windows路径
3. **权限问题**: 确保ExifTool文件具有执行权限
4. **防病毒软件**: 可能需要将应用目录添加到防病毒软件白名单

## 相关文件

- `electron/windows-exiftool-fix.js` - 主要修复逻辑
- `electron/main.js` - ExifTool初始化和使用
- `package.json` - 打包配置
- `scripts/test-exiftool-fix.js` - 测试脚本
