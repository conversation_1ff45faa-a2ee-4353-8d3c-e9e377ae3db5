# 高德地图配置页面粘贴功能修复

## 问题描述
用户反馈在高德地图配置页面的App Key输入框中无法粘贴内容。

## 问题分析
可能的原因：
1. 输入框缺少粘贴事件处理
2. Electron环境下的剪贴板权限问题
3. 键盘快捷键被阻止
4. 浏览器安全策略限制

## 解决方案

### 1. 增强的粘贴事件处理
```typescript
onPaste={(e) => {
  // 确保粘贴操作正常工作
  const pastedText = e.clipboardData?.getData('text') || '';
  if (pastedText) {
    setApiKey(pastedText.trim());
    e.preventDefault();
  }
}}
```

### 2. 键盘快捷键支持
添加了完整的键盘快捷键处理：
- **Ctrl+V / Cmd+V**: 粘贴（保持浏览器默认行为）
- **Ctrl+A / Cmd+A**: 全选
- **Enter**: 保存配置
- **Escape**: 取消并关闭弹窗

```typescript
const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
  // Ctrl+V 或 Cmd+V 粘贴 - 让浏览器处理默认行为
  if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
    return;
  }
  
  // Ctrl+A 或 Cmd+A 全选
  if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
    e.currentTarget.select();
    e.preventDefault();
  }
  
  // Enter 键保存
  if (e.key === 'Enter') {
    handleSave();
  }
  
  // Escape 键取消
  if (e.key === 'Escape') {
    handleCancel();
  }
};
```

### 3. 粘贴按钮备选方案
添加了一个"粘贴"按钮，使用 Clipboard API：

```typescript
const handlePasteClick = async () => {
  try {
    // 尝试使用 Clipboard API
    if (navigator.clipboard && navigator.clipboard.readText) {
      const text = await navigator.clipboard.readText();
      if (text) {
        setApiKey(text.trim());
      }
    } else {
      // 如果 Clipboard API 不可用，提示用户使用键盘快捷键
      alert('请使用 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac) 粘贴');
    }
  } catch (error) {
    // 如果权限被拒绝或其他错误，提示用户使用键盘快捷键
    alert('请使用 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac) 粘贴');
  }
};
```

### 4. 用户体验改进
- **自动聚焦**: 弹窗打开时自动聚焦到输入框
- **视觉提示**: 添加了工具提示和说明文字
- **输入框属性优化**: 
  - `autoComplete="off"`: 禁用自动完成
  - `spellCheck={false}`: 禁用拼写检查
  - `title`: 添加工具提示说明快捷键

### 5. UI界面改进
- 在输入框上方添加了"粘贴"按钮，带有剪贴板图标
- 在输入框下方添加了操作提示文字
- 保持了原有的"清空"按钮功能

## 使用方法

用户现在可以通过以下方式粘贴App Key：

1. **键盘快捷键**（推荐）:
   - Windows/Linux: `Ctrl + V`
   - macOS: `Cmd + V`

2. **粘贴按钮**: 点击输入框上方的"粘贴"按钮

3. **右键菜单**: 在输入框中右键选择"粘贴"（浏览器默认行为）

## 兼容性
- ✅ 支持所有现代浏览器
- ✅ 支持 Electron 环境
- ✅ 支持 Windows、macOS、Linux
- ✅ 优雅降级：如果 Clipboard API 不可用，会提示用户使用键盘快捷键

## 测试建议
1. 复制一个测试的App Key到剪贴板
2. 打开地图配置页面
3. 尝试以下操作：
   - 使用 Ctrl+V/Cmd+V 粘贴
   - 点击"粘贴"按钮
   - 使用 Ctrl+A/Cmd+A 全选
   - 使用 Enter 保存
   - 使用 Escape 取消
