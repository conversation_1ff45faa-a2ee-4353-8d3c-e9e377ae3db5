# macOS应用无法打开问题解决方案

## 问题描述

macOS构建成功，但是安装后无法打开应用，通常会出现以下错误：
- "应用已损坏，无法打开"
- "无法验证开发者"
- "应用无法打开"

## 原因分析

这是macOS Gatekeeper安全机制导致的，主要原因：
1. **未签名的应用**：应用没有有效的开发者签名
2. **Hardened Runtime**：启用了强化运行时但没有正确配置
3. **Gatekeeper阻止**：系统阻止运行未知来源的应用

## 解决方案

### 1. 修改构建配置（已完成）

在 `package.json` 中已修改macOS配置：
```json
{
  "mac": {
    "hardenedRuntime": false,    // 禁用强化运行时
    "gatekeeperAssess": false,   // 跳过Gatekeeper评估
    "identity": null             // 不使用代码签名
  }
}
```

### 2. 用户端解决方案

#### 方法1：右键打开（推荐）
1. 下载并安装dmg文件
2. **不要直接双击应用**
3. 右键点击应用图标
4. 选择"打开"
5. 在弹出的对话框中点击"打开"

#### 方法2：系统设置允许
1. 尝试打开应用（会被阻止）
2. 打开"系统偏好设置" → "安全性与隐私"
3. 在"通用"标签页底部会看到被阻止的应用
4. 点击"仍要打开"

#### 方法3：命令行移除隔离属性
```bash
# 进入应用目录
cd /Applications

# 移除隔离属性
sudo xattr -rd com.apple.quarantine MEEA-VIOFO.app

# 或者移除所有扩展属性
sudo xattr -c MEEA-VIOFO.app
```

#### 方法4：临时允许任何来源
```bash
# 允许任何来源的应用（需要管理员权限）
sudo spctl --master-disable

# 打开应用后，建议重新启用保护
sudo spctl --master-enable
```

### 3. 开发者解决方案（长期）

如果需要正式发布，建议：

#### 获取开发者证书
1. 注册Apple Developer Program ($99/年)
2. 创建开发者证书
3. 配置代码签名

#### 修改构建配置
```json
{
  "mac": {
    "hardenedRuntime": true,
    "gatekeeperAssess": false,
    "entitlements": "build/entitlements.mac.plist",
    "entitlementsInherit": "build/entitlements.mac.plist",
    "identity": "Developer ID Application: Your Name (TEAM_ID)"
  }
}
```

#### 公证应用
```bash
# 构建后公证
xcrun notarytool submit app.dmg --keychain-profile "notarytool-profile" --wait

# 装订公证票据
xcrun stapler staple app.dmg
```

### 4. 创建entitlements.mac.plist

如果需要使用hardened runtime，创建 `build/entitlements.mac.plist`：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
</dict>
</plist>
```

## 当前状态

✅ **已修改配置**：禁用了hardened runtime和代码签名要求
✅ **用户友好**：用户可以通过右键打开应用
⚠️ **安全提醒**：应用会显示"未知开发者"警告，但可以正常使用

## 用户使用指南

### 安装步骤：
1. 下载 `.dmg` 文件
2. 双击打开dmg文件
3. 将应用拖拽到Applications文件夹
4. **重要**：右键点击应用选择"打开"（不要直接双击）
5. 在警告对话框中点击"打开"

### 首次打开后：
- 后续可以正常双击打开
- 应用会被系统记住为"安全"应用

## 测试验证

重新构建后测试：
1. 下载新的dmg文件
2. 按照上述步骤安装
3. 验证应用可以正常打开和运行

如果仍有问题，请检查：
- 系统版本兼容性
- 应用权限设置
- 控制台错误日志
