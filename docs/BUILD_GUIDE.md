# 构建指南

本文档详细说明如何构建 MEEA-VIOFO 桌面应用程序，包括多平台打包和 FFmpeg 配置。

## 快速开始

### 1. 安装依赖

```bash
yarn install
```

### 2. 设置 FFmpeg 二进制文件

```bash
# 创建 FFmpeg 目录结构
yarn setup:ffmpeg

# 复制当前平台的 FFmpeg（用于开发测试）
yarn setup:ffmpeg:current
```

### 3. 构建所有平台

```bash
# 构建前端资源并打包所有平台
yarn dist
```

## 详细说明

### FFmpeg 配置

应用程序依赖 FFmpeg 进行视频处理和 GPS 数据提取。需要为每个目标平台准备对应的 FFmpeg 二进制文件。

#### 目录结构

```
ffmpeg/
├── win-x64/          # Windows x64
│   ├── ffmpeg.exe
│   └── ffprobe.exe
├── win-arm64/        # Windows ARM64
│   ├── ffmpeg.exe
│   └── ffprobe.exe
├── mac-x64/          # macOS Intel
│   ├── ffmpeg
│   └── ffprobe
├── mac-arm64/        # macOS Apple Silicon
│   ├── ffmpeg
│   └── ffprobe
├── linux-x64/        # Linux x64
│   ├── ffmpeg
│   └── ffprobe
└── linux-arm64/      # Linux ARM64
    ├── ffmpeg
    └── ffprobe
```

#### 获取 FFmpeg 二进制文件

**Windows:**
- 下载地址: https://ffmpeg.org/download.html#build-windows
- 推荐: https://github.com/BtbN/FFmpeg-Builds/releases

**macOS:**
```bash
# 使用 Homebrew
brew install ffmpeg

# 查找二进制文件位置
which ffmpeg
which ffprobe
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# 或下载静态构建版本
# https://johnvansickle.com/ffmpeg/
```

### 构建脚本

#### 主要构建命令

```bash
# 构建所有平台（推荐）
yarn dist

# 单独构建特定平台
yarn dist:mac      # macOS
yarn dist:win      # Windows  
yarn dist:linux    # Linux
```

#### 构建输出

所有构建产物将输出到 `dist/` 目录：

```
dist/
├── MEEA-VIOFO-1.0.0-darwin-x64.dmg
├── MEEA-VIOFO-1.0.0-darwin-x64.zip
├── MEEA-VIOFO-1.0.0-darwin-arm64.dmg
├── MEEA-VIOFO-1.0.0-darwin-arm64.zip
├── MEEA-VIOFO-Setup-1.0.0-win32-x64.exe
├── MEEA-VIOFO-1.0.0-win32-x64.zip
├── MEEA-VIOFO-Setup-1.0.0-win32-arm64.exe
├── MEEA-VIOFO-1.0.0-win32-arm64.zip
├── MEEA-VIOFO-1.0.0-linux-x64.AppImage
├── MEEA-VIOFO-1.0.0-linux-x64.tar.gz
├── MEEA-VIOFO-1.0.0-linux-arm64.AppImage
└── MEEA-VIOFO-1.0.0-linux-arm64.tar.gz
```

### 开发环境

在开发环境中，应用程序会自动使用 `@ffmpeg-installer` 包提供的 FFmpeg 二进制文件：

```bash
# 启动开发服务器
yarn dev
```

### 构建配置

主要配置在 `package.json` 的 `build` 字段中：

- **输出目录**: `dist/`
- **支持平台**: Windows, macOS, Linux
- **支持架构**: x64, ARM64
- **输出格式**: 
  - Windows: NSIS 安装包 + ZIP 压缩包
  - macOS: DMG 镜像 + ZIP 压缩包
  - Linux: AppImage + tar.gz 压缩包

### 注意事项

1. **FFmpeg 版本**: 建议使用 FFmpeg 4.4+ 版本以确保兼容性
2. **文件权限**: Unix 系统上的 FFmpeg 二进制文件需要执行权限 (755)
3. **文件大小**: FFmpeg 二进制文件较大，会显著增加应用程序体积
4. **跨平台构建**: 某些平台可能需要在对应系统上进行构建以获得最佳兼容性

### 故障排除

#### FFmpeg 未找到

如果在运行时出现 FFmpeg 未找到的错误：

1. 检查 `ffmpeg/` 目录是否包含对应平台的二进制文件
2. 确认文件名正确（Windows 需要 `.exe` 扩展名）
3. 检查文件权限（Unix 系统需要执行权限）

#### 构建失败

1. 确保所有依赖已正确安装: `yarn install`
2. 清理构建缓存: `yarn cleanup`
3. 检查 Node.js 版本是否符合要求 (>=20.19.0)

#### 内存不足

大型应用程序构建可能需要更多内存：

```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
yarn dist
```
