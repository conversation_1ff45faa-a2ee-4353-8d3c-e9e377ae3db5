# 构建架构说明

## 🏗️ 架构类型

### x64 (Intel/AMD 64位)
- **适用于**: 传统的 Intel 和 AMD 处理器
- **兼容性**: 最广泛的兼容性
- **性能**: 在 x64 处理器上原生运行

### ARM64 (ARM 64位)
- **适用于**: ARM 架构处理器
- **Windows**: Surface Pro X, 其他 ARM Windows 设备
- **macOS**: Apple Silicon (M1, M2, M3 等)
- **Linux**: Raspberry Pi 4, 其他 ARM 服务器

## 📦 构建命令详解

### Windows 构建选项

| 命令 | 架构 | 产物 | 说明 |
|------|------|------|------|
| `yarn build:windows-x64` | x64 | `MEEA-VIOFO-Setup-{version}-win32-x64.exe` | 仅 x64 架构 |
| `yarn build:windows-arm64` | ARM64 | `MEEA-VIOFO-Setup-{version}-win32-arm64.exe` | 仅 ARM64 架构 |
| `yarn build:windows-x64-arm64` | x64 + ARM64 | 两个安装包 | 同时构建两种架构 |
| `yarn build:windows` | x64 + ARM64 | 两个安装包 | 所有 Windows 架构 |

### macOS 构建选项

| 命令 | 架构 | 产物 | 说明 |
|------|------|------|------|
| `yarn build:macos-x64` | x64 | `MEEA-VIOFO-{version}-mac-x64.dmg` | Intel Mac |
| `yarn build:macos-arm64` | ARM64 | `MEEA-VIOFO-{version}-mac-arm64.dmg` | Apple Silicon Mac |
| `yarn build:macos` | x64 + ARM64 | 两个安装包 | 所有 macOS 架构 |

### Linux 构建选项

| 命令 | 架构 | 产物 | 说明 |
|------|------|------|------|
| `yarn build:linux-x64` | x64 | `MEEA-VIOFO-{version}-linux-x64.AppImage` | x64 Linux |
| `yarn build:linux-arm64` | ARM64 | `MEEA-VIOFO-{version}-linux-arm64.AppImage` | ARM64 Linux |
| `yarn build:linux` | x64 + ARM64 | 两个安装包 | 所有 Linux 架构 |

## 🎯 选择合适的构建方式

### 场景 1: 只需要 x64 版本
```bash
yarn build:windows-x64
yarn build:macos-x64
yarn build:linux-x64
```

### 场景 2: 只需要 ARM64 版本
```bash
yarn build:windows-arm64
yarn build:macos-arm64
yarn build:linux-arm64
```

### 场景 3: 需要完整的平台支持
```bash
yarn build:windows    # x64 + ARM64
yarn build:macos      # x64 + ARM64
yarn build:linux     # x64 + ARM64
```

### 场景 4: 构建所有平台和架构
```bash
yarn build:all
```

## 📊 架构使用统计

### Windows
- **x64**: ~95% 的 Windows 设备
- **ARM64**: ~5% 的 Windows 设备 (Surface Pro X 等)

### macOS
- **x64**: Intel Mac (2020年前)
- **ARM64**: Apple Silicon Mac (2020年后)

### Linux
- **x64**: 大部分桌面和服务器
- **ARM64**: Raspberry Pi, ARM 服务器

## 🚀 推荐策略

### 开发测试
```bash
# 构建当前平台的主要架构
yarn build:windows-x64    # Windows 开发
yarn build:macos-arm64     # Apple Silicon Mac 开发
yarn build:linux-x64      # Linux 开发
```

### 发布版本
```bash
# 构建所有架构以获得最大兼容性
yarn build:all
```

### 特定需求
```bash
# 如果明确知道目标架构
yarn build:windows-arm64  # 专门为 ARM Windows 设备
yarn build:macos-x64      # 专门为 Intel Mac
```

## ⚠️ 注意事项

1. **构建时间**: 多架构构建需要更多时间
2. **存储空间**: 每个架构都会生成独立的安装包
3. **测试**: 建议在对应架构的设备上测试
4. **依赖**: 某些原生依赖可能不支持所有架构

## 🔍 如何确定用户设备架构

### Windows
```cmd
echo %PROCESSOR_ARCHITECTURE%
```

### macOS
```bash
uname -m
# x86_64 = Intel (x64)
# arm64 = Apple Silicon
```

### Linux
```bash
uname -m
# x86_64 = x64
# aarch64 = ARM64
```

## 📝 总结

- **单架构构建**: 更快，文件更少，适合开发测试
- **多架构构建**: 更全面，兼容性更好，适合正式发布
- **选择建议**: 根据目标用户群体和发布需求选择合适的构建方式
