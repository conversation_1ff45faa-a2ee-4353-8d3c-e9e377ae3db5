# Console 和调试功能启用说明

## 🎯 问题解决

**问题**: 构建版本中console被禁用，无法进行调试和问题排查

**解决方案**: 启用所有调试功能，包括开发者工具、console控制台和右键菜单

## 🔧 已修改的配置

### 1. ✅ 启用开发者工具

**文件**: `electron/main.js`

**修改前**:
```javascript
devTools: isDev // 只在开发模式下启用开发者工具
```

**修改后**:
```javascript
devTools: true // 始终启用开发者工具，包括构建版本
```

### 2. ✅ 允许调试快捷键

**修改前**:
```javascript
// 禁用调试快捷键
if (input.key === 'F12' ||
    (input.control && input.shift && input.key === 'I') ||
    (input.meta && input.alt && input.key === 'I')) {
  event.preventDefault();
  console.log('🚫 已阻止调试快捷键:', input.key);
}
```

**修改后**:
```javascript
// 不再禁用调试快捷键，允许在构建版本中使用console和开发者工具
// 这有助于用户和开发者进行问题排查
/*
if (input.key === 'F12' || ...) {
  event.preventDefault();
}
*/
```

### 3. ✅ 启用右键菜单

**修改前**:
```javascript
// 禁用右键菜单，但允许在输入框中使用
mainWindow.webContents.on('context-menu', (event, params) => {
  if (params.inputFieldType !== 'none') {
    return; // 不阻止输入框的右键菜单
  }
  // 其他地方禁用右键菜单
  event.preventDefault();
});
```

**修改后**:
```javascript
// 允许右键菜单，便于访问开发者工具和调试功能
mainWindow.webContents.on('context-menu', (event, params) => {
  // 允许所有右键菜单，包括"检查元素"等调试功能
  // 这有助于用户和开发者进行问题排查
  console.log('🖱️ 右键菜单:', params.inputFieldType);
  // 不再阻止右键菜单
  // event.preventDefault();
});
```

## 🚀 现在可以使用的功能

### 1. 开发者工具
- **快捷键**: `F12` 或 `Ctrl+Shift+I` (Windows/Linux) / `Cmd+Option+I` (macOS)
- **右键菜单**: 右键点击 → "检查元素"

### 2. Console 控制台
- **直接快捷键**: `Ctrl+Shift+J` (Windows/Linux) / `Cmd+Option+J` (macOS)
- **通过开发者工具**: 打开开发者工具 → Console 标签页

### 3. 其他调试功能
- **刷新**: `F5` 或 `Ctrl+R` / `Cmd+R`
- **强制刷新**: `Ctrl+Shift+R` / `Cmd+Shift+R`
- **查看源码**: 右键 → "查看页面源代码"

## 📋 使用场景

### 构建过程调试
```bash
# 构建时可以看到详细的构建日志
yarn build:windows-x64

# 构建完成后，在应用中按F12查看运行时日志
```

### 问题排查
1. **应用启动问题**: 按 `F12` → Console 查看错误信息
2. **功能异常**: 在Console中执行调试代码
3. **性能问题**: Network标签页监控请求
4. **界面问题**: Elements标签页检查DOM结构

### 用户反馈
用户遇到问题时，可以：
1. 按 `F12` 打开开发者工具
2. 截图Console中的错误信息
3. 提供给开发者进行问题定位

## ⚠️ 注意事项

### 性能影响
- 开发者工具会消耗额外的内存和CPU
- 建议在不需要调试时关闭开发者工具

### 用户体验
- 普通用户可能不熟悉这些调试功能
- 可以在文档中提供简单的使用说明

### 安全考虑
- Console中可能显示敏感信息
- 不要在生产环境中执行不信任的代码

## 📚 相关文档

- **[docs/DEBUG_FEATURES.md](DEBUG_FEATURES.md)** - 详细的调试功能说明
- **[BUILD.md](../BUILD.md)** - 构建说明（已更新）
- **[README.md](../README.md)** - 项目总览

## 🎉 总结

现在构建版本中已经完全启用了调试功能：

- ✅ **Console 可用**: 可以查看所有日志和错误信息
- ✅ **开发者工具可用**: F12 或右键菜单都可以打开
- ✅ **调试快捷键可用**: 所有标准调试快捷键都正常工作
- ✅ **右键菜单可用**: 包含检查元素等调试选项

这将大大便于构建版本的调试、问题排查和用户支持。
