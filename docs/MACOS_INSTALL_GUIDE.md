# 🍎 macOS 安装指南

## ✅ 预签名应用程序

**好消息！** 从最新版本开始，MEEA-VIOFO 应用程序已在构建时自动进行自签名，大大减少了安装障碍。

### 🚀 标准安装流程
1. **下载** 对应架构的 `.dmg` 文件
2. **双击** DMG 文件挂载
3. **拖拽** 应用程序到 Applications 文件夹
4. **首次启动** 时，如遇安全提示：
   - 右键点击应用程序
   - 选择"打开"
   - 在对话框中点击"打开"
5. **后续使用** 可正常双击启动

## ⚠️ 如仍遇到问题

虽然应用程序已预签名，但在某些情况下仍可能遇到"应用程序已损坏"的提示。以下是解决方法：

### 方法 0: 自签名（推荐，一劳永逸）

**最佳解决方案**，创建自签名证书对应用程序进行签名：

```bash
# 下载项目源码或脚本
git clone https://github.com/your-repo/meea-viofo-all.git
cd meea-viofo-all

# 运行自签名脚本
./scripts/self-sign-macos.sh
```

**优点**：
- ✅ 一次设置，永久解决
- ✅ 不需要每次都移除隔离属性
- ✅ 更安全的解决方案
- ✅ 支持应用程序更新

详细说明请查看：[自签名指南](SELF_SIGN_GUIDE.md)

### 方法 1: 移除隔离属性（推荐）

1. **下载应用程序**后，不要直接双击安装
2. **打开终端**（应用程序 > 实用工具 > 终端）
3. **执行以下命令**移除隔离属性：

```bash
# 进入下载文件夹
cd ~/Downloads

# 移除 DMG 文件的隔离属性
sudo xattr -rd com.apple.quarantine MEEA-VIOFO-*.dmg

# 或者移除 APP 文件的隔离属性（如果已经解压）
sudo xattr -rd com.apple.quarantine /Applications/MEEA-VIOFO.app
```

4. **重新安装**应用程序

### 方法 2: 系统偏好设置允许

1. **打开系统偏好设置** > **安全性与隐私**
2. **点击"通用"标签页**
3. **在"允许从以下位置下载的应用"部分**，选择"任何来源"
4. 如果没有"任何来源"选项，在终端中执行：
```bash
sudo spctl --master-disable
```
5. **重新安装**应用程序
6. **安装完成后**，建议重新启用 Gatekeeper：
```bash
sudo spctl --master-enable
```

### 方法 3: 右键安装

1. **右键点击** DMG 文件或 APP 文件
2. **选择"打开"**
3. **在弹出的对话框中点击"打开"**

### 方法 4: 使用 spctl 命令

```bash
# 允许特定应用程序运行
sudo spctl --add /Applications/MEEA-VIOFO.app

# 或者临时禁用 Gatekeeper
sudo spctl --master-disable
```

## 🔒 为什么会出现这个问题？

### 技术原因
- **未签名应用**: 应用程序没有 Apple 开发者证书签名
- **Gatekeeper 保护**: macOS 10.8+ 的安全机制
- **隔离属性**: 从网络下载的文件被标记为"隔离"

### 安全说明
- 这是正常的安全提示，不代表应用程序有恶意
- MEEA-VIOFO 是开源项目，代码透明可审查
- 建议从官方 GitHub Releases 下载

## 📋 安装步骤

### 标准安装流程
1. **下载** DMG 文件
2. **执行**上述任一解决方法
3. **双击** DMG 文件挂载
4. **拖拽** MEEA-VIOFO.app 到应用程序文件夹
5. **启动**应用程序

### 验证安装
```bash
# 检查应用程序是否正确安装
ls -la /Applications/MEEA-VIOFO.app

# 检查应用程序签名状态
codesign -dv /Applications/MEEA-VIOFO.app
```

## 🛠️ 开发者信息

### 代码签名状态
- **当前状态**: 未签名（开源项目）
- **安全性**: 代码开源，可审查
- **来源**: GitHub Actions 自动构建

### 未来计划
- 考虑申请 Apple 开发者证书
- 实现代码签名和公证
- 提供更好的安装体验

## 🆘 需要帮助？

如果以上方法都无法解决问题，请：

1. **检查 macOS 版本**：确保系统版本兼容
2. **查看系统日志**：控制台.app > 系统日志
3. **提交 Issue**：在 GitHub 项目页面报告问题
4. **提供信息**：包括 macOS 版本、错误信息截图

---

**⚠️ 重要提示**: 修改系统安全设置有一定风险，请确保从可信来源下载应用程序。
