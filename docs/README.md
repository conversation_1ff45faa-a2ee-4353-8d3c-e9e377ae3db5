# MEEA VIOFO 文档中心

欢迎来到 MEEA VIOFO 项目文档中心！这里包含了项目的完整文档和指南。

## 📚 文档目录

### 🚀 快速开始
- [项目主页](../README.md) - 项目概述和快速开始指南

### 📦 发布和部署
- [发布指南](RELEASE.md) - 详细的版本发布流程和最佳实践
- [GitHub Token 设置](GITHUB_TOKEN_SETUP.md) - CI/CD 权限配置步骤

### 🏗️ 开发文档
- [项目结构](PROJECT_STRUCTURE.md) - 代码组织和架构说明

### 📁 历史文档归档
`archive/` 目录包含了之前在根目录下的历史文档文件：

- [BUG_FIXES.md](archive/BUG_FIXES.md) - 错误修复文档
- [GROUPING_UPDATE.md](archive/GROUPING_UPDATE.md) - 文件分组更新
- [PLAYBACK_FEATURES_UPDATE.md](archive/PLAYBACK_FEATURES_UPDATE.md) - 视频播放功能更新
- [PROJECT_SETUP.md](archive/PROJECT_SETUP.md) - 初始项目设置文档
- [UI_IMPROVEMENTS.md](archive/UI_IMPROVEMENTS.md) - UI 改进文档
- [VIDEO_FILE_MANAGER.md](archive/VIDEO_FILE_MANAGER.md) - 视频文件管理器文档
- [VIDEO_PLAYBACK_DEBUG.md](archive/VIDEO_PLAYBACK_DEBUG.md) - 视频播放调试
- [VIDEO_PLAYBACK_SOLUTION.md](archive/VIDEO_PLAYBACK_SOLUTION.md) - 视频播放解决方案
- [VIDEO_PLAYER_FEATURES.md](archive/VIDEO_PLAYER_FEATURES.md) - 视频播放器功能
- [VIDEO_PLAYER_FIXES.md](archive/VIDEO_PLAYER_FIXES.md) - 视频播放器修复

## 🎯 常用操作

### 发布新版本
```bash
# 快速发布
./scripts/create-release.sh

# 或手动发布
git tag v1.0.0
git push origin v1.0.0
```

### 本地开发
```bash
# 安装依赖
yarn install

# 启动开发服务器
yarn dev

# 构建应用
yarn build
```

### CI/CD 设置
1. 按照 [GitHub Token 设置指南](GITHUB_TOKEN_SETUP.md) 配置权限
2. 推送代码或标签自动触发构建
3. 在 GitHub Releases 页面下载构建产物

## 🔗 相关链接

- [GitHub 仓库](https://github.com/your-username/meea-viofo-all)
- [GitHub Actions](https://github.com/your-username/meea-viofo-all/actions)
- [GitHub Releases](https://github.com/your-username/meea-viofo-all/releases)

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **查看文档**: 首先查看相关文档是否有解决方案
2. **检查 Issues**: 在 GitHub Issues 中搜索类似问题
3. **创建 Issue**: 如果没有找到解决方案，创建新的 Issue
4. **联系开发者**: 发送邮件到 <EMAIL>

## 📝 文档贡献

欢迎改进文档！如果您发现文档有误或需要补充：

1. Fork 项目
2. 修改文档
3. 提交 Pull Request

---

<div align="center">
  <p>📖 持续更新中...</p>
  <p>最后更新：2025年1月</p>
</div>
