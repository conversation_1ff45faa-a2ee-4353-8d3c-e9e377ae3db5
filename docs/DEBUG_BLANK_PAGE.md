# 空白页面调试指南

## 🐛 问题描述

Windows 应用启动后显示空白页面，需要查看详细的错误信息来诊断问题。

## 🔧 调试步骤

### 1. 自动打开开发者工具

**当前版本已自动启用**：
- 应用启动时会自动打开开发者工具
- 可以直接查看 Console 标签页中的错误信息

### 2. 手动打开开发者工具

如果开发者工具没有自动打开，可以使用以下方法：

**快捷键**：
- Windows/Linux: `F12` 或 `Ctrl+Shift+I`
- macOS: `Cmd+Option+I`

**右键菜单**：
- 右键点击空白页面
- 选择"检查元素"或"Inspect Element"

### 3. 查看错误信息

打开开发者工具后，按以下顺序检查：

#### A. Console 标签页
查看是否有红色的错误信息：
```
🚨 JavaScript错误: [错误详情]
🚨 未处理的Promise拒绝: [错误详情]
❌ 页面加载失败: [错误详情]
```

#### B. Network 标签页
检查资源加载情况：
- 查看是否有失败的请求（红色状态）
- 检查 JavaScript 和 CSS 文件是否正确加载
- 注意 404 或其他错误状态码

#### C. Elements 标签页
检查页面结构：
- 确认 `<div id="root"></div>` 元素存在
- 检查是否有内容被渲染到 root 元素中

### 4. 常见错误类型

#### A. 模块加载错误
```
Error: Cannot find module 'xxx'
Module not found: xxx
```
**解决方案**: 检查依赖是否正确打包

#### B. 路径错误
```
Failed to load resource: net::ERR_FILE_NOT_FOUND
404 Not Found
```
**解决方案**: 检查资源文件路径是否正确

#### C. JavaScript 语法错误
```
SyntaxError: Unexpected token
ReferenceError: xxx is not defined
```
**解决方案**: 检查代码编译是否正确

#### D. React 渲染错误
```
Error: Target container is not a DOM element
React is not defined
```
**解决方案**: 检查 React 应用初始化

## 🔍 详细调试信息

### 自动输出的调试信息

应用启动时会自动输出以下调试信息：

```
📄 页面开始加载...
✅ 页面加载完成
👁️ 主窗口已显示，标题栏保留，功能菜单已隐藏
🔧 窗口调试信息: {
  url: "file:///.../dist/index.html",
  title: "MEEA-VIOFO",
  isLoading: false,
  isLoadingMainFrame: false
}
🔧 页面调试信息: {
  title: "MEEA-VIOFO",
  readyState: "complete",
  rootElement: "exists",
  bodyChildren: 1,
  scripts: 6,
  errors: "handler exists"
}
```

### 手动检查命令

在 Console 中执行以下命令进行手动检查：

```javascript
// 检查根元素
console.log('Root element:', document.getElementById('root'));

// 检查页面状态
console.log('Document ready state:', document.readyState);

// 检查脚本加载
console.log('Scripts loaded:', document.scripts.length);

// 检查是否有React
console.log('React available:', typeof React !== 'undefined');

// 检查全局错误
console.log('Window errors:', window.onerror);
```

## 🚨 常见问题和解决方案

### 1. 页面完全空白，无任何内容

**可能原因**:
- JavaScript 文件加载失败
- React 应用初始化失败
- 根元素未找到

**检查步骤**:
1. 查看 Network 标签页，确认所有资源都成功加载
2. 查看 Console 是否有 JavaScript 错误
3. 检查 `document.getElementById('root')` 是否返回元素

### 2. 页面有基本结构但无内容

**可能原因**:
- React 组件渲染失败
- 路由配置错误
- 状态管理问题

**检查步骤**:
1. 查看 React DevTools（如果可用）
2. 检查组件是否正确挂载
3. 查看是否有组件渲染错误

### 3. 资源加载失败

**可能原因**:
- 文件路径错误
- 文件缺失
- 权限问题

**检查步骤**:
1. 查看 Network 标签页的失败请求
2. 检查文件是否存在于正确位置
3. 确认文件权限设置

## 🛠️ 临时解决方案

### 1. 强制刷新
- Windows/Linux: `Ctrl+Shift+R`
- macOS: `Cmd+Shift+R`

### 2. 清除缓存
在 Console 中执行：
```javascript
// 清除本地存储
localStorage.clear();
sessionStorage.clear();

// 重新加载页面
location.reload(true);
```

### 3. 检查文件完整性
确认以下文件存在：
- `dist/index.html`
- `dist/assets/*.js`
- `dist/assets/*.css`

## 📞 获取技术支持

如果问题仍然存在，请提供以下信息：

1. **Console 中的完整错误信息**
2. **Network 标签页的截图**
3. **操作系统版本**
4. **应用版本号**
5. **重现步骤**

### 导出调试信息

在 Console 中执行以下命令导出调试信息：

```javascript
// 导出完整的调试信息
const debugInfo = {
  userAgent: navigator.userAgent,
  url: location.href,
  title: document.title,
  readyState: document.readyState,
  scripts: Array.from(document.scripts).map(s => s.src),
  stylesheets: Array.from(document.styleSheets).map(s => s.href),
  errors: window.errorLog || [],
  timestamp: new Date().toISOString()
};

console.log('=== DEBUG INFO ===');
console.log(JSON.stringify(debugInfo, null, 2));
console.log('=== END DEBUG INFO ===');
```

复制输出的调试信息并提供给技术支持团队。
