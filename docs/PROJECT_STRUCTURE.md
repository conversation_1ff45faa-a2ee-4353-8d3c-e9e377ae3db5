# 项目结构说明

## 📁 目录结构

```
meea-viofo-all/
├── 📁 .github/
│   └── 📁 workflows/
│       └── 📄 build.yml              # CI/CD 构建工作流
├── 📁 assets/
│   ├── 📄 logo.svg                   # 项目Logo (SVG格式)
│   └── 📄 icon.ico                   # 应用图标 (占位符)
├── 📁 docs/
│   ├── 📄 GITHUB_TOKEN_SETUP.md     # GitHub Token 设置指南
│   ├── 📄 PROJECT_STRUCTURE.md      # 项目结构说明 (本文件)
│   └── 📄 RELEASE.md                 # 发布流程指南
├── 📁 electron/
│   ├── 📄 main.js                    # Electron 主进程
│   └── 📄 preload.js                 # 预加载脚本
├── 📁 public/
│   └── 📄 vite.svg                   # Vite 默认图标
├── 📁 scripts/
│   ├── 📄 create-release.sh          # 自动发布脚本
│   └── 📄 release.sh                 # 发布辅助脚本
├── 📁 src/
│   ├── 📁 components/
│   │   ├── 📄 GPSInfo.tsx            # GPS信息显示组件
│   │   ├── 📄 MapComponent.tsx       # 地图显示组件
│   │   ├── 📄 VideoFileManager.tsx   # 视频文件管理组件
│   │   ├── 📄 VideoPlayer.tsx        # 视频播放器组件
│   │   └── 📄 VideoTimeline.tsx      # 视频时间轴组件
│   ├── 📁 types/
│   │   └── 📄 electron.ts            # TypeScript 类型定义
│   ├── 📄 App.tsx                    # 主应用组件
│   ├── 📄 index.css                  # 全局样式
│   ├── 📄 main.tsx                   # React 入口文件
│   └── 📄 vite-env.d.ts              # Vite 环境类型
├── 📄 .gitignore                     # Git 忽略文件
├── 📄 README.md                      # 项目说明文档
├── 📄 electron-builder.env           # Electron Builder 环境配置
├── 📄 index.html                     # HTML 模板
├── 📄 package.json                   # 项目配置和依赖
├── 📄 tsconfig.json                  # TypeScript 配置
├── 📄 tsconfig.node.json             # Node.js TypeScript 配置
├── 📄 vite.config.ts                 # Vite 构建配置
└── 📄 yarn.lock                      # Yarn 依赖锁定文件
```

## 🏗️ 核心模块说明

### 📱 前端应用 (`src/`)

#### 主要组件
- **App.tsx**: 主应用布局，三栏式设计
- **VideoPlayer.tsx**: 核心视频播放器，支持高精度时间轴
- **VideoFileManager.tsx**: 文件管理器，支持智能分组
- **MapComponent.tsx**: 地图显示组件，集成地图API
- **GPSInfo.tsx**: GPS信息展示面板
- **VideoTimeline.tsx**: 视频时间轴控制器

#### 类型定义
- **electron.ts**: Electron相关的TypeScript接口定义

### 🖥️ 桌面应用 (`electron/`)

#### 主进程
- **main.js**: Electron主进程，窗口管理和系统集成
- **preload.js**: 预加载脚本，安全的渲染进程通信

### 🔧 构建配置

#### Vite配置
- **vite.config.ts**: 前端构建配置
- **index.html**: HTML模板文件

#### Electron Builder
- **package.json**: 包含electron-builder配置
- **electron-builder.env**: 构建环境变量

#### TypeScript
- **tsconfig.json**: 主TypeScript配置
- **tsconfig.node.json**: Node.js环境配置

### 🚀 CI/CD (`/.github/workflows/`)

#### 构建流程
- **build.yml**: 多平台自动构建和发布工作流

### 📜 脚本工具 (`/scripts/`)

#### 发布工具
- **create-release.sh**: 一键发布脚本
- **release.sh**: 发布辅助工具

### 📚 文档 (`/docs/`)

#### 用户指南
- **RELEASE.md**: 详细发布流程
- **GITHUB_TOKEN_SETUP.md**: CI/CD权限配置
- **PROJECT_STRUCTURE.md**: 项目结构说明

### 🎨 资源文件 (`/assets/`)

#### 图标资源
- **logo.svg**: 项目Logo (SVG矢量格式)
- **icon.ico**: 应用图标 (Windows ICO格式)

## 🔄 数据流

```mermaid
graph TD
    A[用户选择视频文件夹] --> B[VideoFileManager扫描文件]
    B --> C[显示文件列表]
    C --> D[用户选择视频]
    D --> E[VideoPlayer加载视频]
    E --> F[VideoTimeline显示进度]
    F --> G[MapComponent显示位置]
    G --> H[GPSInfo显示详情]
    
    I[用户拖动时间轴] --> J[VideoPlayer实时seek]
    J --> K[更新GPS数据]
    K --> L[更新地图位置]
```

## 🛠️ 开发工作流

1. **本地开发**: `yarn dev`
2. **代码提交**: 推送到main分支触发测试构建
3. **版本发布**: 创建标签触发正式构建
4. **自动部署**: GitHub Actions自动构建所有平台

## 📦 构建产物

### 开发构建
- `release/linux-unpacked/`: Linux开发版本
- 用于快速测试和验证

### 正式发布
- **Windows**: `.exe` NSIS安装程序
- **macOS**: `.dmg` 磁盘映像文件  
- **Linux**: `.AppImage` 便携应用

## 🔧 技术栈

- **前端**: React 19 + TypeScript + Chakra UI
- **桌面**: Electron 37
- **构建**: Vite + electron-builder
- **CI/CD**: GitHub Actions
- **包管理**: Yarn
