# MEEA VIOFO 发布指南

## 🚀 快速发布

### 方法一：使用发布脚本（推荐）

```bash
# 在项目根目录运行
./scripts/create-release.sh
```

脚本会自动：
1. 检查Git状态
2. 询问新版本号
3. 更新package.json
4. 创建Git标签
5. 推送到GitHub
6. 触发自动构建

### 方法二：手动发布

```bash
# 1. 确保代码已提交
git status

# 2. 更新版本号（不创建标签）
npm version 1.0.0 --no-git-tag-version

# 3. 提交版本更新
git add package.json
git commit -m "chore: bump version to 1.0.0"

# 4. 创建并推送标签
git tag v1.0.0
git push origin main
git push origin v1.0.0
```

## 📦 自动构建流程

推送版本标签后，GitHub Actions 会自动：

1. **构建所有平台**：
   - Windows: x64, x86 (32位), ARM64
   - macOS: Intel (x64), Apple Silicon (ARM64)
   - Linux: x64, ARM64

2. **生成安装包**：
   - Windows: `.exe` (NSIS安装程序)
   - macOS: `.dmg` (磁盘映像)
   - Linux: `.AppImage` (便携应用)

3. **创建GitHub Release**：
   - 自动生成发布说明
   - 提供所有平台的下载链接
   - 包含详细的安装说明

## 📋 版本命名规范

使用语义化版本控制 (Semantic Versioning)：

- `1.0.0` - 主版本号（重大更新）
- `1.1.0` - 次版本号（新功能）
- `1.1.1` - 修订号（Bug修复）

## 🔗 发布后的下载链接

发布完成后，用户可以在以下位置找到下载链接：

1. **GitHub Releases页面**：
   ```
   https://github.com/your-username/meea-viofo-all/releases
   ```

2. **具体版本页面**：
   ```
   https://github.com/your-username/meea-viofo-all/releases/tag/v1.0.0
   ```

3. **直接下载链接格式**：
   ```
   # Windows
   https://github.com/your-username/meea-viofo-all/releases/download/v1.0.0/MEEA-VIOFO-Setup-1.0.0-x64.exe
   
   # macOS
   https://github.com/your-username/meea-viofo-all/releases/download/v1.0.0/MEEA-VIOFO-1.0.0-arm64.dmg
   
   # Linux
   https://github.com/your-username/meea-viofo-all/releases/download/v1.0.0/MEEA-VIOFO-1.0.0-x64.AppImage
   ```

## ⚠️ 注意事项

1. **网络环境**：本地构建可能因网络问题失败，推荐使用GitHub Actions构建
2. **版本唯一性**：确保版本号未被使用过
3. **代码状态**：发布前确保所有代码已提交
4. **构建时间**：完整构建所有平台大约需要15-30分钟

## 🐛 故障排除

### 构建失败
- 检查GitHub Actions日志
- 确认package.json配置正确
- 验证所有依赖项可用

### 标签冲突
```bash
# 删除本地标签
git tag -d v1.0.0

# 删除远程标签
git push origin :refs/tags/v1.0.0
```

### 重新发布
如果需要重新发布相同版本：
1. 删除GitHub Release
2. 删除Git标签
3. 重新运行发布流程
