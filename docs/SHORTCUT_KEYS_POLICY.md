# 快捷键策略文档

## 概述

本文档描述了 MEEA-VIOFO 应用在不同环境下的快捷键策略，确保生产环境的安全性和用户体验。

## 快捷键分类

### 🚫 生产环境禁用的快捷键

#### 调试相关快捷键
- `F12` - 开发者工具
- `Ctrl+Shift+I` / `Cmd+Shift+I` - 开发者工具
- `Ctrl+Shift+J` / `Cmd+Shift+J` - 控制台
- `Ctrl+Shift+C` / `Cmd+Shift+C` - 元素检查器
- `Ctrl+Shift+D` / `Cmd+Shift+D` - 调试工具
- `Ctrl+Shift+K` / `Cmd+Shift+K` - 控制台清理
- `Cmd+Alt+I` - macOS 开发者工具

#### 刷新相关快捷键
- `Ctrl+R` / `Cmd+R` - 刷新页面
- `F5` - 刷新页面

#### 测试相关快捷键
- `Ctrl+Shift+L` / `Cmd+Shift+L` - 查看日志（仅调试模式）
- `Ctrl+Shift+T` / `Cmd+Shift+T` - 测试快捷键（仅调试模式）

### ✅ 生产环境保留的快捷键

#### 用户功能快捷键
- `Ctrl+Shift+M` / `Cmd+Shift+M` - 查看机器码（用于许可证申请）

#### 应用内快捷键
- `空格` - 视频播放/暂停
- `←` / `→` - 视频快退/快进
- `↑` / `↓` - 音量调节
- `M` - 静音切换
- `F` - 全屏切换
- `Escape` - 退出全屏/关闭窗口

## 实现策略

### 开发环境 (isDev = true)

#### 调试模式 (isDebugMode = true)
```javascript
// 启用所有调试快捷键
- Ctrl+Shift+I: 开发者工具
- Ctrl+R: 刷新
- Ctrl+Shift+L: 查看日志
- Ctrl+Shift+T: 测试快捷键
- Ctrl+Shift+M: 查看机器码
```

#### 非调试模式 (isDebugMode = false)
```javascript
// 只启用基本功能
- Ctrl+Shift+M: 查看机器码
// 禁用调试和刷新快捷键
```

### 生产环境 (IS_PRODUCTION = true)

```javascript
// 只启用用户功能快捷键
- Ctrl+Shift+M: 查看机器码

// 禁用所有调试和刷新快捷键
- 通过 before-input-event 事件拦截
- 阻止快捷键的默认行为
- 记录被阻止的快捷键日志
```

## 技术实现

### 1. 全局快捷键注册

```javascript
// 条件注册快捷键
if (isDebugMode) {
  // 注册调试快捷键
  globalShortcut.register('CommandOrControl+Shift+L', handler);
}

// 始终注册用户功能快捷键
globalShortcut.register('CommandOrControl+Shift+M', showMachineIdDialog);
```

### 2. 窗口级快捷键拦截

```javascript
mainWindow.webContents.on('before-input-event', (event, input) => {
  if (shouldBlockShortcut(input)) {
    event.preventDefault();
    console.log('🚫 已阻止快捷键:', input.key);
  }
});
```

### 3. 快捷键检测函数

```javascript
function shouldBlockShortcut(input) {
  return (
    // 调试快捷键
    input.key === 'F12' ||
    (input.control && input.shift && input.key === 'I') ||
    // 刷新快捷键
    (input.control && input.key === 'R') ||
    input.key === 'F5'
    // ... 其他禁用的快捷键
  );
}
```

## 安全考虑

### 1. 防止调试访问
- 禁用所有开发者工具快捷键
- 禁用控制台访问快捷键
- 禁用右键菜单

### 2. 防止意外刷新
- 禁用所有刷新快捷键
- 防止用户意外丢失数据

### 3. 保留必要功能
- 保留机器码查看功能（许可证申请需要）
- 保留应用内媒体控制快捷键

## 用户体验

### 1. 透明性
- 被阻止的快捷键会在控制台记录
- 不会显示错误提示给用户

### 2. 一致性
- 所有平台使用相同的快捷键策略
- Windows、macOS、Linux 行为一致

### 3. 功能性
- 保留所有必要的用户功能
- 不影响正常的应用使用

## 测试验证

### 开发环境测试
```bash
# 启动开发环境
yarn dev

# 测试调试快捷键（应该工作）
Ctrl+Shift+I, Ctrl+R, Ctrl+Shift+L

# 测试机器码快捷键（应该工作）
Ctrl+Shift+M
```

### 生产环境测试
```bash
# 构建生产版本
yarn dist:all

# 测试调试快捷键（应该被阻止）
F12, Ctrl+Shift+I, Ctrl+R, F5

# 测试机器码快捷键（应该工作）
Ctrl+Shift+M
```

## 维护说明

### 添加新的禁用快捷键
1. 在 `main.js` 的 `before-input-event` 处理器中添加检测
2. 在 `main-production.js` 中添加相同的检测
3. 更新本文档

### 添加新的用户功能快捷键
1. 使用 `globalShortcut.register()` 注册
2. 确保在所有环境下都可用
3. 更新本文档

这个策略确保了生产环境的安全性，同时保留了必要的用户功能。
