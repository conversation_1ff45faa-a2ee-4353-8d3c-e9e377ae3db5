# GPS功能实现文档

## 功能概述

本项目已成功实现从VIOFO行车记录仪视频文件中提取GPS信息并在高德地图中绘制路径的功能，包括：

- **GPS轨迹提取**: 从VIOFO视频文件中提取完整的GPS轨迹数据
- **实时位置显示**: 根据视频播放进度显示当前GPS位置
- **轨迹统计分析**: 计算行驶距离、平均速度、最大速度等统计信息
- **地图可视化**: 在高德地图上绘制完整的行驶轨迹
- **坐标系转换**: 自动将WGS84坐标转换为GCJ-02坐标系

## 技术实现

### 1. GPS数据提取

- **工具**: 使用 `exiftool-vendored` 包，内置了exiftool二进制文件
- **提取命令**: 使用参数 `-n -j -ee -G3 -a -u -U -GPS* -CreateDate -DateTimeOriginal -ModifyDate`
- **数据格式**: VIOFO视频文件将GPS数据存储在Doc1, Doc2, Doc3...等字段中，每个字段包含：
  - GPSLatitude: 纬度
  - GPSLongitude: 经度
  - GPSSpeed: 速度 (km/h)
  - GPSTrack: 方向角度
  - GPSDateTime: 时间戳

### 2. 数据结构

```typescript
interface GPSPoint {
  latitude: number;
  longitude: number;
  altitude?: number;
  speed?: number;
  heading?: number;
  timestamp?: string;
  accuracy?: number;
}

interface GPSStatistics {
  totalDistance: number;      // 总距离（米）
  duration: number;           // 持续时间（秒）
  averageSpeed: number;       // 平均速度（km/h）
  maxSpeed: number;           // 最大速度（km/h）
  minSpeed: number;           // 最小速度（km/h）
  speedFromGPS: number;       // 从GPS记录计算的平均速度
  speedFromDistance: number;  // 从距离计算的平均速度
  validSpeedPoints: number;   // 有效速度点数量
  totalPoints: number;        // 总点数
}

interface GPSTrack {
  points: GPSPoint[];
  totalDistance?: number;     // 总距离（米）
  duration?: number;          // 持续时间（秒）
  averageSpeed?: number;      // 平均速度（km/h）
  maxSpeed?: number;          // 最大速度（km/h）
  minSpeed?: number;          // 最小速度（km/h）
  startTime?: string;         // 开始时间
  endTime?: string;           // 结束时间
  pointCount?: number;        // GPS点数量
  statistics?: GPSStatistics; // 详细统计信息
}
```

### 3. GPS统计分析

项目提供了完整的GPS轨迹统计分析功能：

#### 3.1 距离计算
- **总距离**: 使用Haversine公式计算GPS点之间的实际距离
- **精确度**: 考虑地球曲率，提供米级精度的距离计算
- **累积计算**: 逐段累加所有GPS点之间的距离

#### 3.2 速度统计
- **平均速度**:
  - 优先使用GPS记录的速度数据计算平均值
  - 当GPS速度数据不足时，使用距离/时间计算
- **最大速度**: 记录轨迹中的最高速度
- **最小速度**: 记录轨迹中的最低速度（排除0值）
- **速度验证**: 对比GPS记录速度和距离计算速度，提供数据质量参考

#### 3.3 时间分析
- **行驶时间**: 计算从开始到结束的总时间
- **时间戳解析**: 支持多种GPS时间格式
- **时区处理**: 自动处理时区转换

#### 3.4 数据质量评估
- **有效点统计**: 统计包含完整GPS信息的点数量
- **数据完整度**: 计算有效数据点占总点数的比例
- **质量指示器**: 根据数据点数量和完整度评估数据质量

### 4. 坐标系转换

由于高德地图使用GCJ-02坐标系，而GPS设备通常使用WGS84坐标系，项目实现了自动坐标转换功能：

- **WGS84 → GCJ-02**: 将GPS原始坐标转换为适合中国地图的坐标系
- **转换算法**: 使用标准的坐标转换算法，确保位置准确性
- **中国境内判断**: 只对中国境内的坐标进行转换，境外坐标保持不变
- **统计数据保持**: 坐标转换不影响距离、速度等统计数据的准确性

### 5. 地图显示

- **地图服务**: 高德地图 API
- **API密钥**: c40508bc073682b5c1ad08af528feda0
- **功能**:
  - 显示完整GPS轨迹路径
  - 实时显示当前播放位置
  - 自动调整地图视野以显示完整轨迹
  - 支持地图缩放和拖拽

### 6. 视频同步

- 视频播放时间与GPS轨迹同步
- 根据播放进度计算对应的GPS点位置
- 在地图上实时显示当前位置标记

## 使用方法

1. 选择包含GPS数据的VIOFO视频文件
2. 应用会自动提取GPS数据（首次加载时）
3. 地图会显示完整的行驶轨迹
4. 播放视频时，地图上的红色标记会跟随当前播放位置移动

## 性能优化

- GPS数据提取结果会缓存在VideoFile对象中，避免重复提取
- 地图组件支持轨迹和当前位置的独立更新
- 使用高效的GPS点索引算法进行时间同步

## 支持的文件格式

- VIOFO行车记录仪生成的MP4文件
- 包含GPS元数据的视频文件

## 注意事项

- 首次加载视频文件时需要时间提取GPS数据
- 如果视频文件不包含GPS数据，地图将不显示轨迹
- 需要网络连接以加载高德地图
