# 应用图标配置指南

## 📁 文件结构

```
build/icons/
├── icon.png      # Linux 图标 (512x512)
├── icon.ico      # Windows 图标 (256x256, 多尺寸)
├── icon.icns     # macOS 图标 (512x512 或 1024x1024)
└── <EMAIL>   # 高分辨率图标 (1024x1024)
```

## 🎨 图标规格要求

### Windows (.ico)
- **尺寸**: 16x16, 32x32, 48x48, 256x256 (多尺寸ICO文件)
- **格式**: ICO 格式
- **透明度**: 支持
- **用途**: 应用程序图标、任务栏、系统托盘

### macOS (.icns)
- **尺寸**: 16x16 到 1024x1024 (多尺寸ICNS文件)
- **格式**: ICNS 格式
- **透明度**: 支持
- **用途**: Dock图标、Finder图标、应用程序包

### Linux (.png)
- **尺寸**: 512x512 像素
- **格式**: PNG 格式
- **透明度**: 支持
- **用途**: 桌面图标、应用程序菜单

## 🛠️ 图标生成工具

### 在线工具
1. **ICO Convert**: https://icoconvert.com/
2. **CloudConvert**: https://cloudconvert.com/
3. **Favicon Generator**: https://www.favicon-generator.org/

### 命令行工具
```bash
# 安装 ImageMagick (macOS)
brew install imagemagick

# PNG 转 ICO (Windows)
convert icon.png -define icon:auto-resize=256,128,64,48,32,16 icon.ico

# PNG 转 ICNS (macOS)
mkdir icon.iconset
sips -z 16 16 icon.png --out icon.iconset/icon_16x16.png
sips -z 32 32 icon.png --out icon.iconset/<EMAIL>
sips -z 32 32 icon.png --out icon.iconset/icon_32x32.png
sips -z 64 64 icon.png --out icon.iconset/<EMAIL>
sips -z 128 128 icon.png --out icon.iconset/icon_128x128.png
sips -z 256 256 icon.png --out icon.iconset/<EMAIL>
sips -z 256 256 icon.png --out icon.iconset/icon_256x256.png
sips -z 512 512 icon.png --out icon.iconset/<EMAIL>
sips -z 512 512 icon.png --out icon.iconset/icon_512x512.png
sips -z 1024 1024 icon.png --out icon.iconset/<EMAIL>
iconutil -c icns icon.iconset
```

## ⚙️ Electron Builder 配置

### package.json 配置示例
```json
{
  "build": {
    "appId": "com.meea.viofo",
    "productName": "MEEA VIOFO",
    "directories": {
      "buildResources": "build"
    },
    "mac": {
      "icon": "build/icons/icon.icns",
      "category": "public.app-category.video"
    },
    "win": {
      "icon": "build/icons/icon.ico",
      "target": "nsis"
    },
    "linux": {
      "icon": "build/icons/icon.png",
      "category": "AudioVideo"
    }
  }
}
```

## 🔍 验证图标

### 开发环境
- 开发环境下图标可能不显示，这是正常现象
- 图标主要在打包后的应用中显示

### 生产环境
- Windows: 检查 .exe 文件属性中的图标
- macOS: 检查 .app 文件在 Finder 中的图标
- Linux: 检查 .AppImage 文件的图标

## 🔧 常见问题修复

### electron-builder 26.x 配置问题
- ❌ `publisherName` 属性已废弃，需要移除
- ✅ 使用 `copyright` 字段替代发布者信息
- ✅ 图标路径使用相对于 `buildResources` 的路径

### 当前工作配置 (已修复)
```json
{
  "build": {
    "appId": "com.meea.viofo",
    "productName": "MEEA VIOFO",
    "copyright": "Copyright © 2024 MEEA",
    "directories": {
      "output": "release",
      "buildResources": "build"
    },
    "mac": {
      "icon": "icons/icon.icns"
    },
    "win": {
      "icon": "icons/icon.ico",
      "verifyUpdateCodeSignature": false
    },
    "linux": {
      "icon": "icons/icon.png"
    },
    "nsis": {
      "oneClick": false,
      "allowElevation": true,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "MEEA VIOFO"
    }
  }
}
```

## 📋 检查清单

- [x] 图标文件放置在正确位置 (`build/icons/`)
- [x] 图标尺寸符合平台要求
- [x] 图标格式正确 (PNG, ICO, ICNS)
- [x] package.json 配置正确 (移除废弃属性)
- [x] 构建配置兼容 electron-builder 26.x
- [ ] 构建测试通过
- [ ] 各平台图标显示正常

## 🚀 快速使用图标

### 当前状态 ✅
所有图标文件已在本地准备完成，无需额外生成：

```bash
# 检查图标文件状态
./scripts/copy-icons.sh

# 直接构建应用
yarn build:electron
```

### 图标文件详情
```
build/icons/
├── icon.png      # Linux (512x512, 36KB)
├── icon.ico      # Windows (多尺寸, 280KB)
├── icon.icns     # macOS (多尺寸, 196KB)
└── <EMAIL>   # 高分辨率 (1024x1024, 80KB)
```

### 重新生成图标（如需要）
```bash
# 完整重新生成（需要 macOS 环境）
./scripts/generate-icons.sh

# 或使用 Node.js 跨平台生成
node scripts/generate-icons-node.js
```

### CI/CD 使用
GitHub Actions 会自动使用预生成的图标文件，无需额外配置。
