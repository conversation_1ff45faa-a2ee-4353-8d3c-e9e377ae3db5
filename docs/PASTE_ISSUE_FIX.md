# 高德地图配置页面粘贴功能修复

## 问题描述
用户反馈在高德地图配置页面的App Key输入框中无法使用 Ctrl+V 或 Cmd+V 进行粘贴操作。

## 修复方案

### 1. 简化输入框配置
移除了所有可能干扰原生粘贴行为的自定义事件处理器，让输入框使用浏览器的原生粘贴功能。

### 2. 添加调试信息
临时添加了事件监听器来确认粘贴和键盘事件是否正常触发：

```typescript
onPaste={(e) => {
  console.log('粘贴事件被触发');
  // 不阻止默认行为，让浏览器正常处理粘贴
}}
onKeyDown={(e) => {
  console.log('按键事件:', e.key, e.ctrlKey, e.metaKey);
  // 不阻止任何默认行为
}}
```

### 3. 确保输入框属性正确
- 移除了可能影响粘贴的属性如 `autoComplete`、`spellCheck`
- 确保没有 `readOnly` 或 `disabled` 属性
- 保持基本的样式属性

### 4. 添加用户提示
在输入框下方添加了提示文字，告知用户支持的粘贴快捷键。

## 当前输入框配置

```typescript
<Input
  ref={inputRef}
  value={apiKey}
  onChange={(e) => setApiKey(e.target.value)}
  onPaste={(e) => {
    console.log('粘贴事件被触发');
    // 不阻止默认行为，让浏览器正常处理粘贴
  }}
  onKeyDown={(e) => {
    console.log('按键事件:', e.key, e.ctrlKey, e.metaKey);
    // 不阻止任何默认行为
  }}
  placeholder="例如：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
  size="md"
  bg="white"
  borderColor="gray.300"
  _hover={{ borderColor: "gray.400" }}
  _focus={{ borderColor: "blue.500", boxShadow: "0 0 0 1px #3182ce" }}
/>
```

## 测试步骤

1. **准备测试数据**：
   - 复制一个测试的App Key到剪贴板
   - 例如：`a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`

2. **打开配置页面**：
   - 启动应用
   - 点击地图区域的设置按钮打开配置弹窗

3. **测试粘贴功能**：
   - 点击输入框使其获得焦点
   - 使用 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac) 粘贴
   - 检查控制台是否有调试信息输出
   - 确认内容是否正确粘贴到输入框

4. **测试其他功能**：
   - 测试手动输入是否正常
   - 测试清空按钮是否正常工作
   - 测试保存功能是否正常

## 可能的问题原因

如果粘贴仍然不工作，可能的原因包括：

1. **Electron安全策略**：
   - 检查 `webSecurity` 设置
   - 检查 `contextIsolation` 配置

2. **操作系统权限**：
   - macOS可能需要剪贴板访问权限
   - 检查系统安全设置

3. **浏览器兼容性**：
   - 某些Electron版本可能有剪贴板API的问题
   - 检查Electron版本兼容性

4. **CSS样式干扰**：
   - 检查是否有全局CSS影响输入框
   - 检查 `user-select` 或 `pointer-events` 属性

## 后续优化

测试确认粘贴功能正常后，可以：

1. 移除调试用的 `console.log` 语句
2. 根据需要添加回键盘快捷键支持（Enter保存、Escape取消）
3. 优化用户体验和错误处理
