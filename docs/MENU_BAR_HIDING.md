# 菜单栏配置实现

## 概述

本文档描述了如何配置应用程序的菜单栏，保留原生标题栏但隐藏功能菜单（File、Edit 等），只保留应用程序菜单。

## 实现方案

### 1. 窗口创建时的设置

在创建 BrowserWindow 时，保留标题栏：

```javascript
const window = new BrowserWindow({
  // 其他配置...
  titleBarStyle: 'default',   // 保留原生标题栏
  autoHideMenuBar: false      // 不自动隐藏菜单栏，保留空白标题栏
});
```

### 2. 最小应用菜单设置

创建只包含应用程序菜单的最小菜单栏：

```javascript
function createMinimalAppMenu() {
  const template = [];

  if (process.platform === 'darwin') {
    // macOS: 只保留应用程序菜单
    template.push({
      label: app.getName(),
      submenu: [
        { label: '关于 ' + app.getName(), role: 'about' },
        { type: 'separator' },
        { label: '隐藏 ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
        { label: '隐藏其他', accelerator: 'Command+Shift+H', role: 'hideothers' },
        { label: '显示全部', role: 'unhide' },
        { type: 'separator' },
        { label: '退出', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    });
  }
  // Windows/Linux: 空菜单模板，只保留标题栏

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}
```

### 3. 应用启动时设置

在应用启动时创建最小菜单：

```javascript
app.whenReady().then(() => {
  createMinimalAppMenu();
});
```

## 修改的文件

### electron/main.js

1. **主窗口 (createWindow)**
   - 添加 `menuBarVisible: false` 配置
   - 在窗口创建后调用 `mainWindow.setMenuBarVisibility(false)`

2. **日志查看器窗口 (createLogViewerWindow)**
   - 添加 `autoHideMenuBar: true` 和 `menuBarVisible: false` 配置
   - 在窗口创建后调用 `logViewerWindow.setMenuBarVisibility(false)`

3. **图片预览窗口 (createImagePreviewWindow)**
   - 添加 `menuBarVisible: false` 配置
   - 在窗口创建后调用 `imagePreviewWindow.setMenuBarVisibility(false)`

4. **应用菜单设置**
   - 移除了调试模式下的菜单创建逻辑
   - 在所有情况下都设置 `Menu.setApplicationMenu(null)`

### electron/main-production.js

1. **主窗口配置**
   - 将 `autoHideMenuBar: IS_PRODUCTION` 改为 `autoHideMenuBar: true`
   - 添加 `menuBarVisible: false` 配置
   - 在窗口创建后调用 `mainWindow.setMenuBarVisibility(false)`

2. **应用菜单设置**
   - 移除了条件判断，始终设置 `Menu.setApplicationMenu(null)`

## 平台兼容性

### Windows
- `autoHideMenuBar: true` - 隐藏菜单栏但保留 Alt 键激活功能
- `menuBarVisible: false` - 完全禁用菜单栏显示
- `Menu.setApplicationMenu(null)` - 移除应用级菜单

### macOS
- `Menu.setApplicationMenu(null)` - 移除应用菜单栏中的 File、Edit 等菜单
- `setMenuBarVisibility(false)` - 确保窗口级菜单栏不显示

### Linux
- 与 Windows 类似的行为
- `autoHideMenuBar` 和 `menuBarVisible` 配置生效

## 测试

创建了测试脚本 `scripts/test-menu-hiding.js` 来验证菜单栏隐藏功能：

```bash
node scripts/test-menu-hiding.js
```

测试内容：
- 验证窗口菜单栏可见性
- 检查应用菜单是否被移除
- 确认在所有平台上的一致性

## 效果

实施这些更改后：

1. ✅ 所有平台上的 File 菜单完全隐藏
2. ✅ 所有平台上的 Edit 菜单完全隐藏
3. ✅ 其他默认菜单项（View、Window 等）也被隐藏
4. ✅ 保持窗口标题栏和控制按钮的正常功能
5. ✅ 不影响应用的其他功能

## 注意事项

1. **开发调试**: 如果需要在开发过程中访问开发者工具，可以通过快捷键或代码方式打开
2. **用户体验**: 隐藏菜单栏后，确保所有必要功能都能通过应用内的界面访问
3. **平台差异**: 不同平台的菜单栏行为可能略有差异，但通过多重设置确保一致性

## 回滚方案

如果需要恢复菜单栏，可以：

1. 移除 `menuBarVisible: false` 配置
2. 将 `autoHideMenuBar` 设置为 `false`
3. 恢复 `Menu.setApplicationMenu()` 的菜单模板
4. 移除 `setMenuBarVisibility(false)` 调用
