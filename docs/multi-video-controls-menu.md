# 多视频控制菜单优化

## 概述

为了解决多视频播放模式下控制栏按钮过多导致的遮挡问题，我们实现了一个二级菜单系统，将剪辑和截图功能整合到一个下拉菜单中。

## 功能特性

### 1. 智能菜单定位
- 菜单会根据触发按钮的位置自动调整显示位置
- 防止菜单超出屏幕边界
- 支持上方和下方两种显示模式

### 2. 用户交互优化
- 点击外部区域自动关闭菜单
- 支持 ESC 键快速关闭菜单
- 操作完成后自动关闭菜单

### 3. 加载状态指示
- 截图和保存剪辑操作显示加载状态
- 防止重复点击
- 操作失败时也会正确关闭菜单

### 4. 视觉设计
- 半透明背景配合模糊效果
- 与现有控制栏风格保持一致
- 清晰的图标和文字标识

## 组件结构

### MultiVideoControlsMenu
新增的菜单组件，包含以下功能：
- 多视频截图
- 剪辑模式切换
- 保存剪辑
- 剪辑时长显示

### VideoPlayerControls 更新
- 在多视频模式下使用新的菜单组件
- 在单视频模式下保持原有的直接按钮布局
- 通过 `isMultiVideo` 属性自动切换显示模式

## 使用方式

### 多视频模式
1. 在多视频播放界面，控制栏右侧会显示一个"更多操作"按钮（三个点图标）
2. 点击按钮打开下拉菜单
3. 选择需要的操作：
   - **多视频截图**：同时截取所有视频的当前帧
   - **进入剪辑模式**：开启剪辑功能，可以设置剪辑范围
   - **保存剪辑**：在剪辑模式下保存设置的剪辑片段

### 单视频模式
- 保持原有的直接按钮布局
- 所有功能按钮直接显示在控制栏上

## 技术实现

### 关键特性
1. **动态定位算法**：根据按钮位置和屏幕尺寸计算最佳菜单位置
2. **Portal 渲染**：使用 Chakra UI 的 Portal 组件确保菜单在正确的层级显示
3. **事件处理**：监听点击外部和键盘事件，提供良好的用户体验
4. **状态管理**：统一管理菜单开关、加载状态和位置信息

### 代码组织
- `MultiVideoControlsMenu.tsx`：新的菜单组件
- `VideoPlayerControls.tsx`：更新的控制栏组件，支持条件渲染
- 保持向后兼容，不影响现有的单视频播放功能

## 优势

1. **空间优化**：有效解决控制栏空间不足的问题
2. **用户体验**：保持功能完整性的同时简化界面
3. **可扩展性**：未来可以轻松添加更多功能到菜单中
4. **兼容性**：不影响现有的单视频播放体验

## 未来改进

1. 可以考虑添加更多快捷键支持
2. 支持自定义菜单项顺序
3. 添加菜单项的工具提示
4. 支持菜单项的分组显示
