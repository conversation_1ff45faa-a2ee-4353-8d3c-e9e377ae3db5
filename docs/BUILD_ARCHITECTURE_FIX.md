# 构建架构问题修复

## 🐛 问题描述

**问题**: 使用 `yarn build:windows-x64` 命令构建时，产物中包含了 ARM64 版本的文件，这是不正确的。

**原因**: electron-builder 的配置中，Windows 平台的 `extraResources` 同时包含了 x64 和 ARM64 的资源文件，导致即使指定了 `--x64` 参数，构建产物中仍然包含 ARM64 的资源。

## 🔧 解决方案

### 1. 创建专门的构建配置文件

为不同架构创建独立的 electron-builder 配置文件：

- **`electron-builder-win-x64.json`** - Windows x64 专用配置
- **`electron-builder-win-arm64.json`** - Windows ARM64 专用配置

### 2. 修改构建命令

**修改前**:
```json
"build:windows-x64": "yarn predist && yarn build && electron-builder --win --x64 --publish=never"
```

**修改后**:
```json
"build:windows-x64": "yarn predist && yarn build && electron-builder --win --x64 --config=electron-builder-win-x64.json --publish=never"
```

### 3. 配置文件内容

#### Windows x64 配置 (`electron-builder-win-x64.json`)

```json
{
  "extends": "./package.json",
  "win": {
    "files": [
      "!ffmpeg/mac-*/**/*",
      "!ffmpeg/linux-*/**/*",
      "!ffmpeg/win-arm64/**/*"  // 排除 ARM64 资源
    ],
    "extraResources": [
      {
        "from": "ffmpeg/win-x64",  // 只包含 x64 FFmpeg
        "to": "ffmpeg/win-x64"
      }
      // 不包含 win-arm64 资源
    ],
    "target": [
      {
        "target": "nsis",
        "arch": ["x64"]  // 明确指定只构建 x64
      }
    ]
  },
  "nsis": {
    "artifactName": "${productName}-Setup-${version}-win32-x64.${ext}"
  }
}
```

#### Windows ARM64 配置 (`electron-builder-win-arm64.json`)

```json
{
  "extends": "./package.json",
  "win": {
    "files": [
      "!ffmpeg/mac-*/**/*",
      "!ffmpeg/linux-*/**/*",
      "!ffmpeg/win-x64/**/*"  // 排除 x64 资源
    ],
    "extraResources": [
      {
        "from": "ffmpeg/win-arm64",  // 只包含 ARM64 FFmpeg
        "to": "ffmpeg/win-arm64"
      }
      // 不包含 win-x64 资源
    ],
    "target": [
      {
        "target": "nsis",
        "arch": ["arm64"]  // 明确指定只构建 ARM64
      }
    ]
  },
  "nsis": {
    "artifactName": "${productName}-Setup-${version}-win32-arm64.${ext}"
  }
}
```

## ✅ 修复结果

### 现在的构建行为

| 命令 | 配置文件 | 包含的资源 | 产物文件名 |
|------|----------|------------|------------|
| `yarn build:windows-x64` | `electron-builder-win-x64.json` | 仅 x64 资源 | `MEEA-VIOFO-Setup-{version}-win32-x64.exe` |
| `yarn build:windows-arm64` | `electron-builder-win-arm64.json` | 仅 ARM64 资源 | `MEEA-VIOFO-Setup-{version}-win32-arm64.exe` |
| `yarn build:windows` | `package.json` (默认) | x64 + ARM64 资源 | 两个安装包 |

### 验证方法

构建完成后，检查产物：

```bash
# 构建 x64 版本
yarn build:windows-x64

# 检查构建产物
ls -la dist/
# 应该只看到: MEEA-VIOFO-Setup-{version}-win32-x64.exe

# 解压安装包检查内容（可选）
7z l dist/MEEA-VIOFO-Setup-{version}-win32-x64.exe
# 应该只包含 win-x64 相关的文件，不包含 win-arm64
```

## 🔍 技术细节

### 为什么会出现这个问题？

1. **默认配置问题**: package.json 中的 `win` 配置包含了所有架构的资源
2. **electron-builder 行为**: 即使指定了 `--x64` 参数，extraResources 中的所有资源仍然会被包含
3. **文件过滤不足**: 没有明确排除不需要的架构资源

### 解决方案的优势

1. **精确控制**: 每个架构有独立的配置，避免交叉污染
2. **文件大小优化**: 单架构构建产物更小
3. **清晰的命名**: 产物文件名明确标识架构
4. **向后兼容**: 不影响现有的多架构构建命令

## 📋 测试清单

构建后请验证：

- [ ] `yarn build:windows-x64` 只生成 x64 安装包
- [ ] `yarn build:windows-arm64` 只生成 ARM64 安装包
- [ ] 安装包文件名正确标识架构
- [ ] 安装包内容不包含其他架构的资源
- [ ] `yarn build:windows` 仍然生成两个架构的安装包

## 🚀 后续优化

可以考虑为其他平台也创建类似的专门配置：

- `electron-builder-mac-x64.json`
- `electron-builder-mac-arm64.json`
- `electron-builder-linux-x64.json`
- `electron-builder-linux-arm64.json`

这样可以确保所有单架构构建都是精确的，避免类似问题。
