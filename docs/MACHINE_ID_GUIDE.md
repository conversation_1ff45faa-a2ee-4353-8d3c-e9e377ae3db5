# 机器码使用指南

## 什么是机器码？

机器码是基于您的硬件特征生成的唯一标识符，用于软件授权和技术支持。即使重新安装软件，机器码也会保持不变。

## 如何查看机器码？

### 方法一：快捷键（推荐）

在应用运行时，按下以下快捷键组合：

- **Windows/Linux**: `Ctrl + Shift + M`
- **macOS**: `Cmd + Shift + M`

这将弹出一个对话框显示您的机器码，您可以选择复制原始机器码或格式化机器码。

### 方法二：开发模式（仅开发阶段）

如果您在开发模式下运行应用：

1. 在验证码页面底部有"机器码测试"区域
2. 点击"获取机器码"按钮
3. 查看显示的机器码信息

## 机器码格式

机器码有两种显示格式：

- **原始格式**: `EBBE718EB1DFA5AEC9EECDF471EEF2FF`
- **格式化格式**: `EBBE-718E-B1DF-A5AE-C9EE-CDF4-71EE-F2FF`

两种格式都是有效的，您可以根据需要选择使用。

## 技术特性

- **稳定性**: 基于硬件特征生成，重新安装后保持不变
- **唯一性**: 每台设备的机器码都是唯一的
- **跨平台**: 支持 Windows、macOS、Linux
- **安全性**: 使用 SHA256 哈希算法生成
- **无缓存**: 每次启动都重新生成，但结果保持一致

## 硬件特征

机器码基于以下硬件特征生成：

- 操作系统平台和架构
- CPU 型号
- 网络接口 MAC 地址
- 系统内存大小
- 平台特定的硬件标识符：
  - Windows: 主板序列号、系统 UUID
  - macOS: 硬件 UUID
  - Linux: 系统机器 ID

## 注意事项

1. **隐私保护**: 机器码不包含任何个人信息
2. **硬件更换**: 如果更换主要硬件组件，机器码可能会发生变化
3. **技术支持**: 在联系技术支持时，请提供您的机器码
4. **授权验证**: 机器码用于软件授权验证，请妥善保管

## 常见问题

### Q: 机器码会泄露我的隐私信息吗？
A: 不会。机器码是通过哈希算法生成的，不包含任何可识别的个人信息。

### Q: 重新安装系统后机器码会变化吗？
A: 通常不会。机器码基于硬件特征生成，只要硬件不变，机器码就保持不变。

### Q: 如果我更换了硬件，机器码会变化吗？
A: 可能会。如果更换了主要硬件组件（如主板、CPU等），机器码可能会发生变化。

### Q: 我可以自定义机器码吗？
A: 不可以。机器码是自动生成的，无法手动修改，这确保了其唯一性和安全性。

## 技术支持

如果您在使用机器码功能时遇到问题，请联系技术支持并提供：

1. 您的机器码
2. 操作系统版本
3. 应用版本
4. 问题描述

---

*本文档适用于 MEEA-VIOFO 应用程序*
