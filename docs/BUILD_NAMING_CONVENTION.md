# 构建产物命名规则说明

## 🏷️ 为什么是 win32-x64？

### electron-builder 的默认命名规则

在 electron-builder 中，Windows 平台的标识符是 `win32`，这是一个历史遗留的命名规则：

- **`win32`**: Windows 平台标识符（不管是 32 位还是 64 位）
- **`x64`**: 64 位架构标识符
- **`arm64`**: ARM64 架构标识符

### 为什么不是 win64？

1. **Node.js 标准**: Node.js 中 `process.platform` 在 Windows 上返回 `win32`
2. **Electron 继承**: Electron 继承了 Node.js 的平台标识符
3. **Windows API**: Windows API 本身也使用 Win32 作为标识符
4. **历史兼容**: 保持与现有生态系统的兼容性

## 📦 当前的命名规则

### 修改前（默认）
```
MEEA-VIOFO-Setup-25.07.18-1805-win32-x64.exe
MEEA-VIOFO-Setup-25.07.18-1805-win32-arm64.exe
```

### 修改后（自定义）
```
MEEA-VIOFO-Setup-25.07.18-1805-windows-x64.exe
MEEA-VIOFO-Setup-25.07.18-1805-windows-arm64.exe
```

## 🔧 命名规则配置

### 在配置文件中修改

**electron-builder-win-x64.json**:
```json
{
  "nsis": {
    "artifactName": "${productName}-Setup-${version}-windows-x64.${ext}"
  }
}
```

**electron-builder-win-arm64.json**:
```json
{
  "nsis": {
    "artifactName": "${productName}-Setup-${version}-windows-arm64.${ext}"
  }
}
```

### 可用的变量

| 变量 | 说明 | 示例 |
|------|------|------|
| `${productName}` | 产品名称 | `MEEA-VIOFO` |
| `${version}` | 版本号 | `25.07.18-1805` |
| `${os}` | 操作系统 | `win32`, `darwin`, `linux` |
| `${arch}` | 架构 | `x64`, `arm64`, `ia32` |
| `${ext}` | 文件扩展名 | `exe`, `dmg`, `AppImage` |

## 🌍 其他平台的命名

### macOS
```
MEEA-VIOFO-25.07.18-1805-mac-x64.dmg      # Intel Mac
MEEA-VIOFO-25.07.18-1805-mac-arm64.dmg    # Apple Silicon Mac
```

### Linux
```
MEEA-VIOFO-25.07.18-1805-linux-x64.AppImage
MEEA-VIOFO-25.07.18-1805-linux-arm64.AppImage
```

## 📋 推荐的命名策略

### 1. 保持默认（推荐）
```
${productName}-Setup-${version}-${os}-${arch}.${ext}
```
- **优点**: 与生态系统兼容
- **缺点**: win32 可能让用户困惑

### 2. 自定义友好命名
```
${productName}-Setup-${version}-windows-${arch}.${ext}
${productName}-${version}-mac-${arch}.${ext}
${productName}-${version}-linux-${arch}.${ext}
```
- **优点**: 用户友好，清晰明了
- **缺点**: 与标准不一致

### 3. 简化命名
```
${productName}-${version}-${arch}.${ext}
```
- **优点**: 简洁
- **缺点**: 不区分操作系统

## 🎯 我们的选择

我们选择了 **自定义友好命名**：

```
MEEA-VIOFO-Setup-25.07.18-1805-windows-x64.exe
MEEA-VIOFO-Setup-25.07.18-1805-windows-arm64.exe
MEEA-VIOFO-25.07.18-1805-mac-x64.dmg
MEEA-VIOFO-25.07.18-1805-mac-arm64.dmg
MEEA-VIOFO-25.07.18-1805-linux-x64.AppImage
MEEA-VIOFO-25.07.18-1805-linux-arm64.AppImage
```

### 优势
1. **用户友好**: `windows` 比 `win32` 更直观
2. **清晰标识**: 明确区分操作系统和架构
3. **一致性**: 所有平台使用相同的命名模式
4. **专业性**: 符合现代软件的命名习惯

## 🔍 验证命名规则

构建完成后，可以使用验证脚本检查文件名：

```bash
# 验证构建产物
yarn verify:output

# 检查文件名格式
ls -la dist/MEEA-VIOFO-Setup-*-windows-*.exe
```

## 📚 参考资料

- [electron-builder 配置文档](https://www.electron.build/configuration/configuration)
- [Node.js process.platform 文档](https://nodejs.org/api/process.html#process_process_platform)
- [Windows API 命名历史](https://docs.microsoft.com/en-us/windows/win32/)
