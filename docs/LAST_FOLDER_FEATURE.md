# 上次使用文件夹记忆功能

## 功能概述

实现了应用重启后自动记住并加载上次使用的视频文件夹功能。当应用关闭后重新打开时，会自动尝试加载上次使用的视频文件夹。如果文件夹不存在，则清空视频列表，等待用户重新选择。

## 实现细节

### 1. 配置存储机制

在 `electron/main.js` 中实现了配置文件的读写功能：

- **配置文件位置**: `app.getPath('userData')/config.json`
- **默认配置**: 包含 `lastVideoFolder` 和 `windowBounds` 等设置
- **读取配置**: `loadConfig()` 函数，支持配置文件不存在时返回默认配置
- **保存配置**: `saveConfig(config)` 函数，自动创建配置目录

### 2. IPC 通信接口

#### 主进程 (main.js)
- `config:load` - 加载完整配置
- `config:save` - 保存完整配置
- `config:getLastVideoFolder` - 获取上次使用的文件夹路径
- `config:setLastVideoFolder` - 设置上次使用的文件夹路径

#### 预加载脚本 (preload.js)
暴露了以下 API 到渲染进程：
- `window.electronAPI.loadConfig()`
- `window.electronAPI.saveConfig(config)`
- `window.electronAPI.getLastVideoFolder()`
- `window.electronAPI.setLastVideoFolder(folderPath)`

### 3. 前端组件改进

#### VideoFileManager 组件
- **新增 `loadVideoFolder` 函数**: 统一处理文件夹加载逻辑
- **修改 `handleSelectFolder` 函数**: 选择文件夹后自动保存到配置
- **新增 `useEffect` 钩子**: 组件初始化时自动加载上次使用的文件夹

#### 错误处理
- 当文件夹不存在或无法访问时，自动清空配置中的路径
- 清空视频文件列表，等待用户重新选择
- 提供友好的错误日志输出

### 4. TypeScript 类型定义

在 `src/types/electron.d.ts` 中添加了配置管理相关的类型定义：

```typescript
// 配置管理相关API
loadConfig: () => Promise<any>;
saveConfig: (config: any) => Promise<boolean>;
getLastVideoFolder: () => Promise<string | null>;
setLastVideoFolder: (folderPath: string | null) => Promise<boolean>;
```

## 使用流程

### 首次使用
1. 用户启动应用
2. 没有配置文件，显示空的视频列表
3. 用户点击"选择视频文件夹"按钮
4. 选择文件夹后，自动扫描并显示视频文件
5. 文件夹路径保存到配置文件

### 后续使用
1. 用户启动应用
2. 自动读取配置文件中的 `lastVideoFolder`
3. 如果文件夹存在，自动加载视频文件
4. 如果文件夹不存在，清空列表等待用户重新选择

### 文件夹变更
1. 用户选择新的文件夹
2. 新路径自动保存到配置文件
3. 下次启动时使用新路径

## 配置文件结构

```json
{
  "lastVideoFolder": "/path/to/video/folder",
  "windowBounds": {
    "width": 1400,
    "height": 900
  }
}
```

## 技术特点

- **安全性**: 使用 Electron 的 IPC 机制，避免在渲染进程中直接访问文件系统
- **容错性**: 完善的错误处理，文件夹不存在时自动清理配置
- **用户体验**: 无需用户手动操作，自动记忆上次使用状态
- **扩展性**: 配置系统支持添加更多应用设置

## 文件修改清单

1. `electron/main.js` - 添加配置管理功能和 IPC 处理
2. `electron/preload.js` - 暴露配置管理 API
3. `src/types/electron.d.ts` - 添加 TypeScript 类型定义
4. `src/components/VideoFileManager.tsx` - 实现自动加载逻辑
