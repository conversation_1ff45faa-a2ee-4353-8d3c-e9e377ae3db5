# 构建配置总结

## 🎯 简化的构建方案

为了减少用户操作难度，我们提供了简单直观的构建命令：

### ✅ 已配置的构建命令

#### 单平台构建
```bash
yarn build:windows-x64     # Windows x64
yarn build:windows-arm64   # Windows ARM64
yarn build:macos-x64       # macOS Intel
yarn build:macos-arm64     # macOS Apple Silicon
yarn build:linux-x64       # Linux x64
yarn build:linux-arm64     # Linux ARM64
```

#### 平台组合构建
```bash
yarn build:windows         # 所有 Windows 版本 (x64 + ARM64)
yarn build:macos           # 所有 macOS 版本 (x64 + ARM64)
yarn build:linux          # 所有 Linux 版本 (x64 + ARM64)
yarn build:all             # 所有平台和架构
```

### 🛠️ 构建工具

#### 交互式构建工具
```bash
node scripts/build-platforms.js
```

支持的选项：
- `windows-x64`, `windows-arm64`
- `macos-x64`, `macos-arm64`
- `linux-x64`, `linux-arm64`
- `windows`, `macos`, `linux`
- `all`, `x64`, `arm64`

#### 构建配置验证
```bash
yarn verify:build
```

检查所有构建配置是否正确。

### 📦 构建产物

构建完成后，安装包位于 `dist/` 目录：

- **Windows**: `MEEA-VIOFO-Setup-{version}-win32-{arch}.exe`
- **macOS**: `MEEA-VIOFO-{version}-mac-{arch}.dmg`
- **Linux**: `MEEA-VIOFO-{version}-linux-{arch}.AppImage`

### 🚀 推荐使用方式

#### 开发者
```bash
# 1. 验证构建配置
yarn verify:build

# 2. 构建当前平台测试
yarn build:windows-x64  # 或对应平台

# 3. 发布前构建所有平台
yarn build:all
```

#### 用户/贡献者
```bash
# 最简单的方式 - 构建当前平台
yarn build:windows-x64

# 或使用交互式工具
node scripts/build-platforms.js
```

### ⚡ 构建优化

- **并行构建**: 不同平台可以并行构建
- **增量构建**: 只有代码变更时才重新构建
- **缓存优化**: 利用 electron-builder 缓存

### 📋 构建时间参考

- **单平台**: 3-5 分钟
- **全平台**: 15-20 分钟
- **首次构建**: 可能需要额外时间下载依赖

### 🔧 故障排除

#### 构建失败
```bash
# 1. 清理缓存
yarn cleanup

# 2. 重新安装依赖
yarn install

# 3. 验证配置
yarn verify:build

# 4. 重新构建
yarn build:windows-x64
```

#### 跨平台构建限制
- **macOS**: 可以构建所有平台
- **Windows**: 可以构建 Windows 和 Linux
- **Linux**: 可以构建 Windows 和 Linux

### 📚 相关文档

- [BUILD.md](../BUILD.md) - 详细构建说明
- [README.md](../README.md) - 项目总览
- [scripts/build-platforms.js](../scripts/build-platforms.js) - 构建工具源码

## 🎉 总结

通过简化的构建命令和交互式工具，用户可以轻松构建所需的平台版本，无需复杂的配置和脚本操作。
