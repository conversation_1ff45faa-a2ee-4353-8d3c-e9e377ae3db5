# 粘贴功能深度诊断

## 当前状态
- ✅ 能识别到粘贴快捷键（Ctrl+V/Cmd+V）
- ❌ 数据不粘贴到输入框中
- ✅ 移除了 before-input-event 监听器
- ✅ 使用了最基本的原生input

## 问题分析

这种情况通常表明：
1. **键盘事件正常传播**：快捷键被检测到
2. **粘贴事件被阻止或干扰**：数据传输过程中出现问题
3. **可能的原因**：事件监听器在捕获阶段干扰了事件流

## 最新修复

### 1. ✅ 将全局键盘监听器改为冒泡阶段

**问题**：在捕获阶段的事件监听器可能干扰了粘贴事件的正常流程

**修复**：
```typescript
// 之前：使用捕获阶段（true）
document.addEventListener('keydown', handleGlobalKeyDown, true);

// 现在：使用冒泡阶段（false）
document.addEventListener('keydown', handleGlobalKeyDown, false);
```

**原理**：
- **捕获阶段**：事件从document向下传播到目标元素，可能在到达输入框之前就被处理
- **冒泡阶段**：事件从目标元素向上传播到document，输入框先处理事件

### 2. ✅ 添加详细的粘贴事件诊断

```typescript
<input
  onPaste={(e) => {
    console.log('🎯 粘贴事件触发');
    console.log('🎯 clipboardData:', e.clipboardData);
    console.log('🎯 clipboardData.getData("text"):', e.clipboardData?.getData('text'));
    // 不调用 e.preventDefault()，让浏览器处理默认行为
  }}
  onKeyDown={(e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
      console.log('🎯 粘贴快捷键检测到');
    }
  }}
  // ... 其他属性
/>
```

## 测试步骤

### 1. 基础粘贴测试

1. **准备测试数据**：
   ```
   复制这段文本：test-api-key-abc123
   ```

2. **执行粘贴操作**：
   - 打开地图配置页面
   - 点击输入框使其获得焦点
   - 按 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac)

3. **检查控制台日志**：
   应该看到以下日志序列：
   ```
   🔍 在输入框中，完全跳过处理，让原生功能正常工作
   🎯 粘贴快捷键检测到
   🎯 粘贴事件触发
   🎯 clipboardData: [DataTransfer object]
   🎯 clipboardData.getData("text"): test-api-key-abc123
   ```

4. **检查结果**：
   - 输入框中应该显示粘贴的文本
   - 如果没有显示，说明粘贴事件虽然触发了，但数据传输仍有问题

### 2. 右键菜单测试

1. **右键点击输入框**
2. **检查上下文菜单**：
   - 应该显示标准的浏览器上下文菜单
   - 应该包含"粘贴"选项
3. **点击"粘贴"选项**
4. **检查结果**：
   - 应该触发相同的粘贴事件
   - 应该在控制台看到相同的日志

### 3. 帧率控制测试

由于我们将键盘监听器改为了冒泡阶段，需要测试帧率控制是否仍然正常：

1. **加载视频文件**
2. **确保焦点不在输入框中**
3. **测试长按左右箭头键**：
   - 应该看到控制台日志：`🎮 键盘事件: ArrowLeft`
   - 应该看到：`⬅️ 长按检测触发，开始连续后退`
   - 视频应该快速后退/前进

## 可能的结果

### 情况1：粘贴现在正常工作 ✅
如果控制台显示完整的粘贴事件日志，并且输入框中显示了粘贴的内容：
- 问题已解决！是捕获阶段的事件监听器干扰了粘贴
- 可以进行后续优化：移除调试日志，恢复Chakra UI组件等

### 情况2：粘贴事件触发但数据仍不显示 ❌
如果控制台显示粘贴事件触发，clipboardData也有数据，但输入框中仍然没有内容：
- 可能是React的受控组件机制问题
- 可能需要手动处理粘贴数据

### 情况3：粘贴事件根本不触发 ❌
如果连粘贴事件都不触发：
- 可能还有其他地方阻止了事件传播
- 需要进一步检查事件流

### 情况4：帧率控制失效 ❌
如果长按左右键不能快速前进/后退：
- 冒泡阶段的优先级不够
- 可能需要其他解决方案

## 下一步方案

根据测试结果，我们可能需要：

### 如果粘贴正常工作
1. 移除调试日志
2. 恢复必要的快捷键禁用（如F12等）
3. 将原生input替换回Chakra UI组件

### 如果粘贴仍有问题
1. **尝试手动处理粘贴**：
   ```typescript
   onPaste={(e) => {
     e.preventDefault();
     const text = e.clipboardData?.getData('text');
     if (text) {
       setApiKey(text.trim());
     }
   }}
   ```

2. **完全移除全局键盘监听器**：
   将键盘控制移到视频组件级别

3. **使用Electron clipboard API**：
   作为最后的备选方案

请先测试一下现在的修复是否解决了粘贴问题！
