# 调试功能说明

## 🔧 已启用的调试功能

为了便于构建版本的调试和问题排查，我们已经启用了以下调试功能：

### 1. ✅ 开发者工具 (DevTools)

**启用方式**：
- **快捷键**: `F12` 或 `Ctrl+Shift+I` (Windows/Linux) / `Cmd+Option+I` (macOS)
- **右键菜单**: 右键点击页面 → "检查元素"

**功能**：
- Console 控制台
- Elements 元素检查
- Network 网络监控
- Sources 源码调试
- Application 应用数据

### 2. ✅ Console 控制台

**访问方式**：
1. 按 `F12` 打开开发者工具
2. 点击 "Console" 标签页
3. 或直接按 `Ctrl+Shift+J` (Windows/Linux) / `Cmd+Option+J` (macOS)

**用途**：
- 查看应用日志
- 执行JavaScript代码
- 调试错误信息
- 监控性能

### 3. ✅ 右键菜单

**功能**：
- 输入框：复制、粘贴、剪切等
- 页面元素：检查元素、查看源码等
- 图片：另存为、复制图片等

### 4. ✅ 键盘快捷键

所有标准的调试快捷键都已启用：

| 快捷键 | Windows/Linux | macOS | 功能 |
|--------|---------------|-------|------|
| 开发者工具 | `F12` 或 `Ctrl+Shift+I` | `Cmd+Option+I` | 打开开发者工具 |
| 控制台 | `Ctrl+Shift+J` | `Cmd+Option+J` | 直接打开控制台 |
| 刷新页面 | `F5` 或 `Ctrl+R` | `Cmd+R` | 刷新应用 |
| 强制刷新 | `Ctrl+Shift+R` | `Cmd+Shift+R` | 清除缓存并刷新 |

## 🚀 如何使用调试功能

### 查看应用日志
1. 按 `F12` 打开开发者工具
2. 点击 "Console" 标签页
3. 查看应用运行时的日志信息

### 调试JavaScript错误
1. 打开开发者工具
2. 在 "Console" 中查看错误信息
3. 点击错误信息可跳转到源码位置

### 检查网络请求
1. 打开开发者工具
2. 点击 "Network" 标签页
3. 监控API请求和响应

### 检查应用状态
1. 打开开发者工具
2. 点击 "Application" 标签页
3. 查看本地存储、会话存储等

## 🔍 常见调试场景

### 1. 粘贴功能不工作
```javascript
// 在控制台中执行，检查剪贴板权限
navigator.permissions.query({name: 'clipboard-read'}).then(result => {
  console.log('剪贴板读取权限:', result.state);
});
```

### 2. 地图不显示
```javascript
// 检查地图API配置
console.log('地图API Key:', localStorage.getItem('amapApiKey'));
console.log('地图容器:', document.querySelector('#map-container'));
```

### 3. 视频播放问题
```javascript
// 检查视频元素状态
const videos = document.querySelectorAll('video');
videos.forEach((video, index) => {
  console.log(`视频 ${index}:`, {
    src: video.src,
    readyState: video.readyState,
    error: video.error
  });
});
```

### 4. GPS数据问题
```javascript
// 检查GPS数据
console.log('当前GPS数据:', window.currentGPSData);
console.log('GPS轨迹:', window.gpsTrack);
```

## ⚠️ 注意事项

### 性能影响
- 开发者工具会消耗一定的系统资源
- 建议在不需要调试时关闭开发者工具

### 安全考虑
- 不要在控制台中执行不信任的代码
- 敏感信息可能在控制台中可见

### 用户体验
- 普通用户可能不需要这些调试功能
- 可以通过快捷键快速访问

## 🛠️ 开发者专用功能

### 1. 热重载
在开发模式下，代码修改会自动重载应用。

### 2. 源码映射
构建版本包含源码映射，便于调试原始TypeScript代码。

### 3. 详细日志
应用会输出详细的调试日志，包括：
- 文件操作日志
- GPS数据处理日志
- 视频播放状态日志
- 网络请求日志

## 📋 故障排查清单

当遇到问题时，请按以下步骤排查：

1. **打开开发者工具** (`F12`)
2. **查看控制台错误** (Console 标签页)
3. **检查网络请求** (Network 标签页)
4. **查看应用状态** (Application 标签页)
5. **检查元素结构** (Elements 标签页)

## 🔄 如何禁用调试功能

如果需要在生产版本中禁用调试功能，可以修改 `electron/main.js`：

```javascript
// 禁用开发者工具
devTools: false

// 禁用调试快捷键
if (input.key === 'F12' || /* 其他快捷键 */) {
  event.preventDefault();
}

// 禁用右键菜单
mainWindow.webContents.on('context-menu', (event) => {
  event.preventDefault();
});
```

但建议保留这些功能，以便用户和开发者进行问题排查。
