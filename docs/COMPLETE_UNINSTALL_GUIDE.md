# MEEA-VIOFO 完全卸载指南

## 🗑️ 完全卸载步骤

### 方法一：使用Windows控制面板（推荐）

1. **打开控制面板**
   - 按 `Win + R` 键，输入 `appwiz.cpl`，按回车
   - 或者：设置 → 应用 → 应用和功能

2. **找到并卸载MEEA-VIOFO**
   - 在程序列表中找到 "MEEA-VIOFO"
   - 点击"卸载"按钮
   - 按照卸载向导完成卸载

### 方法二：使用安装包卸载

1. **重新运行安装程序**
   - 双击原始的 `MEEA-VIOFO-Setup-*.exe` 安装文件
   - 选择"卸载"选项
   - 按照提示完成卸载

### 方法三：手动完全清理

如果上述方法无法完全卸载，请按以下步骤手动清理：

## 🧹 手动清理步骤

### 1. 停止相关进程

打开任务管理器（`Ctrl + Shift + Esc`），结束以下进程：
- `MEEA-VIOFO.exe`
- `meea-viofo-all.exe`
- 任何包含 "MEEA" 或 "VIOFO" 的进程

### 2. 删除程序文件

删除以下目录（如果存在）：

**默认安装位置：**
```
C:\Program Files\MEEA-VIOFO\
C:\Program Files (x86)\MEEA-VIOFO\
C:\Program Files\Video\MEEA-VIOFO\
C:\Program Files (x86)\Video\MEEA-VIOFO\
C:\Program Files\Video\MEEA-VIOFO-DEBUG\
C:\Program Files (x86)\Video\MEEA-VIOFO-DEBUG\
```

**用户自定义安装位置：**
- 如果您选择了自定义安装路径，请删除相应目录

### 3. 清理用户数据

删除以下用户数据目录：

```
C:\Users\<USER>\AppData\Roaming\MEEA-VIOFO\
C:\Users\<USER>\AppData\Roaming\meea-viofo-all\
C:\Users\<USER>\AppData\Roaming\MEEA-VIOFO-DEBUG\
C:\Users\<USER>\AppData\Local\MEEA-VIOFO\
C:\Users\<USER>\AppData\Local\meea-viofo-all\
C:\Users\<USER>\AppData\Local\MEEA-VIOFO-DEBUG\
```

**包含的数据：**
- 应用配置文件
- 日志文件
- 许可证文件
- 缓存文件
- 用户偏好设置

### 4. 清理注册表

⚠️ **警告：修改注册表有风险，请先备份注册表**

1. **打开注册表编辑器**
   - 按 `Win + R`，输入 `regedit`，按回车

2. **删除以下注册表项**（如果存在）：

```
HKEY_CURRENT_USER\Software\MEEA-VIOFO
HKEY_CURRENT_USER\Software\meea-viofo-all
HKEY_LOCAL_MACHINE\SOFTWARE\MEEA-VIOFO
HKEY_LOCAL_MACHINE\SOFTWARE\meea-viofo-all
HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\MEEA-VIOFO
HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\meea-viofo-all
```

3. **清理卸载信息**

在以下位置查找并删除相关项：
```
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\
HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\
```

查找包含 "MEEA-VIOFO" 或 "meea-viofo-all" 的项目并删除。

### 5. 清理开始菜单和桌面

删除以下快捷方式（如果存在）：

**开始菜单：**
```
C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO\
C:\ProgramData\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO\
```

**桌面：**
```
C:\Users\<USER>\Desktop\MEEA-VIOFO.lnk
C:\Users\<USER>\Desktop\MEEA-VIOFO.lnk
```

### 6. 清理临时文件

删除临时文件：
```
C:\Users\<USER>\AppData\Local\Temp\MEEA-VIOFO*
C:\Users\<USER>\AppData\Local\Temp\meea-viofo*
C:\Windows\Temp\MEEA-VIOFO*
C:\Windows\Temp\meea-viofo*
```

## 🔧 自动化清理脚本

为了方便用户，我们提供了自动化清理脚本：

### PowerShell 清理脚本

创建一个 `.ps1` 文件，内容如下：

```powershell
# MEEA-VIOFO 完全卸载脚本
Write-Host "开始清理 MEEA-VIOFO..." -ForegroundColor Green

# 停止相关进程
Get-Process | Where-Object {$_.ProcessName -like "*MEEA*" -or $_.ProcessName -like "*viofo*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# 删除程序文件
$programPaths = @(
    "${env:ProgramFiles}\MEEA-VIOFO",
    "${env:ProgramFiles(x86)}\MEEA-VIOFO",
    "${env:ProgramFiles}\Video\MEEA-VIOFO",
    "${env:ProgramFiles(x86)}\Video\MEEA-VIOFO",
    "${env:ProgramFiles}\Video\MEEA-VIOFO-DEBUG",
    "${env:ProgramFiles(x86)}\Video\MEEA-VIOFO-DEBUG"
)

foreach ($path in $programPaths) {
    if (Test-Path $path) {
        Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "已删除: $path" -ForegroundColor Yellow
    }
}

# 删除用户数据
$userDataPaths = @(
    "${env:APPDATA}\MEEA-VIOFO",
    "${env:APPDATA}\meea-viofo-all",
    "${env:APPDATA}\MEEA-VIOFO-DEBUG",
    "${env:LOCALAPPDATA}\MEEA-VIOFO",
    "${env:LOCALAPPDATA}\meea-viofo-all",
    "${env:LOCALAPPDATA}\MEEA-VIOFO-DEBUG"
)

foreach ($path in $userDataPaths) {
    if (Test-Path $path) {
        Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "已删除用户数据: $path" -ForegroundColor Yellow
    }
}

# 清理注册表
$regPaths = @(
    "HKCU:\Software\MEEA-VIOFO",
    "HKCU:\Software\meea-viofo-all",
    "HKLM:\SOFTWARE\MEEA-VIOFO",
    "HKLM:\SOFTWARE\meea-viofo-all",
    "HKLM:\SOFTWARE\WOW6432Node\MEEA-VIOFO",
    "HKLM:\SOFTWARE\WOW6432Node\meea-viofo-all"
)

foreach ($regPath in $regPaths) {
    if (Test-Path $regPath) {
        Remove-Item $regPath -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "已删除注册表项: $regPath" -ForegroundColor Yellow
    }
}

Write-Host "MEEA-VIOFO 清理完成！" -ForegroundColor Green
Write-Host "建议重启计算机以确保完全清理。" -ForegroundColor Cyan
```

### 使用方法：

1. 将上述代码保存为 `uninstall-meea-viofo.ps1`
2. 右键点击文件，选择"使用PowerShell运行"
3. 如果提示执行策略错误，请以管理员身份运行PowerShell并执行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

## ✅ 验证卸载完成

卸载完成后，请验证以下内容：

1. **检查程序列表**：控制面板中不再显示MEEA-VIOFO
2. **检查文件系统**：上述目录已被删除
3. **检查进程**：任务管理器中没有相关进程
4. **检查开始菜单**：没有MEEA-VIOFO快捷方式

## 🔄 重新安装

如果需要重新安装MEEA-VIOFO：

1. 确保已完全卸载旧版本
2. 重启计算机
3. 下载最新版本的安装程序
4. 以管理员身份运行安装程序

## 📞 技术支持

如果在卸载过程中遇到问题：

1. **常见问题**：
   - 文件被占用：重启计算机后重试
   - 权限不足：以管理员身份运行清理脚本
   - 注册表访问被拒绝：确保以管理员身份运行

2. **联系支持**：
   - 邮箱：<EMAIL>
   - 提供详细的错误信息和系统环境

## ⚠️ 注意事项

1. **数据备份**：卸载前请备份重要的配置和数据
2. **管理员权限**：某些操作需要管理员权限
3. **防病毒软件**：可能需要临时禁用防病毒软件
4. **系统重启**：建议卸载后重启计算机
5. **注册表风险**：修改注册表前请备份
