# 播放功能更新

## 🎯 新增功能

### 1. 倍数播放功能
**功能**：支持多种播放速度选择
**操作**：点击控制栏中的速度按钮（如 "1x"）循环切换速度

**支持的播放速度**：
- 0.5x（慢速）
- 0.75x（较慢）
- 1x（正常速度）
- 1.25x（稍快）
- 1.5x（较快）
- 2x（快速）

### 2. 可拖动的进度条
**功能**：鼠标拖动进度条精确控制播放位置
**特性**：
- 点击进度条跳转到指定位置
- 拖动进度条实时调整播放时间
- 悬停时显示拖拽点
- 拖动时拖拽点高亮显示

### 3. 优化的时间轴显示
**改进**：
- 使用日期格式显示时间刻度
- 增加左右间距和内边距
- 改进视觉效果和布局
- 更好的悬停交互效果

## 🔧 技术实现

### 倍数播放控制
```typescript
// 播放速度状态
const [playbackRate, setPlaybackRate] = useState(1);

// 播放速度控制
const changePlaybackRate = useCallback((rate: number) => {
  if (!videoRef.current) return;
  
  setPlaybackRate(rate);
  videoRef.current.playbackRate = rate;
}, []);

// 循环播放速度选项
const cyclePlaybackRate = useCallback(() => {
  const rates = [0.5, 0.75, 1, 1.25, 1.5, 2];
  const currentIndex = rates.indexOf(playbackRate);
  const nextIndex = (currentIndex + 1) % rates.length;
  changePlaybackRate(rates[nextIndex]);
}, [playbackRate, changePlaybackRate]);
```

### 可拖动进度条
```typescript
// 拖动状态
const [isDraggingProgress, setIsDraggingProgress] = useState(false);

// 进度条拖动处理
const handleProgressMouseDown = useCallback((e: React.MouseEvent) => {
  setIsDraggingProgress(true);
  const rect = e.currentTarget.getBoundingClientRect();
  const clickX = e.clientX - rect.left;
  const percentage = clickX / rect.width;
  seekTo(percentage * duration);
}, [duration, seekTo]);

const handleProgressMouseMove = useCallback((e: React.MouseEvent) => {
  if (!isDraggingProgress) return;
  
  const rect = e.currentTarget.getBoundingClientRect();
  const clickX = e.clientX - rect.left;
  const percentage = Math.max(0, Math.min(1, clickX / rect.width));
  seekTo(percentage * duration);
}, [isDraggingProgress, duration, seekTo]);
```

### 时间轴日期显示
```typescript
// 时间刻度显示
<Text fontSize="xs" color="gray.600" mt={1} whiteSpace="nowrap" fontWeight="medium">
  {i === 0 || i === tickCount ? 
    time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) :
    time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
</Text>
```

## 🎨 界面改进

### 播放控制栏
```
┌─────────────────────────────────────────────────────────┐
│ ⏮️ ▶️ ⏭️  00:00/05:23  🔊 ────●──  1x  🔍           │
│                                      ↑                  │
│                                   新增速度按钮           │
└─────────────────────────────────────────────────────────┘
```

### 可拖动进度条
- **视觉改进**：进度条高度增加到 6px
- **交互改进**：悬停时显示拖拽点
- **拖动反馈**：拖动时拖拽点放大并高亮
- **平滑动画**：所有状态变化都有过渡动画

### 优化的时间轴
```
┌─────────────────────────────────────────────────────────┐
│  ┌─────────────────────────────────────────────────┐    │
│  │ 04/12    10:30    11:00    11:30    04/12      │    │
│  │ ████████████████████████████████████████████████ │    │
│  │ │video_001_F.mp4│video_002_R.mp4│video_003_I.mp4│ │    │
│  └─────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
```

## 🎮 使用方法

### 倍数播放
1. **切换速度**：点击控制栏中的速度按钮（显示当前速度如 "1x"）
2. **循环选择**：每次点击切换到下一个速度档位
3. **速度显示**：按钮上实时显示当前播放速度

### 进度条拖动
1. **点击跳转**：点击进度条任意位置跳转到对应时间
2. **拖动控制**：
   - 鼠标悬停在进度条上显示拖拽点
   - 按住拖拽点并移动鼠标调整播放位置
   - 松开鼠标完成拖动操作
3. **实时反馈**：拖动过程中视频时间实时更新

### 时间轴导航
1. **日期显示**：两端显示日期（月/日格式）
2. **时间刻度**：中间显示具体时间（时:分格式）
3. **视觉间距**：左右有适当间距，整体布局更美观

## 📋 功能特点

### 播放速度控制
- ✅ **6档速度**：从 0.5x 到 2x 的完整速度范围
- ✅ **循环切换**：点击按钮循环选择速度
- ✅ **实时显示**：按钮显示当前播放速度
- ✅ **即时生效**：速度变化立即应用到视频播放

### 进度条交互
- ✅ **精确控制**：拖动进度条精确定位播放时间
- ✅ **视觉反馈**：悬停和拖动时的视觉提示
- ✅ **平滑操作**：流畅的拖动体验
- ✅ **兼容性**：同时支持点击和拖动操作

### 时间轴优化
- ✅ **日期格式**：使用本地化的日期时间格式
- ✅ **智能间隔**：根据时间跨度自动调整刻度密度
- ✅ **美观布局**：圆角、阴影、间距等视觉优化
- ✅ **清晰标识**：字体加粗、颜色区分等提高可读性

## 🚀 下一步优化建议

### 播放功能
- [ ] 添加播放速度快捷键（如数字键 1-6）
- [ ] 支持自定义播放速度
- [ ] 添加播放速度记忆功能

### 进度条功能
- [ ] 添加预览缩略图（悬停时显示对应时间的视频帧）
- [ ] 支持键盘方向键精确调整
- [ ] 添加章节标记功能

### 时间轴功能
- [ ] 支持缩放功能（放大/缩小时间范围）
- [ ] 添加时间范围选择功能
- [ ] 支持书签和标记功能

所有新功能已实现并可正常使用！
