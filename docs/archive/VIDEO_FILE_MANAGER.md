# 视频文件管理功能说明

## 功能概述

已成功实现了完整的视频文件管理功能，包括文件夹选择、递归扫描和文件显示。

## 🎯 主要功能

### 1. **文件夹选择**
- 点击"选择视频文件夹"按钮
- 使用系统原生文件夹选择对话框
- 显示当前选择的文件夹路径

### 2. **递归文件扫描**
- 自动扫描选择文件夹及其所有子文件夹
- 支持多种视频格式：`.mp4`, `.avi`, `.mov`, `.mkv`, `.wmv`, `.flv`, `.webm`, `.m4v`
- 按文件名自动排序

### 3. **文件信息显示**
- **文件名**：完整的视频文件名
- **时间戳**：文件修改时间（格式化为本地时间）
- **文件大小**：自动格式化（B, KB, MB, GB）
- **摄像头位置**：根据文件名最后一个字符识别
  - `F` → 前置摄像头
  - `R` → 后置摄像头  
  - `I` → 内置摄像头
- **文件夹路径**：显示文件所在的相对路径

### 4. **搜索功能**
- 实时搜索视频文件名
- 不区分大小写
- 显示匹配结果数量

### 5. **用户体验优化**
- **加载状态**：扫描文件时显示加载动画
- **文件计数**：显示当前文件数量徽章
- **空状态处理**：不同情况下的友好提示
- **悬停效果**：文件项交互反馈
- **独立滚动**：文件列表区域独立滚动

## 🔧 技术实现

### Electron 主进程 (main.js)
```javascript
// 文件夹选择对话框
ipcMain.handle('dialog:selectFolder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: '选择视频文件夹'
  });
  return result.filePaths[0] || null;
});

// 递归扫描视频文件
ipcMain.handle('fs:scanVideoFiles', async (event, folderPath) => {
  // 递归扫描所有子文件夹
  // 过滤视频文件格式
  // 返回文件信息数组
});
```

### 预加载脚本 (preload.js)
```javascript
contextBridge.exposeInMainWorld('electronAPI', {
  selectFolder: () => ipcRenderer.invoke('dialog:selectFolder'),
  scanVideoFiles: (folderPath) => ipcRenderer.invoke('fs:scanVideoFiles', folderPath),
  getFileInfo: (filePath) => ipcRenderer.invoke('fs:getFileInfo', filePath)
});
```

### React 组件 (VideoFileManager.tsx)
```typescript
const handleSelectFolder = async () => {
  const folderPath = await window.electronAPI.selectFolder();
  if (folderPath) {
    setSelectedFolder(folderPath);
    const files = await window.electronAPI.scanVideoFiles(folderPath);
    setVideoFiles(files);
  }
};
```

## 📁 支持的文件格式

- **MP4** (.mp4) - 最常见的视频格式
- **AVI** (.avi) - 传统视频格式
- **MOV** (.mov) - Apple QuickTime 格式
- **MKV** (.mkv) - 开源容器格式
- **WMV** (.wmv) - Windows Media 格式
- **FLV** (.flv) - Flash 视频格式
- **WebM** (.webm) - Web 优化格式
- **M4V** (.m4v) - iTunes 视频格式

## 🎨 界面特点

### 布局结构
```
┌─────────────────────────────┐
│  [选择视频文件夹] 按钮        │
│  当前文件夹路径              │
├─────────────────────────────┤
│  🔍 搜索框                  │
├─────────────────────────────┤
│  视频文件列表 [数量徽章]      │
│  ┌─────────────────────────┐ │
│  │ 📹 video_001_F.mp4 [前] │ │
│  │ 2024-07-10 10:30:15    │ │
│  │ 125.6 MB               │ │
│  │ 📁 subfolder           │ │
│  └─────────────────────────┘ │
│  ... (可滚动列表)           │
└─────────────────────────────┘
```

### 视觉设计
- **清晰分层**：按钮、搜索、列表三个区域
- **信息丰富**：文件名、时间、大小、位置一目了然
- **状态反馈**：加载、空状态、错误状态
- **交互友好**：悬停效果、点击反馈

## 🚀 使用方法

1. **启动应用**：`yarn dev`
2. **选择文件夹**：点击"选择视频文件夹"按钮
3. **等待扫描**：系统会自动扫描所有子文件夹
4. **查看文件**：在列表中查看所有视频文件
5. **搜索文件**：使用搜索框快速查找特定文件

## 📋 下一步功能建议

- [ ] 文件预览缩略图
- [ ] 文件分组（按日期/摄像头位置）
- [ ] 文件排序选项
- [ ] 文件详细信息面板
- [ ] 文件操作（重命名、删除、移动）
- [ ] 导入/导出功能

功能已完全实现并可正常使用！
