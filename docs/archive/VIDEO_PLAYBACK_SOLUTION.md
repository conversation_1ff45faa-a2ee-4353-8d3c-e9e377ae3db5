# 视频播放问题解决方案

## 🎯 问题分析

根据控制台错误信息：
```
Not allowed to load local resource: file:///Volumes/KINGSTON/viofo/RO/2025_0404_130709_01338R.MP4
```

这是典型的浏览器安全限制，阻止直接访问本地文件系统。

## 🔧 已实施的修复

### 1. Electron 安全配置
```javascript
// electron/main.js
webPreferences: {
  nodeIntegration: false,
  contextIsolation: true,
  enableRemoteModule: false,
  preload: path.join(__dirname, 'preload.js'),
  webSecurity: false, // 允许访问本地文件
  allowRunningInsecureContent: true, // 允许不安全内容
  experimentalFeatures: true // 启用实验性功能
}
```

### 2. 内容安全策略 (CSP) 设置
```javascript
// 设置 CSP 以允许本地文件访问
mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
  callback({
    responseHeaders: {
      ...details.responseHeaders,
      'Content-Security-Policy': ['default-src \'self\' \'unsafe-inline\' \'unsafe-eval\' data: file: *']
    }
  });
});
```

### 3. 简化的文件URL处理
```javascript
// 获取视频文件的安全URL
ipcMain.handle('fs:getVideoUrl', async (event, filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      // 直接返回文件协议URL
      return `file://${filePath}`;
    }
    return null;
  } catch (error) {
    console.error('获取视频URL时出错:', error);
    return null;
  }
});
```

### 4. 前端调试信息
```typescript
// 添加调试信息显示
{videoPath && (
  <Box position="absolute" top="10px" left="10px" bg="rgba(0,0,0,0.7)">
    <Text>视频路径: {videoPath}</Text>
  </Box>
)}
```

### 5. 错误处理增强
```typescript
// 视频加载错误处理
const handleError = (e) => {
  setIsLoading(false);
  setHasError(true);
  setErrorMessage(`视频加载失败: ${e.target.error?.message || '未知错误'}`);
  console.error('视频加载错误:', e.target.error);
};
```

## 🚀 测试步骤

### 1. 重新启动应用
```bash
# 清理端口
lsof -ti:5173 | xargs kill -9 2>/dev/null || true

# 启动开发服务器
yarn dev
```

### 2. 测试视频播放
1. **选择文件夹**：点击"选择视频文件夹"
2. **选择视频文件**：点击左侧列表中的视频文件
3. **查看调试信息**：
   - 检查播放器左上角显示的视频路径
   - 查看控制台的日志输出
   - 观察是否还有"Not allowed to load local resource"错误

### 3. 预期结果
- ✅ 视频路径正确显示
- ✅ 控制台输出获取到的视频URL
- ✅ 视频开始播放或显示具体错误信息
- ✅ 不再出现"Not allowed to load local resource"错误

## 🔍 故障排除

### 如果仍然无法播放：

#### 1. 检查文件格式
```bash
# 使用 ffprobe 检查视频信息
ffprobe -v quiet -print_format json -show_format -show_streams your_video.mp4
```

#### 2. 测试简单视频
- 使用标准的 MP4 文件（H.264 + AAC）
- 文件名不包含特殊字符
- 文件路径不包含中文

#### 3. 浏览器兼容性测试
```html
<!-- 在浏览器中直接测试 -->
<video controls>
  <source src="file:///path/to/your/video.mp4" type="video/mp4">
</video>
```

#### 4. Electron 版本兼容性
- 确认 Electron 版本支持当前的安全设置
- 检查是否需要更新 Electron

## 📋 支持的视频格式

| 格式 | 编码 | 状态 | 说明 |
|------|------|------|------|
| MP4 | H.264 + AAC | ✅ 推荐 | 最佳兼容性 |
| MP4 | H.265 + AAC | ⚠️ 部分 | 需要硬件支持 |
| WebM | VP8/VP9 + Vorbis | ✅ 支持 | 开源格式 |
| MOV | H.264 + AAC | ✅ 支持 | Apple 格式 |
| AVI | 各种编码 | ⚠️ 取决于编码 | 传统格式 |

## 🛠️ 备用解决方案

如果上述方法仍然无效，可以考虑：

### 1. 使用 Blob URL
```javascript
// 读取文件为 Blob
const fileBuffer = fs.readFileSync(filePath);
const blob = new Blob([fileBuffer], { type: 'video/mp4' });
const blobUrl = URL.createObjectURL(blob);
```

### 2. 使用 Data URL
```javascript
// 转换为 Base64 Data URL（适用于小文件）
const fileBuffer = fs.readFileSync(filePath);
const base64 = fileBuffer.toString('base64');
const dataUrl = `data:video/mp4;base64,${base64}`;
```

### 3. 临时文件复制
```javascript
// 复制到应用临时目录
const tempDir = app.getPath('temp');
const tempFile = path.join(tempDir, 'current_video.mp4');
fs.copyFileSync(originalPath, tempFile);
```

## 📊 调试检查清单

- [ ] Electron 安全设置已正确配置
- [ ] CSP 策略允许文件访问
- [ ] 视频文件路径正确显示
- [ ] 控制台无"Not allowed to load local resource"错误
- [ ] 视频文件格式受支持
- [ ] 文件权限正确
- [ ] 路径不包含特殊字符

## 🎯 下一步

请重新启动应用并测试视频播放功能。如果问题仍然存在，请提供：

1. **控制台完整错误信息**
2. **显示的视频路径**
3. **测试的视频文件格式**
4. **操作系统版本**

这将帮助我们进一步诊断和解决问题！
