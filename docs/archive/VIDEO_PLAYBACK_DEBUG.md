# 视频播放问题诊断

## 🐛 当前问题

视频文件选择后无法正常播放，但播放按钮已经可以正常显示。

## 🔧 已实施的修复

### 1. 简化视频URL处理
**修改**：移除复杂的自定义协议，直接使用 `file://` 协议
```typescript
// 修改前：使用自定义协议
const videoUrl = await window.electronAPI.getVideoUrl(file.path);

// 修改后：直接使用文件协议
setCurrentVideoUrl(`file://${file.path}`);
```

### 2. 启用本地文件访问
**修改**：在 Electron 主进程中启用 `webSecurity: false`
```javascript
webPreferences: {
  nodeIntegration: false,
  contextIsolation: true,
  enableRemoteModule: false,
  preload: path.join(__dirname, 'preload.js'),
  webSecurity: false // 允许访问本地文件
}
```

### 3. 添加调试信息
**功能**：在视频播放器左上角显示当前视频路径
```typescript
{/* 调试信息 */}
{videoPath && (
  <Box position="absolute" top="10px" left="10px" bg="rgba(0,0,0,0.7)">
    <Text>视频路径: {videoPath}</Text>
  </Box>
)}
```

### 4. 增强错误处理
**功能**：添加视频加载错误的检测和显示
```typescript
const handleError = (e) => {
  setIsLoading(false);
  setHasError(true);
  setErrorMessage(`视频加载失败: ${e.target.error?.message || '未知错误'}`);
  console.error('视频加载错误:', e.target.error);
};
```

## 🔍 诊断步骤

### 1. 检查视频路径
- 在播放器左上角查看显示的视频路径
- 确认路径格式是否正确 (`file:///path/to/video.mp4`)
- 验证文件是否真实存在

### 2. 检查控制台错误
- 打开开发者工具 (F12)
- 查看 Console 标签页中的错误信息
- 注意任何网络请求失败或文件访问错误

### 3. 检查文件格式
- 确认视频文件格式是否受支持
- 推荐格式：MP4 (H.264 编码)
- 避免使用过于新的编码格式

### 4. 检查文件权限
- 确认应用有权限访问选择的文件夹
- 在 macOS 上可能需要授予文件访问权限

## 🎯 可能的问题和解决方案

### 问题1：文件路径编码问题
**症状**：路径包含中文或特殊字符
**解决方案**：
```typescript
// 确保路径正确编码
const encodedPath = encodeURI(file.path);
setCurrentVideoUrl(`file://${encodedPath}`);
```

### 问题2：视频编码不支持
**症状**：文件存在但无法播放
**解决方案**：
- 使用 MP4 格式，H.264 视频编码，AAC 音频编码
- 避免使用 HEVC (H.265) 等新编码

### 问题3：Electron 安全限制
**症状**：控制台显示 CORS 或安全错误
**解决方案**：
- 确认 `webSecurity: false` 已设置
- 检查 CSP (Content Security Policy) 设置

### 问题4：文件路径格式错误
**症状**：路径显示不正确
**解决方案**：
```typescript
// Windows 路径处理
const normalizedPath = file.path.replace(/\\/g, '/');
setCurrentVideoUrl(`file:///${normalizedPath}`);

// macOS/Linux 路径处理
setCurrentVideoUrl(`file://${file.path}`);
```

## 🧪 测试建议

### 1. 使用简单的测试视频
- 创建一个简单的 MP4 文件
- 文件名使用英文，无特殊字符
- 放在简单的路径下（如桌面）

### 2. 检查浏览器兼容性
- 在 Chrome 浏览器中测试相同的视频文件
- 使用 `<video>` 标签直接测试

### 3. 逐步调试
```typescript
// 添加更多调试信息
useEffect(() => {
  if (videoPath) {
    console.log('视频路径:', videoPath);
    console.log('文件是否存在:', /* 检查文件存在性 */);
  }
}, [videoPath]);
```

## 📋 支持的视频格式

| 格式 | 视频编码 | 音频编码 | 兼容性 |
|------|----------|----------|--------|
| MP4 | H.264 | AAC | ✅ 最佳 |
| MP4 | H.265 | AAC | ⚠️ 部分支持 |
| WebM | VP8/VP9 | Vorbis | ✅ 良好 |
| AVI | 各种 | 各种 | ⚠️ 取决于编码 |
| MOV | H.264 | AAC | ✅ 良好 |

## 🔧 临时解决方案

如果问题持续存在，可以尝试以下临时解决方案：

### 1. 使用 HTTP 服务器
```javascript
// 在 Electron 主进程中启动本地 HTTP 服务器
const express = require('express');
const app = express();
app.use('/videos', express.static(videoFolderPath));
app.listen(3000);

// 然后使用 http://localhost:3000/videos/filename.mp4
```

### 2. 复制文件到应用目录
```javascript
// 将视频文件复制到应用的临时目录
const tempPath = path.join(app.getPath('temp'), 'video.mp4');
fs.copyFileSync(originalPath, tempPath);
```

## 🚀 下一步调试

1. **启动应用**：运行 `yarn dev`
2. **选择视频文件**：点击选择文件夹，选择包含视频的文件夹
3. **点击视频文件**：在左侧列表中点击任意视频文件
4. **查看调试信息**：
   - 检查播放器左上角的路径显示
   - 查看控制台的错误信息
   - 观察是否显示错误状态

请按照以上步骤进行调试，并告诉我具体看到的错误信息或异常行为！
