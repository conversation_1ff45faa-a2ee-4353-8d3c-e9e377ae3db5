# FFmpeg 设置指南

## 📋 官方下载地址

基于 https://ffmpeg.org/download.html 的官方推荐

### Windows
**下载地址：** https://www.gyan.dev/ffmpeg/builds/
- 选择 "release builds" 中的 "ffmpeg-release-essentials.zip"
- 解压后复制 `bin/ffmpeg.exe` 和 `bin/ffprobe.exe`

**保存位置：**
```
ffmpeg/win-x64/ffmpeg.exe
ffmpeg/win-x64/ffprobe.exe
ffmpeg/win-arm64/ffmpeg.exe
ffmpeg/win-arm64/ffprobe.exe
```

### macOS
**推荐方法：** 使用系统 Homebrew
```bash
brew install ffmpeg
```

**保存位置：**
```
ffmpeg/mac-x64/ffmpeg
ffmpeg/mac-x64/ffprobe
ffmpeg/mac-arm64/ffmpeg
ffmpeg/mac-arm64/ffprobe
```

**设置命令：**
```bash
cp $(which ffmpeg) ffmpeg/mac-x64/ffmpeg
cp $(which ffprobe) ffmpeg/mac-x64/ffprobe
cp $(which ffmpeg) ffmpeg/mac-arm64/ffmpeg
cp $(which ffprobe) ffmpeg/mac-arm64/ffprobe
chmod +x ffmpeg/mac-*/ffmpeg ffmpeg/mac-*/ffprobe
```

### Linux
**下载地址：**
- x64: https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
- ARM64: https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz

**保存位置：**
```
ffmpeg/linux-x64/ffmpeg
ffmpeg/linux-x64/ffprobe
ffmpeg/linux-arm64/ffmpeg
ffmpeg/linux-arm64/ffprobe
```

**设置命令：**
```bash
# x64
curl -L -o ffmpeg-linux-x64.tar.xz "https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz"
tar -xf ffmpeg-linux-x64.tar.xz
cp ffmpeg-*-amd64-static/ffmpeg ffmpeg/linux-x64/
cp ffmpeg-*-amd64-static/ffprobe ffmpeg/linux-x64/
chmod +x ffmpeg/linux-x64/ffmpeg ffmpeg/linux-x64/ffprobe

# ARM64
curl -L -o ffmpeg-linux-arm64.tar.xz "https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz"
tar -xf ffmpeg-linux-arm64.tar.xz
cp ffmpeg-*-arm64-static/ffmpeg ffmpeg/linux-arm64/
cp ffmpeg-*-arm64-static/ffprobe ffmpeg/linux-arm64/
chmod +x ffmpeg/linux-arm64/ffmpeg ffmpeg/linux-arm64/ffprobe
```

## 🚀 快速设置

### 方法一：自动下载（推荐）

#### 1. 交互式下载（最简单）
```bash
yarn download:ffmpeg:interactive
```
提供友好的命令行界面，引导完成整个下载过程。

#### 2. 高级自动下载（推荐）
```bash
# 设置Python环境
yarn setup:download:env

# 运行高级下载（支持JavaScript渲染）
yarn download:ffmpeg:advanced
```

#### 3. 基础自动下载
```bash
yarn download:ffmpeg
```

#### 4. Selenium浏览器自动化
```bash
# 需要先安装Chrome浏览器
yarn download:ffmpeg:selenium
```

### 方法二：手动设置

#### 1. 清理错误文件
```bash
yarn cleanup:ffmpeg
```

#### 2. 创建目录结构
```bash
mkdir -p ffmpeg/{win-x64,win-arm64,mac-x64,mac-arm64,linux-x64,linux-arm64}
```

#### 3. 设置 macOS 版本（如果系统已安装）
```bash
cp $(which ffmpeg) ffmpeg/mac-x64/ffmpeg
cp $(which ffprobe) ffmpeg/mac-x64/ffprobe
cp $(which ffmpeg) ffmpeg/mac-arm64/ffmpeg
cp $(which ffprobe) ffmpeg/mac-arm64/ffprobe
chmod +x ffmpeg/mac-*/ffmpeg ffmpeg/mac-*/ffprobe
```

#### 4. 手动下载其他平台
按照上述地址下载 Windows 和 Linux 版本

### 验证和测试

#### 1. 验证设置
```bash
yarn test:ffmpeg
```

#### 2. 测试下载环境
```bash
yarn test:download:env
```

#### 3. 测试构建
```bash
yarn version:generate
yarn dist
```

## 📊 预期文件大小

| 平台 | ffmpeg | ffprobe |
|------|--------|---------|
| Windows | ~50MB | ~45MB |
| macOS (Homebrew) | ~400KB | ~300KB |
| Linux (静态) | ~70MB | ~65MB |

## ⚠️ 注意事项

1. **macOS 文件较小** - 因为是符号链接到系统二进制文件
2. **Linux 文件较大** - 因为是静态编译版本
3. **Windows ARM64** - 使用 x64 版本（兼容性更好）
4. **权限设置** - Unix 系统需要执行权限
5. **版本兼容** - 建议使用 FFmpeg 4.4+ 版本

## 🎯 完成后

设置完成后运行：
```bash
# 验证配置
yarn test:ffmpeg

# 生成版本号 (格式: 25.07.18.1115)
yarn version:generate

# 测试构建
yarn dist

# 提交到仓库
git add ffmpeg/
git commit -m "feat: add FFmpeg binaries for all platforms"
git push
```

合并到 master 分支后，CircleCI 将自动构建所有平台并上传到 OSS！
