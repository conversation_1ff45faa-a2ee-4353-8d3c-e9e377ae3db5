# MEEA VIOFO

<div align="center">
  <img src="assets/logo.svg" alt="MEEA VIOFO Logo" width="128" height="128">

  <h3>专业的行车记录仪视频管理工具</h3>

  [![Build Status](https://github.com/your-username/meea-viofo-all/workflows/Build%20and%20Release/badge.svg)](https://github.com/your-username/meea-viofo-all/actions)
  [![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
  [![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)](https://github.com/your-username/meea-viofo-all/releases)
</div>

## 技术栈

- **Electron** - 跨平台桌面应用框架
- **React** - 用户界面库
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速的构建工具
- **Chakra UI** - 现代化的React组件库
- **Framer Motion** - 动画库
- **Yarn** - 包管理器

## 功能特性

- 📁 文件管理 - 选择和管理行车记录仪视频文件
- 🎥 视频播放 - 内置视频播放器
- 🗺️ 地图显示 - 显示行车轨迹和GPS信息
- 📊 GPS信息 - 实时显示位置、速度等信息
- 🎨 现代化UI - 使用Chakra UI构建的美观界面

## 开发环境设置

### 前置要求

- Node.js (推荐 18.x 或更高版本)
- Yarn 包管理器

### 安装依赖

```bash
yarn install
```

### 开发模式

```bash
yarn dev
```

这将同时启动Vite开发服务器和Electron应用。

### 构建发布版本

```bash
# 构建所有平台
yarn build:all

# 单独构建平台
yarn build:windows-x64    # Windows x64
yarn build:macos-arm64     # macOS Apple Silicon
yarn build:linux-x64      # Linux x64

# 构建平台所有架构
yarn build:windows         # Windows (x64 + ARM64)
yarn build:macos           # macOS (x64 + ARM64)
yarn build:linux          # Linux (x64 + ARM64)
```

> 📋 **详细构建说明**: 查看 [BUILD.md](BUILD.md) 了解完整的构建选项和说明

## 项目结构

```
meea-viofo-all/
├── electron/           # Electron主进程文件
│   ├── main.js        # 主进程入口
│   └── preload.js     # 预加载脚本
├── src/               # React源代码
│   ├── App.tsx        # 主应用组件
│   ├── main.tsx       # React入口文件
│   └── theme.ts       # Chakra UI主题配置
├── public/            # 静态资源
├── dist/              # 构建输出目录
├── release/           # 打包输出目录
├── package.json       # 项目配置
├── vite.config.ts     # Vite配置
├── tsconfig.json      # TypeScript配置
└── README.md          # 项目说明
```

## 📚 文档中心

> 📖 **完整文档**: [docs/](docs/) | **文档索引**: [docs/README.md](docs/README.md)

### 📋 快速导航
| 文档 | 描述 | 链接 |
|------|------|------|
| 🚀 **发布指南** | 详细的版本发布流程和最佳实践 | [docs/RELEASE.md](docs/RELEASE.md) |
| 🔑 **Token 设置** | GitHub CI/CD 权限配置步骤 | [docs/GITHUB_TOKEN_SETUP.md](docs/GITHUB_TOKEN_SETUP.md) |
| 🏗️ **项目结构** | 代码组织和架构说明 | [docs/PROJECT_STRUCTURE.md](docs/PROJECT_STRUCTURE.md) |

## 🚀 快速发布

### 发布前准备
1. 确保已设置 [GitHub Token](docs/GITHUB_TOKEN_SETUP.md)
2. 检查代码已提交到主分支

### 快速发布（推荐）
```bash
# 运行发布脚本，自动处理版本更新和标签创建
./scripts/create-release.sh
```

### 手动发布
```bash
# 1. 更新版本号
npm version 1.0.0 --no-git-tag-version
git add package.json
git commit -m "chore: bump version to 1.0.0"

# 2. 创建标签并推送
git tag v1.0.0
git push origin main
git push origin v1.0.0
```

### 自动构建
- 推送版本标签后，GitHub Actions 自动构建所有平台
- 支持 Windows (x64/x86/ARM64)、macOS (Intel/Apple Silicon)、Linux (x64/ARM64)
- 自动创建 GitHub Release 并提供下载链接

> 📖 更多详细信息请查看 [发布指南](docs/RELEASE.md)

## 🛠️ 开发指南

### 添加新功能

1. 在 `src/` 目录下创建新的组件
2. 使用Chakra UI组件构建界面
3. 通过Electron的IPC机制与主进程通信
4. 遵循TypeScript类型安全原则

### 主题定制

编辑 `src/theme.ts` 文件来定制应用主题，包括颜色、字体等。

### Electron集成

- 主进程代码位于 `electron/main.js`
- 预加载脚本位于 `electron/preload.js`
- 通过 `contextBridge` 安全地暴露API给渲染进程

## 许可证

MIT License
