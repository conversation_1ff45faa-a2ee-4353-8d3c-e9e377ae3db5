# Package.json 脚本清理总结

## 🧹 清理前后对比

### 清理前：54个脚本
包含大量重复、过时和不必要的脚本

### 清理后：17个脚本
保留核心功能，删除冗余脚本

## ✅ 保留的核心脚本

### 开发相关
- `dev` - 开发模式（Vite + Electron）
- `dev:vite` - 启动Vite开发服务器
- `dev:electron` - 启动Electron开发模式
- `electron` - 直接启动Electron
- `preview` - Vite预览模式

### 构建相关
- `prebuild` - 构建前准备（版本生成 + 注入）
- `build` - 完整构建流程
- `pack` - 打包到目录（不生成安装包）

### 分发相关
- `predist` - 分发前准备
- `dist` - 构建所有平台
- `dist:all` - 构建所有平台和架构
- `dist:mac` - 构建macOS（x64 + ARM64）
- `dist:win` - 构建Windows（x64 + ARM64）
- `dist:linux` - 构建Linux（x64 + ARM64）

### 维护相关
- `cleanup` - 清理构建空间
- `setup:ffmpeg` - 设置FFmpeg

## ❌ 删除的脚本类别

### 1. 重复的构建脚本
- `build:electron` - 与`dist`重复
- `dist:mac:all` - 与`dist:mac`重复
- `dist:win:all` - 与`dist:win`重复
- `dist:linux:all` - 与`dist:linux`重复

### 2. 版本管理脚本
- `version:get` - 不常用
- `version:update` - 不常用
- `version:generate` - 已集成到prebuild
- `version:preview` - 调试用，不需要

### 3. 自动构建脚本
- `auto-build` - 复杂度高，使用频率低
- `auto-build:win` - 同上
- `auto-build:mac` - 同上
- `auto-build:all` - 同上

### 4. FFmpeg相关脚本
- `setup:ffmpeg:current` - 不常用
- `cleanup:ffmpeg` - 不常用
- `download:ffmpeg*` - 8个下载脚本，过于复杂
- `setup:download:env` - 环境设置，不常用
- `test:download:env` - 测试脚本，不常用
- `test:ffmpeg` - 测试脚本，不常用
- `verify:ffmpeg` - 验证脚本，不常用
- `fix:ffmpeg` - 修复脚本，不常用

### 5. 构建相关脚本
- `check:build` - 检查脚本，不常用
- `build:all` - 与dist重复
- `build:help` - 帮助信息，不常用
- `build:production` - 与dist重复
- `build:prod` - 与dist重复
- `build:dynamic*` - 4个动态构建脚本，复杂度高
- `build:win:debug` - 调试构建，不常用
- `build:windows:debug` - 重复
- `build:win:debug:js` - 重复

### 6. CI相关脚本
- `ci:validate` - CI验证，不常用

## 💡 清理原则

1. **保留核心功能**: 开发、构建、分发的基本流程
2. **删除重复脚本**: 功能相同或相似的脚本
3. **删除调试脚本**: 临时调试和测试用的脚本
4. **删除复杂脚本**: 过于复杂、使用频率低的脚本
5. **简化命名**: 统一命名规范，避免混淆

## 🚀 使用指南

### 日常开发
```bash
yarn dev          # 开发模式
yarn build        # 构建项目
yarn preview      # 预览构建结果
```

### 打包分发
```bash
yarn dist         # 构建所有平台
yarn dist:win     # 只构建Windows
yarn dist:mac     # 只构建macOS
yarn dist:linux   # 只构建Linux
```

### 维护操作
```bash
yarn cleanup      # 清理构建空间
yarn setup:ffmpeg # 设置FFmpeg
```

## 📈 清理效果

- **脚本数量**: 54 → 17 (-68%)
- **维护复杂度**: 大幅降低
- **使用便利性**: 显著提升
- **文档清晰度**: 更加简洁

## 🔄 如需恢复

如果需要恢复某些被删除的脚本，可以：
1. 查看Git历史记录
2. 根据需要重新添加特定脚本
3. 建议先评估是否真的需要

## 📝 建议

1. **定期清理**: 每个版本发布后检查并清理不需要的脚本
2. **文档同步**: 更新README中的脚本使用说明
3. **团队沟通**: 确保团队成员了解脚本变更
