<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1400" height="900" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#333">多视频播放器布局</text>
  
  <!-- 左侧文件管理器 -->
  <rect x="20" y="60" width="280" height="820" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="160" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6c757d">文件管理器</text>
  <text x="160" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">VideoFileManager</text>
  
  <!-- 多选文件示意 -->
  <rect x="40" y="120" width="240" height="25" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="50" y="137" font-family="Arial, sans-serif" font-size="10" fill="#1976d2">✓ 前摄像头.mp4</text>
  
  <rect x="40" y="150" width="240" height="25" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="50" y="167" font-family="Arial, sans-serif" font-size="10" fill="#1976d2">✓ 后摄像头.mp4</text>
  
  <rect x="40" y="180" width="240" height="25" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="50" y="197" font-family="Arial, sans-serif" font-size="10" fill="#1976d2">✓ 内摄像头.mp4</text>
  
  <!-- 右侧主内容区域 -->
  <rect x="320" y="60" width="1060" height="820" fill="#f8f9fa" stroke="#495057" stroke-width="2"/>
  <text x="850" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#495057">主内容区域 (flex: 1, bg: gray.50)</text>
  
  <!-- 上半部分 -->
  <rect x="340" y="100" width="1020" height="580" fill="none" stroke="#007bff" stroke-width="2"/>
  <text x="850" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#007bff">上半部分 (flex: 1, 水平布局)</text>
  
  <!-- 视频播放器容器 -->
  <rect x="360" y="140" width="720" height="520" fill="#ffffff" stroke="#dc3545" stroke-width="2"/>
  <text x="720" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#dc3545">MultiVideoPlayer容器 (flex: 1)</text>
  
  <!-- 视频网格容器 -->
  <rect x="380" y="180" width="680" height="460" fill="#000000" stroke="#ffc107" stroke-width="2"/>
  <text x="720" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">视频网格容器 (Grid布局)</text>
  
  <!-- 3视频布局示例 -->
  <text x="720" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">3视频布局示例</text>
  
  <!-- 后摄像头 (左上) -->
  <rect x="400" y="250" width="320" height="180" fill="none" stroke="#ffffff" stroke-width="2"/>
  <text x="560" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">后摄像头</text>
  <text x="560" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#adb5bd">gridColumn: 1, gridRow: 1</text>
  
  <!-- 内摄像头 (右上) -->
  <rect x="740" y="250" width="320" height="180" fill="none" stroke="#ffffff" stroke-width="2"/>
  <text x="900" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">内摄像头</text>
  <text x="900" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#adb5bd">gridColumn: 2, gridRow: 1</text>
  
  <!-- 前摄像头 (下方整行) -->
  <rect x="400" y="450" width="660" height="170" fill="none" stroke="#ffffff" stroke-width="2"/>
  <text x="730" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#ffffff">前摄像头 (主视频)</text>
  <text x="730" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">gridColumn: 1 / -1, gridRow: 2</text>
  
  <!-- 右侧GPS面板 -->
  <rect x="1100" y="140" width="240" height="520" fill="#ffffff" stroke="#28a745" stroke-width="2"/>
  <text x="1220" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#28a745">GPS面板</text>
  <text x="1220" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#28a745">flex: 0 0 300px</text>
  
  <!-- 地图组件 -->
  <rect x="1120" y="200" width="200" height="150" fill="none" stroke="#17a2b8" stroke-width="1"/>
  <text x="1220" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#17a2b8">地图组件</text>
  <text x="1220" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#6c757d">🗺️</text>
  
  <!-- GPS信息 -->
  <rect x="1120" y="370" width="200" height="270" fill="none" stroke="#6f42c1" stroke-width="1"/>
  <text x="1220" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6f42c1">GPS信息</text>
  <text x="1130" y="410" font-family="Arial, sans-serif" font-size="9" fill="#6c757d">📍 经度: 116.404</text>
  <text x="1130" y="425" font-family="Arial, sans-serif" font-size="9" fill="#6c757d">📍 纬度: 39.915</text>
  <text x="1130" y="440" font-family="Arial, sans-serif" font-size="9" fill="#6c757d">🚗 速度: 60 km/h</text>
  <text x="1130" y="455" font-family="Arial, sans-serif" font-size="9" fill="#6c757d">🧭 方向: 北</text>
  
  <!-- 下半部分控制栏 -->
  <rect x="340" y="700" width="1020" height="60" fill="rgba(0,0,0,0.8)" stroke="#6f42c1" stroke-width="2"/>
  <text x="850" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">统一视频控制栏 (flexShrink: 0)</text>
  
  <!-- 控制栏元素 -->
  <circle cx="370" cy="740" r="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="370" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">▶</text>
  
  <rect x="400" y="735" width="300" height="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="550" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">同步进度条</text>
  
  <text x="750" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">00:05:30 / 15:20</text>
  
  <rect x="850" y="735" width="80" height="15" fill="#007bff" stroke="#ffffff" stroke-width="1"/>
  <text x="890" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">3 个视频</text>
  
  <circle cx="970" cy="740" r="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="970" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">🔊</text>
  
  <circle cx="1020" cy="740" r="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="1020" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">角度</text>
  
  <!-- 布局说明 -->
  <rect x="50" y="780" width="1300" height="80" fill="#fff3cd" stroke="#856404" stroke-width="1"/>
  <text x="70" y="800" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#856404">布局说明:</text>
  <text x="70" y="820" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 1个视频: 全屏显示 (1fr grid)</text>
  <text x="70" y="835" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 2个视频: 主视频下方整行 + 次视频左上角</text>
  <text x="70" y="850" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 3个视频: 上方2个次视频 + 下方1个主视频 (如图所示)</text>
  
  <text x="700" y="820" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 4+个视频: 2x2网格均匀分布</text>
  <text x="700" y="835" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 支持视频点击切换、缩放、拖拽操作</text>
  <text x="700" y="850" font-family="Arial, sans-serif" font-size="11" fill="#856404">• 统一控制栏实现多视频同步播放</text>
</svg>
