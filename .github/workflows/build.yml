name: Build and Release

on:
  push:
    branches: [ main, master ]
    tags: [ '*' ]  # 支持任何标签格式
  workflow_dispatch:

# 自动清理旧的 artifacts 和 workflow runs
env:
  ARTIFACT_RETENTION_DAYS: 7  # 减少到7天

permissions:
  contents: write
  actions: read

jobs:
  # 清理旧的 artifacts 以节省存储空间
  cleanup:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Delete old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;
            
            // 获取所有 workflow runs
            const runs = await github.rest.actions.listWorkflowRunsForRepo({
              owner,
              repo,
              per_page: 100
            });
            
            // 删除超过10个的旧 runs 的 artifacts
            const runsToClean = runs.data.workflow_runs.slice(10);
            
            for (const run of runsToClean) {
              try {
                const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
                  owner,
                  repo,
                  run_id: run.id
                });
            
                for (const artifact of artifacts.data.artifacts) {
                  await github.rest.actions.deleteArtifact({
                    owner,
                    repo,
                    artifact_id: artifact.id
                  });
                  console.log(`Deleted artifact: ${artifact.name}`);
                }
              } catch (error) {
                console.log(`Error cleaning run ${run.id}: ${error.message}`);
              }
            }
            
            console.log(`Cleaned ${runsToClean.length} old workflow runs`);
  build-mac:
    runs-on: macos-latest
    needs: cleanup
    if: always() && !cancelled()
    strategy:
      matrix:
        arch: [x64, arm64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn build

      - name: Build Electron app for macOS ${{ matrix.arch }}
        run: |
          if [ "${{ matrix.arch }}" = "arm64" ]; then
            yarn electron-builder --mac --arm64 --publish=never
          else
            yarn electron-builder --mac --x64 --publish=never
          fi
        env:
          # 禁用代码签名自动发现（因为没有证书）
          CSC_IDENTITY_AUTO_DISCOVERY: false
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Self-sign macOS application
        run: |
          echo "🔐 对 macOS 应用程序进行自签名..."
          ./scripts/ci-self-sign.sh
        continue-on-error: true

      - name: Upload macOS ${{ matrix.arch }} artifacts
        uses: actions/upload-artifact@v4
        with:
          name: meea-viofo-mac-${{ matrix.arch }}-${{ github.sha }}
          path: |
            release/*.dmg
            release/*.zip
          retention-days: ${{ env.ARTIFACT_RETENTION_DAYS }}

  build-windows:
    runs-on: windows-latest
    needs: cleanup
    if: always() && !cancelled()
    strategy:
      matrix:
        arch: [x64, ia32, arm64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn build

      - name: Build Electron app for Windows ${{ matrix.arch }}
        run: |
          if ("${{ matrix.arch }}" -eq "arm64") {
            yarn electron-builder --win --arm64 --publish=never
          } elseif ("${{ matrix.arch }}" -eq "ia32") {
            yarn electron-builder --win --ia32 --publish=never
          } else {
            yarn electron-builder --win --x64 --publish=never
          }
        env:
          CSC_LINK: ''
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Upload Windows ${{ matrix.arch }} artifacts
        uses: actions/upload-artifact@v4
        with:
          name: meea-viofo-win-${{ matrix.arch }}-${{ github.sha }}
          path: |
            release/*.exe
            release/*.msi
          retention-days: ${{ env.ARTIFACT_RETENTION_DAYS }}

  build-linux:
    runs-on: ubuntu-latest
    needs: cleanup
    if: always() && !cancelled()
    strategy:
      matrix:
        arch: [x64, arm64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn build

      - name: Build Electron app for Linux ${{ matrix.arch }}
        run: |
          if [ "${{ matrix.arch }}" = "arm64" ]; then
            yarn electron-builder --linux --arm64 --publish=never
          else
            yarn electron-builder --linux --x64 --publish=never
          fi
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Upload Linux ${{ matrix.arch }} artifacts
        uses: actions/upload-artifact@v4
        with:
          name: meea-viofo-linux-${{ matrix.arch }}-${{ github.sha }}
          path: |
            release/*.AppImage
            release/*.deb
            release/*.rpm
          retention-days: ${{ env.ARTIFACT_RETENTION_DAYS }}

  release:
    needs: [build-mac, build-windows, build-linux]
    runs-on: ubuntu-latest
    if: always() && !cancelled() && !failure()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check GitHub Token
        run: ./scripts/check-github-token.sh
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Organize and deduplicate files
        run: |
          echo "📁 原始 artifacts 结构:"
          find artifacts -type f -name "*.exe" -o -name "*.dmg" -o -name "*.AppImage" | sort
          
          echo ""
          echo "🔄 整理文件..."
          mkdir -p release-files
          
          # 复制所有构建文件到统一目录，避免重复
          find artifacts -type f \( -name "*.exe" -o -name "*.dmg" -o -name "*.AppImage" \) | while read file; do
            filename=$(basename "$file")
            if [ ! -f "release-files/$filename" ]; then
              cp "$file" "release-files/"
              echo "✅ 复制: $filename"
            else
              echo "⚠️  跳过重复文件: $filename"
            fi
          done
          
          echo ""
          echo "📋 最终文件列表:"
          ls -lh release-files/

      - name: Get version from tag or generate timestamp
        id: version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
            IS_TAG=true
          else
            # 从package.json获取版本号，添加时间戳
            PACKAGE_VERSION=$(node -p "require('./package.json').version")
            VERSION="${PACKAGE_VERSION}-dev.$(date +'%Y%m%d.%H%M%S')"
            IS_TAG=false
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "is_tag=$IS_TAG" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION (is_tag: $IS_TAG)"

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: MEEA-VIOFO ${{ steps.version.outputs.version }}
          token: ${{ secrets.GH_TOKEN }}
          files: release-files/*
          draft: false
          prerelease: ${{ steps.version.outputs.is_tag == 'false' }}
          generate_release_notes: true
          fail_on_unmatched_files: false
          body: |
            ## 🚀 MEEA-VIOFO ${{ steps.version.outputs.version }}
            
            ### 📦 下载链接 / Download Links
            
            #### Windows
            - **64位 (x64)**: `MEEA-VIOFO-Setup-${{ steps.version.outputs.version }}-x64.exe`
            - **32位 (ia32)**: `MEEA-VIOFO-Setup-${{ steps.version.outputs.version }}-ia32.exe`
            - **ARM64**: `MEEA-VIOFO-Setup-${{ steps.version.outputs.version }}-arm64.exe`
            
            #### macOS
            - **Intel (x64)**: `MEEA-VIOFO-${{ steps.version.outputs.version }}-x64.dmg`
            - **Apple Silicon (arm64)**: `MEEA-VIOFO-${{ steps.version.outputs.version }}-arm64.dmg`
            
            #### Linux
            - **64位 (x64)**: `MEEA-VIOFO-${{ steps.version.outputs.version }}-x64.AppImage`
            - **ARM64**: `MEEA-VIOFO-${{ steps.version.outputs.version }}-arm64.AppImage`
            
            ### ✨ 功能特性
            - 🎥 高精度视频时间轴，实时画面同步
            - 🗺️ 集成地图显示，GPS数据可视化
            - 📁 智能文件管理，自动分组
            - ⚡ 大文件优化，流畅播放体验
            
            ### 🔧 安装说明
            - **Windows**: 下载对应架构的 `.exe` 文件并运行安装
            - **macOS**: 下载对应架构的 `.dmg` 文件，拖拽到应用程序文件夹
              - ✅ **已预签名**: 应用程序已在构建时自动签名，可直接使用
              - 💡 **首次运行**: 如遇安全提示，右键点击应用选择"打开"
              - 🔧 **备用方案**: 如仍有问题，执行: `sudo xattr -rd com.apple.quarantine ~/Downloads/MEEA-VIOFO-*.dmg`
              - 📖 **详细说明**: [macOS 安装指南](docs/MACOS_INSTALL_GUIDE.md)
            - **Linux**: 下载 `.AppImage` 文件，添加执行权限后直接运行
            
            ---
            
            **完整下载列表请查看下方 Assets 部分**
