name: Cleanup Storage

on:
  workflow_dispatch:
    inputs:
      days_to_keep:
        description: 'Keep artifacts from last N days'
        required: true
        default: '7'
        type: string
      max_runs_to_keep:
        description: 'Maximum number of workflow runs to keep'
        required: true
        default: '10'
        type: string

permissions:
  actions: write
  contents: read

jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
    - name: Cleanup old artifacts and workflow runs
      uses: actions/github-script@v7
      with:
        script: |
          const owner = context.repo.owner;
          const repo = context.repo.repo;
          const daysToKeep = parseInt('${{ github.event.inputs.days_to_keep }}') || 7;
          const maxRunsToKeep = parseInt('${{ github.event.inputs.max_runs_to_keep }}') || 10;
          
          console.log(`Cleaning up artifacts older than ${daysToKeep} days`);
          console.log(`Keeping only the latest ${maxRunsToKeep} workflow runs`);
          
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
          
          // 获取所有 workflow runs
          const runs = await github.rest.actions.listWorkflowRunsForRepo({
            owner,
            repo,
            per_page: 100
          });
          
          let deletedArtifacts = 0;
          let deletedRuns = 0;
          
          // 删除旧的 artifacts
          for (const run of runs.data.workflow_runs) {
            const runDate = new Date(run.created_at);
            
            if (runDate < cutoffDate) {
              try {
                const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
                  owner,
                  repo,
                  run_id: run.id
                });
                
                for (const artifact of artifacts.data.artifacts) {
                  await github.rest.actions.deleteArtifact({
                    owner,
                    repo,
                    artifact_id: artifact.id
                  });
                  deletedArtifacts++;
                  console.log(`Deleted artifact: ${artifact.name} from run ${run.id}`);
                }
              } catch (error) {
                console.log(`Error cleaning artifacts for run ${run.id}: ${error.message}`);
              }
            }
          }
          
          // 删除超过保留数量的旧 workflow runs
          const runsToDelete = runs.data.workflow_runs.slice(maxRunsToKeep);
          
          for (const run of runsToDelete) {
            try {
              await github.rest.actions.deleteWorkflowRun({
                owner,
                repo,
                run_id: run.id
              });
              deletedRuns++;
              console.log(`Deleted workflow run: ${run.id}`);
            } catch (error) {
              console.log(`Error deleting run ${run.id}: ${error.message}`);
            }
          }
          
          console.log(`Cleanup completed:`);
          console.log(`- Deleted ${deletedArtifacts} artifacts`);
          console.log(`- Deleted ${deletedRuns} workflow runs`);
          
          // 输出当前存储使用情况
          try {
            const billing = await github.rest.billing.getGithubActionsBillingUser({
              username: owner
            });
            console.log(`Current storage usage: ${billing.data.total_gigabytes_bandwidth_used} GB`);
          } catch (error) {
            console.log('Could not fetch billing information');
          }
