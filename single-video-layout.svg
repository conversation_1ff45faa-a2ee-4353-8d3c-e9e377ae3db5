<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#333">单视频播放器布局</text>
  
  <!-- 左侧文件管理器 -->
  <rect x="20" y="60" width="280" height="720" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="160" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6c757d">文件管理器</text>
  <text x="160" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">VideoFileManager</text>
  
  <!-- 文件列表示意 -->
  <rect x="40" y="120" width="240" height="30" fill="none" stroke="#adb5bd" stroke-width="1"/>
  <text x="50" y="140" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">📁 视频文件夹</text>
  
  <rect x="40" y="160" width="240" height="25" fill="none" stroke="#adb5bd" stroke-width="1"/>
  <text x="50" y="177" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">🎥 video1.mp4</text>
  
  <rect x="40" y="190" width="240" height="25" fill="none" stroke="#adb5bd" stroke-width="1"/>
  <text x="50" y="207" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">🎥 video2.mp4</text>
  
  <!-- 右侧主内容区域 -->
  <rect x="320" y="60" width="860" height="720" fill="none" stroke="#495057" stroke-width="2"/>
  <text x="750" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#495057">主内容区域 (flex: 1)</text>
  
  <!-- 视频播放器区域 -->
  <rect x="340" y="100" width="820" height="380" fill="#ffffff" stroke="#007bff" stroke-width="2"/>
  <text x="750" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#007bff">视频播放器区域 (bg: white, p: 4)</text>
  
  <!-- 视频容器 -->
  <rect x="360" y="140" width="780" height="280" fill="#000000" stroke="#dc3545" stroke-width="2"/>
  <text x="750" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">视频容器 (动态高度, bg: black)</text>
  
  <!-- 视频元素 -->
  <rect x="380" y="180" width="740" height="200" fill="none" stroke="#ffffff" stroke-width="2" stroke-dasharray="3,3"/>
  <text x="750" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#ffffff">VIDEO ELEMENT</text>
  <text x="750" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">100% width/height, objectFit: contain</text>
  
  <!-- 视频控制栏 -->
  <rect x="360" y="430" width="780" height="40" fill="rgba(0,0,0,0.8)" stroke="#ffc107" stroke-width="2"/>
  <text x="750" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">视频控制栏 (VideoPlayerControls)</text>
  
  <!-- 控制按钮示意 -->
  <circle cx="390" cy="450" r="8" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="390" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">▶</text>
  
  <rect x="420" y="445" width="200" height="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="520" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">进度条</text>
  
  <text x="650" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">00:00 / 10:30</text>
  
  <circle cx="750" cy="450" r="8" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="750" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">🔊</text>
  
  <rect x="780" y="445" width="60" height="10" fill="none" stroke="#ffffff" stroke-width="1"/>
  
  <circle cx="860" cy="450" r="8" fill="none" stroke="#ffffff" stroke-width="1"/>
  <text x="860" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#ffffff">⛶</text>
  
  <!-- 下半部分 -->
  <rect x="340" y="500" width="820" height="270" fill="#f8f9fa" stroke="#28a745" stroke-width="2"/>
  <text x="750" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#28a745">下半部分 (flex: 1, 响应式布局)</text>
  
  <!-- 地图显示区域 -->
  <rect x="360" y="540" width="450" height="210" fill="#ffffff" stroke="#17a2b8" stroke-width="2"/>
  <text x="585" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#17a2b8">地图显示区域 (flex: 55%)</text>
  <text x="585" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#6c757d">🗺️</text>
  <text x="585" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">MapComponent</text>
  
  <!-- GPS信息面板 -->
  <rect x="830" y="540" width="310" height="210" fill="#ffffff" stroke="#6f42c1" stroke-width="2"/>
  <text x="985" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6f42c1">GPS信息面板 (flex: 45%)</text>
  <text x="985" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">GPSInfoPanel</text>
  
  <!-- GPS信息示意 -->
  <text x="850" y="620" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">📍 经度: 116.404</text>
  <text x="850" y="635" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">📍 纬度: 39.915</text>
  <text x="850" y="650" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">🚗 速度: 60 km/h</text>
  <text x="850" y="665" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">🧭 方向: 北</text>
  <text x="850" y="680" font-family="Arial, sans-serif" font-size="10" fill="#6c757d">⏰ 时间: 14:30:25</text>
</svg>
