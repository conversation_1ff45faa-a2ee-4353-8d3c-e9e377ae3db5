# FFmpeg 二进制文件目录

此目录包含不同平台和架构的 FFmpeg 二进制文件，用于视频处理和 GPS 数据提取。

## 目录结构

```
ffmpeg/
├── win-x64/          # Windows x64 架构
│   ├── ffmpeg.exe
│   └── ffprobe.exe
├── win-arm64/        # Windows ARM64 架构
│   ├── ffmpeg.exe
│   └── ffprobe.exe
├── mac-x64/          # macOS Intel x64 架构
│   ├── ffmpeg
│   └── ffprobe
├── mac-arm64/        # macOS Apple Silicon ARM64 架构
│   ├── ffmpeg
│   └── ffprobe
├── linux-x64/        # Linux x64 架构
│   ├── ffmpeg
│   └── ffprobe
└── linux-arm64/      # Linux ARM64 架构
    ├── ffmpeg
    └── ffprobe
```

## 使用说明

应用程序会根据当前运行的平台和架构自动选择对应的 FFmpeg 二进制文件：

- **平台检测**: `process.platform` (darwin/win32/linux)
- **架构检测**: `process.arch` (x64/arm64)
- **路径格式**: `ffmpeg/${platform}-${arch}/ffmpeg`

## 获取 FFmpeg 二进制文件

您需要从以下来源下载对应平台的 FFmpeg 二进制文件：

### Windows
- 下载地址: https://ffmpeg.org/download.html#build-windows
- 或使用: https://github.com/BtbN/FFmpeg-Builds/releases

### macOS
- 使用 Homebrew: `brew install ffmpeg`
- 或下载地址: https://ffmpeg.org/download.html#build-mac

### Linux
- 使用包管理器: `sudo apt install ffmpeg` (Ubuntu/Debian)
- 或下载地址: https://ffmpeg.org/download.html#build-linux

## 注意事项

1. 确保所有二进制文件都有执行权限
2. Windows 版本需要 `.exe` 扩展名
3. macOS 和 Linux 版本不需要扩展名
4. 文件大小可能较大，建议使用压缩版本
5. 确保 FFmpeg 版本兼容性（建议使用 4.4+ 版本）

## 开发环境

在开发环境中，应用程序会自动使用 `@ffmpeg-installer/ffmpeg` 和 `@ffprobe-installer/ffprobe` 包提供的二进制文件，无需手动配置此目录。
