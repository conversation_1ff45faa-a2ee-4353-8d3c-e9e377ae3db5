const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const os = require('os');
const { app } = require('electron');

// 获取 FFmpeg 路径的函数
function getFFmpegPaths() {
  const isDev = process.env.NODE_ENV === 'development';
  const isPackaged = app.isPackaged;

  if (isDev && !isPackaged) {
    // 开发环境：使用 npm 包
    try {
      const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
      const ffprobePath = require('@ffprobe-installer/ffprobe').path;
      return { ffmpegPath, ffprobePath };
    } catch (error) {
      console.warn('开发环境 FFmpeg 包未找到，尝试使用打包版本');
    }
  }

  // 生产环境：使用打包的 FFmpeg 文件
  const platform = process.platform;
  const arch = process.arch;

  let platformName;
  if (platform === 'darwin') {
    platformName = 'mac';
  } else if (platform === 'win32') {
    platformName = 'win';
  } else {
    platformName = 'linux';
  }

  const ffmpegDir = isPackaged
    ? path.join(process.resourcesPath, 'ffmpeg', `${platformName}-${arch}`)
    : path.join(__dirname, '..', 'ffmpeg', `${platformName}-${arch}`);

  const ffmpegExt = platform === 'win32' ? '.exe' : '';
  const ffmpegPath = path.join(ffmpegDir, `ffmpeg${ffmpegExt}`);
  const ffprobePath = path.join(ffmpegDir, `ffprobe${ffmpegExt}`);

  return { ffmpegPath, ffprobePath };
}

// 设置FFmpeg和FFprobe路径
const { ffmpegPath, ffprobePath } = getFFmpegPaths();
ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

// 缩略图缓存目录
const THUMBNAIL_CACHE_DIR = path.join(os.tmpdir(), 'meea-viofo-thumbnails');

// 确保缓存目录存在
if (!fs.existsSync(THUMBNAIL_CACHE_DIR)) {
  fs.mkdirSync(THUMBNAIL_CACHE_DIR, { recursive: true });
}

/**
 * 获取视频时长
 * @param {string} videoPath 视频文件路径
 * @returns {Promise<number>} 视频时长（秒）
 */
function getVideoDuration(videoPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }
      
      const duration = metadata.format.duration;
      resolve(duration);
    });
  });
}

/**
 * 生成文件的缓存键
 * @param {string} videoPath 视频文件路径
 * @param {number} timestamp 时间戳（秒）
 * @returns {string} 缓存键
 */
function generateCacheKey(videoPath, timestamp) {
  const fileStats = fs.statSync(videoPath);
  const fileInfo = `${videoPath}-${fileStats.size}-${fileStats.mtime.getTime()}`;
  const hash = crypto.createHash('md5').update(fileInfo).digest('hex');
  return `${hash}-${Math.floor(timestamp)}`;
}

/**
 * 获取缩略图缓存路径
 * @param {string} cacheKey 缓存键
 * @returns {string} 缓存文件路径
 */
function getThumbnailCachePath(cacheKey) {
  return path.join(THUMBNAIL_CACHE_DIR, `${cacheKey}.jpg`);
}

/**
 * 生成单个缩略图
 * @param {string} videoPath 视频文件路径
 * @param {number} timestamp 时间戳（秒）
 * @param {Object} options 选项
 * @returns {Promise<string>} 缩略图文件路径
 */
function generateThumbnail(videoPath, timestamp, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      width = 160,
      height = 90,
      quality = 80
    } = options;

    // 检查缓存
    const cacheKey = generateCacheKey(videoPath, timestamp);
    const cachePath = getThumbnailCachePath(cacheKey);
    
    if (fs.existsSync(cachePath)) {
      console.log(`使用缓存的缩略图: ${cachePath}`);
      resolve(cachePath);
      return;
    }

    console.log(`生成缩略图: ${videoPath} at ${timestamp}s`);

    ffmpeg(videoPath)
      .seekInput(timestamp)
      .frames(1)
      .size(`${width}x${height}`)
      .outputOptions([
        '-q:v', quality.toString(),
        '-f', 'image2'
      ])
      .output(cachePath)
      .on('end', () => {
        console.log(`缩略图生成完成: ${cachePath}`);
        resolve(cachePath);
      })
      .on('error', (err) => {
        console.error(`生成缩略图失败: ${err.message}`);
        reject(err);
      })
      .run();
  });
}

/**
 * 批量生成缩略图
 * @param {string} videoPath 视频文件路径
 * @param {Object} options 选项
 * @returns {Promise<Array>} 缩略图信息数组
 */
async function generateThumbnails(videoPath, options = {}) {
  try {
    const {
      interval = 10, // 间隔秒数
      maxThumbnails = 100, // 最大缩略图数量
      width = 160,
      height = 90,
      quality = 80
    } = options;

    // 获取视频时长
    const duration = await getVideoDuration(videoPath);
    console.log(`视频时长: ${duration}秒`);

    // 计算缩略图时间点
    const thumbnailTimes = [];
    const actualInterval = Math.max(interval, duration / maxThumbnails);

    for (let time = 0; time < duration; time += actualInterval) {
      thumbnailTimes.push(time);
    }

    // 确保包含最后一帧
    if (thumbnailTimes[thumbnailTimes.length - 1] < duration - 1) {
      thumbnailTimes.push(duration - 1);
    }

    console.log(`将生成 ${thumbnailTimes.length} 个缩略图`);

    // 首先检查已存在的缓存
    const thumbnails = [];
    const needGenerate = [];

    for (const time of thumbnailTimes) {
      const cacheKey = generateCacheKey(videoPath, time);
      const cachePath = getThumbnailCachePath(cacheKey);

      if (fs.existsSync(cachePath)) {
        // 使用缓存的缩略图
        thumbnails.push({
          time: time,
          path: cachePath,
          url: `file://${cachePath}`,
          cacheKey: cacheKey
        });
      } else {
        // 需要生成的缩略图
        needGenerate.push(time);
      }
    }

    console.log(`使用缓存: ${thumbnails.length} 个，需要生成: ${needGenerate.length} 个`);

    // 生成缺失的缩略图
    if (needGenerate.length > 0) {
      const batchSize = 3; // 减少并发数量以提高稳定性

      for (let i = 0; i < needGenerate.length; i += batchSize) {
        const batch = needGenerate.slice(i, i + batchSize);
        const batchPromises = batch.map(async (time) => {
          try {
            const thumbnailPath = await generateThumbnail(videoPath, time, {
              width,
              height,
              quality
            });

            return {
              time: time,
              path: thumbnailPath,
              url: `file://${thumbnailPath}`,
              cacheKey: generateCacheKey(videoPath, time)
            };
          } catch (error) {
            console.error(`生成时间点 ${time}s 的缩略图失败:`, error);
            return null;
          }
        });

        const batchResults = await Promise.all(batchPromises);
        thumbnails.push(...batchResults.filter(result => result !== null));

        // 显示进度
        console.log(`缩略图生成进度: ${Math.min(i + batchSize, needGenerate.length)}/${needGenerate.length}`);
      }
    }

    // 按时间排序
    thumbnails.sort((a, b) => a.time - b.time);

    console.log(`缩略图生成完成，共 ${thumbnails.length} 个`);
    return thumbnails;

  } catch (error) {
    console.error('批量生成缩略图失败:', error);
    throw error;
  }
}

/**
 * 清理过期的缓存文件
 * @param {number} maxAge 最大缓存时间（毫秒）
 */
function cleanupCache(maxAge = 7 * 24 * 60 * 60 * 1000) { // 默认7天
  try {
    const files = fs.readdirSync(THUMBNAIL_CACHE_DIR);
    const now = Date.now();
    let cleanedCount = 0;

    for (const file of files) {
      const filePath = path.join(THUMBNAIL_CACHE_DIR, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个过期缓存文件`);
    }
  } catch (error) {
    console.error('清理缓存失败:', error);
  }
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
function getCacheStats() {
  try {
    const files = fs.readdirSync(THUMBNAIL_CACHE_DIR);
    let totalSize = 0;
    
    for (const file of files) {
      const filePath = path.join(THUMBNAIL_CACHE_DIR, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    }

    return {
      fileCount: files.length,
      totalSize: totalSize,
      cacheDir: THUMBNAIL_CACHE_DIR
    };
  } catch (error) {
    return {
      fileCount: 0,
      totalSize: 0,
      cacheDir: THUMBNAIL_CACHE_DIR
    };
  }
}

/**
 * 预加载缩略图（异步）
 * @param {string} videoPath 视频文件路径
 * @param {Object} options 选项
 */
function preloadThumbnails(videoPath, options = {}) {
  // 异步生成，不阻塞主线程
  generateThumbnails(videoPath, options).catch(error => {
    console.error('预加载缩略图失败:', error);
  });
}

// 定期清理缓存（每小时执行一次）
setInterval(() => {
  cleanupCache();
}, 60 * 60 * 1000);

module.exports = {
  generateThumbnail,
  generateThumbnails,
  preloadThumbnails,
  getVideoDuration,
  cleanupCache,
  getCacheStats,
  THUMBNAIL_CACHE_DIR
};
