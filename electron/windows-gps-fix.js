const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// Windows平台运行时修复
function fixWindowsGPSIssues() {
  if (process.platform !== 'win32' || !app.isPackaged) {
    return;
  }

  console.log('🪟 Windows平台GPS修复...');
  
  // 检查ExifTool路径
  const resourcesPath = process.resourcesPath;
  const exiftoolPath = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool');
  
  console.log('🔍 检查ExifTool:', exiftoolPath);
  console.log('📁 文件存在:', fs.existsSync(exiftoolPath));
  
  if (fs.existsSync(exiftoolPath)) {
    try {
      const stats = fs.statSync(exiftoolPath);
      console.log('📊 ExifTool文件大小:', stats.size);
      console.log('📅 修改时间:', stats.mtime);
    } catch (error) {
      console.error('❌ ExifTool状态检查失败:', error.message);
    }
  } else {
    console.warn('⚠️ ExifTool文件不存在，GPS功能可能无法正常工作');
    
    // 尝试查找备用路径
    const alternatePaths = [
      path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool.exe'),
      path.join(resourcesPath, 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool'),
      path.join(path.dirname(process.execPath), 'resources', 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool')
    ];
    
    for (const altPath of alternatePaths) {
      if (fs.existsSync(altPath)) {
        console.log('✅ 找到备用ExifTool路径:', altPath);
        break;
      }
    }
  }
  
  // 检查用户数据目录权限
  try {
    const userDataPath = app.getPath('userData');
    console.log('📁 用户数据目录:', userDataPath);
    
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
      console.log('✅ 创建用户数据目录');
    }
    
    // 测试写入权限
    const testFile = path.join(userDataPath, 'test-write.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log('✅ 用户数据目录写入权限正常');
    
  } catch (error) {
    console.error('❌ 用户数据目录权限问题:', error.message);
  }
}

module.exports = { fixWindowsGPSIssues };