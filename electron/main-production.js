const { app, BrowserWindow, Menu, dialog, ipcMain, protocol, globalShortcut, shell, clipboard } = require('electron');
const path = require('path');
const fs = require('fs');
const { ExifTool } = require('exiftool-vendored');
const { convertGpsTrack } = require('./gps-converter');
const { extractGPSData: extractGPSDataWithFFmpeg } = require('./ffmpeg-gps-extractor');
const { LicenseService } = require('./license-service');

// 生产环境配置
const IS_PRODUCTION = true;
const DISABLE_DEV_TOOLS = true;
const DISABLE_CONSOLE = true;

// 配置管理
const configPath = path.join(app.getPath('userData'), 'config.json');

// 许可证服务实例
let licenseService;

// 默认配置
const defaultConfig = {
  lastVideoFolder: null,
  windowBounds: {
    width: 1400,
    height: 900
  },
  amapApiKey: null // 高德地图App Key
};

// 读取配置
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      return { ...defaultConfig, ...config };
    }
  } catch (error) {
    if (!IS_PRODUCTION) {
      console.error('读取配置文件失败:', error);
    }
  }
  return defaultConfig;
}

// 保存配置
function saveConfig(config) {
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    if (!IS_PRODUCTION) {
      console.error('保存配置文件失败:', error);
    }
  }
}

let mainWindow;
let exifTool;

// 生产环境下禁用控制台输出
if (DISABLE_CONSOLE) {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
  console.info = () => {};
  console.debug = () => {};
}

function createWindow() {
  const config = loadConfig();

  mainWindow = new BrowserWindow({
    width: config.windowBounds.width,
    height: config.windowBounds.height,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      devTools: !DISABLE_DEV_TOOLS, // 生产环境禁用开发工具
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    icon: path.join(__dirname, '../build/icons/icon.png'),
    show: false,
    titleBarStyle: 'default',
    autoHideMenuBar: false, // 保留标题栏
    fullscreenable: true,
    minimizable: true,
    maximizable: true,
    resizable: true
  });

  // 创建最小应用菜单
  const template = [];

  if (process.platform === 'darwin') {
    // macOS: 只保留应用程序菜单
    template.push({
      label: app.getName(),
      submenu: [
        {
          label: '关于 ' + app.getName(),
          role: 'about'
        },
        { type: 'separator' },
        {
          label: '隐藏 ' + app.getName(),
          accelerator: 'Command+H',
          role: 'hide'
        },
        {
          label: '隐藏其他',
          accelerator: 'Command+Shift+H',
          role: 'hideothers'
        },
        {
          label: '显示全部',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: 'Command+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  console.log('📋 生产环境已设置最小应用菜单');

  const isDev = !app.isPackaged;

  if (isDev && !IS_PRODUCTION) {
    mainWindow.loadURL('http://localhost:5174');
    // 开发环境下打开开发工具
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 生产环境下禁用右键菜单
  if (IS_PRODUCTION) {
    mainWindow.webContents.on('context-menu', (e) => {
      e.preventDefault();
    });
  }

  // 生产环境下禁用调试和刷新快捷键
  if (IS_PRODUCTION) {
    mainWindow.webContents.on('before-input-event', (event, input) => {
      // 禁用调试相关快捷键
      if (
        input.key === 'F12' ||
        (input.control && input.shift && input.key === 'I') ||
        (input.control && input.shift && input.key === 'J') ||
        (input.control && input.shift && input.key === 'C') ||
        (input.control && input.shift && input.key === 'D') ||
        (input.meta && input.shift && input.key === 'I') ||
        (input.meta && input.shift && input.key === 'J') ||
        (input.meta && input.shift && input.key === 'C') ||
        (input.meta && input.shift && input.key === 'D') ||
        (input.meta && input.alt && input.key === 'I') ||
        // 禁用刷新快捷键
        (input.control && input.key === 'R') ||
        (input.meta && input.key === 'R') ||
        input.key === 'F5' ||
        // 禁用控制台快捷键
        (input.control && input.shift && input.key === 'K') ||
        (input.meta && input.shift && input.key === 'K') ||
        // 禁用其他调试快捷键
        (input.control && input.shift && input.key === 'L') ||
        (input.meta && input.shift && input.key === 'L') ||
        (input.control && input.shift && input.key === 'T') ||
        (input.meta && input.shift && input.key === 'T')
      ) {
        event.preventDefault();
        console.log('🚫 [生产环境] 已阻止调试/刷新快捷键:', input.key);
      }
    });
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 保存窗口大小
  mainWindow.on('resize', () => {
    const bounds = mainWindow.getBounds();
    const config = loadConfig();
    config.windowBounds = {
      width: bounds.width,
      height: bounds.height
    };
    saveConfig(config);
  });

  // 阻止新窗口打开，改为在默认浏览器中打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// 生产环境下禁用全局快捷键
if (!IS_PRODUCTION) {
  app.whenReady().then(() => {
    // 注册全局快捷键（仅开发环境）
    globalShortcut.register('CommandOrControl+Shift+D', () => {
      if (mainWindow) {
        mainWindow.webContents.toggleDevTools();
      }
    });
  });
}

app.whenReady().then(async () => {
  // 创建最小应用菜单
  const template = [];

  if (process.platform === 'darwin') {
    // macOS: 只保留应用程序菜单
    template.push({
      label: app.getName(),
      submenu: [
        {
          label: '关于 ' + app.getName(),
          role: 'about'
        },
        { type: 'separator' },
        {
          label: '隐藏 ' + app.getName(),
          accelerator: 'Command+H',
          role: 'hide'
        },
        {
          label: '隐藏其他',
          accelerator: 'Command+Shift+H',
          role: 'hideothers'
        },
        {
          label: '显示全部',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: 'Command+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  console.log('📋 生产环境已在应用启动时设置最小应用菜单');

  // 注册机器码查看快捷键（生产环境也可用）
  try {
    const machineIdSuccess = globalShortcut.register('CommandOrControl+Shift+M', () => {
      if (mainWindow) {
        // 这里需要实现 showMachineIdDialog 函数或导入
        const { dialog } = require('electron');
        const machineId = require('node-machine-id').machineIdSync();

        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '机器码信息',
          message: '设备机器码',
          detail: `机器码: ${machineId}\n\n请将此机器码提供给技术支持以获取许可证。`,
          buttons: ['复制到剪贴板', '关闭'],
          defaultId: 0,
          cancelId: 1
        }).then((result) => {
          if (result.response === 0) {
            const { clipboard } = require('electron');
            clipboard.writeText(machineId);
            console.log('🔑 机器码已复制到剪贴板');
          }
        });

        console.log('🔑 [生产环境] 机器码查看快捷键被触发');
      }
    });

    if (machineIdSuccess) {
      console.log('🔑 [生产环境] 已注册机器码查看快捷键: Ctrl+Shift+M');
    } else {
      console.log('❌ [生产环境] 机器码快捷键注册失败');
    }
  } catch (error) {
    console.error('❌ [生产环境] 机器码快捷键注册异常:', error);
  }

  // 初始化许可证服务
  try {
    licenseService = new LicenseService();
    await licenseService.initialize();
  } catch (error) {
    if (!IS_PRODUCTION) {
      console.error('许可证服务初始化失败:', error);
    }
  }

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // 清理全局快捷键
  globalShortcut.unregisterAll();

  // 清理 ExifTool
  if (exifTool) {
    exifTool.end();
  }
});

// 生产环境下禁用某些 IPC 处理程序
const productionDisabledHandlers = [
  'open-dev-tools',
  'reload-app',
  'clear-cache'
];

// IPC 处理程序（省略具体实现，与原文件相同但添加生产环境检查）
// ... 这里会包含所有原有的 IPC 处理程序，但在生产环境下禁用某些功能

// 生产环境特定的错误处理
process.on('uncaughtException', (error) => {
  if (!IS_PRODUCTION) {
    console.error('未捕获的异常:', error);
  }
  // 生产环境下静默处理错误，避免应用崩溃
});

process.on('unhandledRejection', (reason, promise) => {
  if (!IS_PRODUCTION) {
    console.error('未处理的 Promise 拒绝:', reason);
  }
  // 生产环境下静默处理错误
});

// 导出配置供其他模块使用
module.exports = {
  IS_PRODUCTION,
  DISABLE_DEV_TOOLS,
  DISABLE_CONSOLE
};
