/**
 * GPS坐标系转换工具
 * WGS84 -> GCJ-02 (火星坐标系)
 * 
 * WGS84: 世界大地测量系统，GPS设备使用的坐标系
 * GCJ-02: 中国国家测绘局制定的坐标系，高德地图、腾讯地图等使用
 */

// 定义常量
const PI = Math.PI;
const A = 6378245.0; // 长半轴
const EE = 0.00669342162296594323; // 偏心率平方

/**
 * 判断是否在中国境内
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {boolean}
 */
function isInChina(lng, lat) {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
}

/**
 * 转换纬度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number}
 */
function transformLat(lng, lat) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 
            0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

/**
 * 转换经度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number}
 */
function transformLng(lng, lat) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 
            0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
  return ret;
}

/**
 * WGS84转GCJ02
 * @param {number} lng WGS84经度
 * @param {number} lat WGS84纬度
 * @returns {Array} [GCJ02经度, GCJ02纬度]
 */
function wgs84ToGcj02(lng, lat) {
  // 如果不在中国境内，不进行转换
  if (!isInChina(lng, lat)) {
    return [lng, lat];
  }

  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
  
  const mglat = lat + dlat;
  const mglng = lng + dlng;
  
  return [mglng, mglat];
}

/**
 * 转换GPS点
 * @param {Object} gpsPoint GPS点对象
 * @returns {Object} 转换后的GPS点对象
 */
function convertGpsPoint(gpsPoint) {
  if (!gpsPoint || !gpsPoint.latitude || !gpsPoint.longitude) {
    return gpsPoint;
  }

  const [convertedLng, convertedLat] = wgs84ToGcj02(gpsPoint.longitude, gpsPoint.latitude);
  
  return {
    ...gpsPoint,
    latitude: convertedLat,
    longitude: convertedLng,
    // 保留原始坐标用于调试
    originalLatitude: gpsPoint.latitude,
    originalLongitude: gpsPoint.longitude
  };
}

/**
 * 转换GPS轨迹
 * @param {Object} gpsTrack GPS轨迹对象
 * @returns {Object} 转换后的GPS轨迹对象
 */
function convertGpsTrack(gpsTrack) {
  if (!gpsTrack || !gpsTrack.points || !Array.isArray(gpsTrack.points)) {
    return gpsTrack;
  }

  const convertedPoints = gpsTrack.points.map(point => convertGpsPoint(point));

  return {
    ...gpsTrack,
    points: convertedPoints,
    // 保留所有统计信息，坐标转换不影响距离、速度等统计数据
    totalDistance: gpsTrack.totalDistance,
    duration: gpsTrack.duration,
    averageSpeed: gpsTrack.averageSpeed,
    maxSpeed: gpsTrack.maxSpeed,
    minSpeed: gpsTrack.minSpeed,
    startTime: gpsTrack.startTime,
    endTime: gpsTrack.endTime,
    pointCount: gpsTrack.pointCount,
    statistics: gpsTrack.statistics
  };
}

module.exports = {
  wgs84ToGcj02,
  convertGpsPoint,
  convertGpsTrack,
  isInChina
};
