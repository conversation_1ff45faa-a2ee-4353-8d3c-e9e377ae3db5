const crypto = require('crypto');
const os = require('os');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

/**
 * 机器码生成器
 * 基于硬件特征生成稳定的机器码，确保重新安装后机器码保持不变
 */
class MachineIdGenerator {
  constructor() {
    // 不使用缓存，每次都重新生成
  }

  /**
   * 获取CPU信息
   */
  async getCpuInfo() {
    try {
      const cpus = os.cpus();
      if (cpus && cpus.length > 0) {
        // 使用第一个CPU的型号信息
        return cpus[0].model.replace(/\s+/g, '');
      }
    } catch (error) {
      console.warn('获取CPU信息失败:', error.message);
    }
    return 'unknown-cpu';
  }

  /**
   * 获取主板序列号（Windows）
   */
  async getWindowsMotherboardSerial() {
    try {
      const { stdout } = await execAsync('wmic baseboard get serialnumber /value');
      const match = stdout.match(/SerialNumber=(.+)/);
      if (match && match[1] && match[1].trim() !== '') {
        return match[1].trim();
      }
    } catch (error) {
      console.warn('获取Windows主板序列号失败:', error.message);
    }
    return null;
  }

  /**
   * 获取系统UUID（Windows）
   */
  async getWindowsSystemUUID() {
    try {
      const { stdout } = await execAsync('wmic csproduct get uuid /value');
      const match = stdout.match(/UUID=(.+)/);
      if (match && match[1] && match[1].trim() !== '') {
        return match[1].trim();
      }
    } catch (error) {
      console.warn('获取Windows系统UUID失败:', error.message);
    }
    return null;
  }

  /**
   * 获取macOS硬件UUID
   */
  async getMacOSHardwareUUID() {
    try {
      const { stdout } = await execAsync('system_profiler SPHardwareDataType | grep "Hardware UUID"');
      const match = stdout.match(/Hardware UUID:\s*(.+)/);
      if (match && match[1] && match[1].trim() !== '') {
        return match[1].trim();
      }
    } catch (error) {
      console.warn('获取macOS硬件UUID失败:', error.message);
    }
    return null;
  }

  /**
   * 获取Linux机器ID
   */
  async getLinuxMachineId() {
    try {
      // 尝试读取 /etc/machine-id
      if (fs.existsSync('/etc/machine-id')) {
        const machineId = fs.readFileSync('/etc/machine-id', 'utf8').trim();
        if (machineId && machineId.length > 0) {
          return machineId;
        }
      }

      // 尝试读取 /var/lib/dbus/machine-id
      if (fs.existsSync('/var/lib/dbus/machine-id')) {
        const machineId = fs.readFileSync('/var/lib/dbus/machine-id', 'utf8').trim();
        if (machineId && machineId.length > 0) {
          return machineId;
        }
      }
    } catch (error) {
      console.warn('获取Linux机器ID失败:', error.message);
    }
    return null;
  }

  /**
   * 获取网络接口MAC地址
   */
  async getNetworkMacAddress() {
    try {
      const interfaces = os.networkInterfaces();
      const macAddresses = [];

      for (const name of Object.keys(interfaces)) {
        const iface = interfaces[name];
        for (const alias of iface) {
          // 跳过内部接口和虚拟接口
          if (!alias.internal && alias.mac && alias.mac !== '00:00:00:00:00:00') {
            macAddresses.push(alias.mac);
          }
        }
      }

      // 排序确保一致性
      macAddresses.sort();
      return macAddresses.length > 0 ? macAddresses[0] : null;
    } catch (error) {
      console.warn('获取MAC地址失败:', error.message);
    }
    return null;
  }

  /**
   * 收集硬件特征
   */
  async collectHardwareFingerprint() {
    const fingerprint = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      cpu: await this.getCpuInfo(),
      mac: await this.getNetworkMacAddress(),
      totalMemory: os.totalmem().toString()
    };

    // 根据平台获取特定的硬件标识符
    if (process.platform === 'win32') {
      fingerprint.motherboardSerial = await this.getWindowsMotherboardSerial();
      fingerprint.systemUUID = await this.getWindowsSystemUUID();
    } else if (process.platform === 'darwin') {
      fingerprint.hardwareUUID = await this.getMacOSHardwareUUID();
    } else if (process.platform === 'linux') {
      fingerprint.machineId = await this.getLinuxMachineId();
    }

    return fingerprint;
  }

  /**
   * 生成机器码
   */
  async generateMachineId() {
    try {
      // 收集硬件特征
      const fingerprint = await this.collectHardwareFingerprint();

      // 创建稳定的硬件标识符字符串
      const identifiers = [];

      // 添加平台和架构信息
      identifiers.push(fingerprint.platform);
      identifiers.push(fingerprint.arch);

      // 添加CPU信息
      if (fingerprint.cpu) {
        identifiers.push(fingerprint.cpu);
      }

      // 添加MAC地址
      if (fingerprint.mac) {
        identifiers.push(fingerprint.mac);
      }

      // 添加内存大小
      identifiers.push(fingerprint.totalMemory);

      // 添加平台特定的硬件标识符
      if (fingerprint.motherboardSerial) {
        identifiers.push(fingerprint.motherboardSerial);
      }
      if (fingerprint.systemUUID) {
        identifiers.push(fingerprint.systemUUID);
      }
      if (fingerprint.hardwareUUID) {
        identifiers.push(fingerprint.hardwareUUID);
      }
      if (fingerprint.machineId) {
        identifiers.push(fingerprint.machineId);
      }

      // 如果没有足够的硬件标识符，添加主机名作为备用
      if (identifiers.length < 3) {
        identifiers.push(fingerprint.hostname);
      }

      // 生成稳定的哈希值
      const combined = identifiers.join('|');
      const hash = crypto.createHash('sha256').update(combined).digest('hex');

      // 取前32位作为机器码
      const machineId = hash.substring(0, 32).toUpperCase();

      return machineId;

    } catch (error) {
      console.error('生成机器码失败:', error);

      // 如果生成失败，使用备用方案
      const fallbackId = crypto.randomBytes(16).toString('hex').toUpperCase();

      return fallbackId;
    }
  }

  /**
   * 获取机器码（每次都重新生成）
   */
  async getMachineId() {
    return await this.generateMachineId();
  }

  /**
   * 格式化机器码显示（添加分隔符）
   */
  formatMachineId(machineId) {
    if (!machineId || machineId.length !== 32) {
      return machineId;
    }
    
    // 格式化为 XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
    return machineId.match(/.{4}/g).join('-');
  }
}

// 创建单例实例
const machineIdGenerator = new MachineIdGenerator();

module.exports = {
  MachineIdGenerator,
  machineIdGenerator
};
