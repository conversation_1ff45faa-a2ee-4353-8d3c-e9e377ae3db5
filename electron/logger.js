const fs = require('fs');
const path = require('path');

// 支持测试环境
let app;
try {
  app = require('electron').app;
} catch (error) {
  // 测试环境下使用模拟的app对象
  app = global.mockApp || {
    getPath: () => require('os').tmpdir(),
    getName: () => 'MEEA-VIOFO-DEBUG',
    getVersion: () => 'test-version'
  };
}

/**
 * 日志管理系统
 * 功能：
 * - 控制台输出
 * - 文件输出
 * - 日志轮转
 * - 大小限制
 * - 自动清理
 */
class Logger {
  constructor() {
    this.isInitialized = false;
    this.logDir = null;
    this.currentLogFile = null;
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.maxFiles = 5; // 保留最多5个日志文件
    this.originalConsole = {};

    // 延迟初始化，确保app对象可用
    setTimeout(() => {
      this.init();
    }, 100);
  }

  init() {
    try {
      console.log('🔧 [LOGGER] 开始初始化日志系统...');

      // 创建日志目录
      this.createLogDirectory();
      console.log('🔧 [LOGGER] 日志目录创建成功:', this.logDir);

      // 设置当前日志文件
      this.setupCurrentLogFile();
      console.log('🔧 [LOGGER] 日志文件设置成功:', this.currentLogFile);

      // 清理旧日志文件
      this.cleanupOldLogs();

      this.isInitialized = true;
      console.log('✅ [LOGGER] 日志系统初始化成功');

      // 写入初始化成功日志
      this.log('Logger', 'INFO', '日志系统初始化成功');
      this.log('Logger', 'INFO', `日志目录: ${this.logDir}`);
      this.log('Logger', 'INFO', `当前日志文件: ${this.currentLogFile}`);
    } catch (error) {
      console.error('❌ [LOGGER] 日志系统初始化失败:', error);
      console.error('❌ [LOGGER] 错误详情:', error.stack);
    }
  }

  createLogDirectory() {
    try {
      // 获取用户数据目录
      const userDataPath = app.getPath('userData');
      console.log('🔧 [LOGGER] 用户数据目录:', userDataPath);

      this.logDir = path.join(userDataPath, 'logs');
      console.log('🔧 [LOGGER] 计划日志目录:', this.logDir);

      // 创建日志目录
      if (!fs.existsSync(this.logDir)) {
        console.log('🔧 [LOGGER] 日志目录不存在，正在创建...');
        fs.mkdirSync(this.logDir, { recursive: true });
        console.log('✅ [LOGGER] 日志目录创建成功');
      } else {
        console.log('✅ [LOGGER] 日志目录已存在');
      }

      // 验证目录权限
      fs.accessSync(this.logDir, fs.constants.W_OK);
      console.log('✅ [LOGGER] 日志目录写入权限验证成功');

    } catch (error) {
      console.error('❌ [LOGGER] 创建日志目录失败:', error);
      throw error;
    }
  }

  setupCurrentLogFile() {
    try {
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '-');
      const filename = `meea-viofo-debug-${timestamp}.log`;
      this.currentLogFile = path.join(this.logDir, filename);

      console.log('🔧 [LOGGER] 创建日志文件:', this.currentLogFile);

      // 写入日志文件头部信息
      const header = [
        '='.repeat(80),
        `MEEA-VIOFO 调试日志`,
        `启动时间: ${now.toLocaleString()}`,
        `应用版本: ${app.getVersion ? app.getVersion() : 'Unknown'}`,
        `应用名称: ${app.getName ? app.getName() : 'Unknown'}`,
        `平台: ${process.platform} ${process.arch}`,
        `Node.js: ${process.version}`,
        `Electron: ${process.versions.electron}`,
        `日志文件: ${this.currentLogFile}`,
        '='.repeat(80),
        ''
      ].join('\n');

      fs.writeFileSync(this.currentLogFile, header);
      console.log('✅ [LOGGER] 日志文件头部写入成功');

      // 验证文件是否创建成功
      if (fs.existsSync(this.currentLogFile)) {
        const stats = fs.statSync(this.currentLogFile);
        console.log('✅ [LOGGER] 日志文件验证成功, 大小:', stats.size, 'bytes');
      } else {
        throw new Error('日志文件创建后不存在');
      }

    } catch (error) {
      console.error('❌ [LOGGER] 设置日志文件失败:', error);
      throw error;
    }
  }

  cleanupOldLogs() {
    try {
      const files = fs.readdirSync(this.logDir)
        .filter(file => file.startsWith('meea-viofo-debug-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          mtime: fs.statSync(path.join(this.logDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime); // 按修改时间降序排列

      // 删除超过限制的文件
      if (files.length > this.maxFiles) {
        const filesToDelete = files.slice(this.maxFiles);
        filesToDelete.forEach(file => {
          try {
            fs.unlinkSync(file.path);
            console.log(`已删除旧日志文件: ${file.name}`);
          } catch (error) {
            console.error(`删除日志文件失败: ${file.name}`, error);
          }
        });
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  checkFileSize() {
    if (!this.currentLogFile || !fs.existsSync(this.currentLogFile)) {
      return;
    }

    try {
      const stats = fs.statSync(this.currentLogFile);
      if (stats.size > this.maxFileSize) {
        // 文件过大，创建新的日志文件
        this.log('Logger', 'INFO', `日志文件过大 (${(stats.size / 1024 / 1024).toFixed(2)}MB)，创建新文件`);
        this.setupCurrentLogFile();
      }
    } catch (error) {
      console.error('检查日志文件大小失败:', error);
    }
  }

  formatLogEntry(category, level, message, ...args) {
    const now = new Date();
    const timestamp = now.toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] [${level}] [${category}] ${message}${formattedArgs}`;
  }

  log(category, level, message, ...args) {
    if (!this.isInitialized) {
      console.log('⚠️ [LOGGER] 日志系统未初始化，跳过日志写入');
      return;
    }

    try {
      // 检查文件大小
      this.checkFileSize();

      // 格式化日志条目
      const logEntry = this.formatLogEntry(category, level, message, ...args);

      // 写入文件
      fs.appendFileSync(this.currentLogFile, logEntry + '\n');

      // 每10条日志输出一次确认信息（避免过多输出）
      if (!this.logCount) this.logCount = 0;
      this.logCount++;
      if (this.logCount % 10 === 1) {
        console.log(`📝 [LOGGER] 已写入 ${this.logCount} 条日志到文件`);
      }

    } catch (error) {
      console.error('❌ [LOGGER] 写入日志文件失败:', error);
      console.error('❌ [LOGGER] 日志内容:', category, level, message, ...args);
    }
  }

  // 便捷方法
  debug(category, message, ...args) {
    this.log(category, 'DEBUG', message, ...args);
  }

  info(category, message, ...args) {
    this.log(category, 'INFO', message, ...args);
  }

  warn(category, message, ...args) {
    this.log(category, 'WARN', message, ...args);
  }

  error(category, message, ...args) {
    this.log(category, 'ERROR', message, ...args);
  }

  // 获取日志目录路径
  getLogDirectory() {
    return this.logDir;
  }

  // 获取当前日志文件路径
  getCurrentLogFile() {
    return this.currentLogFile;
  }

  // 获取所有日志文件
  getAllLogFiles() {
    if (!this.logDir || !fs.existsSync(this.logDir)) {
      return [];
    }

    try {
      return fs.readdirSync(this.logDir)
        .filter(file => file.startsWith('meea-viofo-debug-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          size: fs.statSync(path.join(this.logDir, file)).size,
          mtime: fs.statSync(path.join(this.logDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);
    } catch (error) {
      console.error('获取日志文件列表失败:', error);
      return [];
    }
  }
}

// 创建全局日志实例
const logger = new Logger();

module.exports = logger;
