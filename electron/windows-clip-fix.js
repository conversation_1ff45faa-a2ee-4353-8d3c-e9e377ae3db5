const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { spawn } = require('child_process');

/**
 * Windows平台截图和剪辑功能修复
 */
class WindowsClipFix {
  constructor() {
    this.isWindows = process.platform === 'win32';
    this.isPackaged = app ? app.isPackaged : false;
    this.ffmpegPath = null;
    this.ffprobePath = null;
    this.isInitialized = false;
    this.tempDir = null;
  }

  /**
   * 初始化Windows截图剪辑修复
   */
  initialize() {
    if (!this.isWindows || this.isInitialized) {
      return;
    }

    console.log('🪟 初始化Windows截图剪辑修复...');

    try {
      // 1. 查找FFmpeg路径
      this.findFFmpegPaths();
      
      // 2. 验证FFmpeg可执行性
      this.verifyFFmpeg();
      
      // 3. 设置临时目录
      this.setupTempDirectory();
      
      // 4. 检查文件权限
      this.checkFilePermissions();
      
      this.isInitialized = true;
      console.log('✅ Windows截图剪辑修复初始化成功');
    } catch (error) {
      console.error('❌ Windows截图剪辑修复初始化失败:', error.message);
      this.logDiagnosticInfo();
    }
  }

  /**
   * 查找FFmpeg路径
   */
  findFFmpegPaths() {
    console.log('🔍 查找FFmpeg路径...');
    
    const arch = process.arch;
    const platformName = 'win';
    
    if (this.isPackaged) {
      // 打包环境路径
      const resourcesPath = process.resourcesPath;
      const ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${arch}`);
      
      this.ffmpegPath = path.join(ffmpegDir, 'ffmpeg.exe');
      this.ffprobePath = path.join(ffmpegDir, 'ffprobe.exe');
      
      console.log('📁 打包环境FFmpeg路径:');
      console.log('   FFmpeg:', this.ffmpegPath);
      console.log('   FFprobe:', this.ffprobePath);
      console.log('   FFmpeg存在:', fs.existsSync(this.ffmpegPath));
      console.log('   FFprobe存在:', fs.existsSync(this.ffprobePath));
    } else {
      // 开发环境路径
      try {
        const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
        const ffprobeInstaller = require('@ffprobe-installer/ffprobe');
        
        this.ffmpegPath = ffmpegInstaller.path;
        this.ffprobePath = ffprobeInstaller.path;
        
        console.log('📁 开发环境FFmpeg路径:');
        console.log('   FFmpeg:', this.ffmpegPath);
        console.log('   FFprobe:', this.ffprobePath);
      } catch (error) {
        console.warn('⚠️ 开发环境FFmpeg包未安装，尝试系统路径');
        this.ffmpegPath = 'ffmpeg';
        this.ffprobePath = 'ffprobe';
      }
    }

    // 检查文件是否存在
    if (this.isPackaged) {
      if (!fs.existsSync(this.ffmpegPath)) {
        throw new Error(`FFmpeg不存在: ${this.ffmpegPath}`);
      }
      if (!fs.existsSync(this.ffprobePath)) {
        throw new Error(`FFprobe不存在: ${this.ffprobePath}`);
      }
    }
  }

  /**
   * 验证FFmpeg可执行性 - 增强错误处理
   */
  async verifyFFmpeg() {
    console.log('🧪 验证FFmpeg可执行性...');

    // 首先检查文件基本属性
    try {
      const ffmpegStats = fs.statSync(this.ffmpegPath);
      const ffprobeStats = fs.statSync(this.ffprobePath);

      console.log('📊 FFmpeg文件信息:');
      console.log(`   路径: ${this.ffmpegPath}`);
      console.log(`   大小: ${ffmpegStats.size} bytes`);
      console.log(`   修改时间: ${ffmpegStats.mtime}`);
      console.log(`   是否为文件: ${ffmpegStats.isFile()}`);

      console.log('📊 FFprobe文件信息:');
      console.log(`   路径: ${this.ffprobePath}`);
      console.log(`   大小: ${ffprobeStats.size} bytes`);
      console.log(`   修改时间: ${ffprobeStats.mtime}`);
      console.log(`   是否为文件: ${ffprobeStats.isFile()}`);

      // 检查文件大小是否合理（FFmpeg应该至少几MB）
      if (ffmpegStats.size < 1024 * 1024) { // 小于1MB可能有问题
        console.warn('⚠️ FFmpeg文件大小异常小，可能损坏');
      }

      if (ffprobeStats.size < 1024 * 100) { // 小于100KB可能有问题
        console.warn('⚠️ FFprobe文件大小异常小，可能损坏');
      }

    } catch (error) {
      console.error('❌ 无法读取FFmpeg文件信息:', error.message);
      throw new Error(`FFmpeg文件访问失败: ${error.message}`);
    }

    try {
      // 测试FFmpeg版本
      console.log('🧪 测试FFmpeg版本...');
      await this.testFFmpegCommand(this.ffmpegPath, ['-version']);
      console.log('✅ FFmpeg可执行');

      // 测试FFprobe版本
      console.log('🧪 测试FFprobe版本...');
      await this.testFFmpegCommand(this.ffprobePath, ['-version']);
      console.log('✅ FFprobe可执行');

    } catch (error) {
      console.error('❌ FFmpeg验证失败:', error.message);
      console.error('❌ 错误类型:', error.code || 'UNKNOWN');

      // 针对EFTYPE错误提供特殊处理
      if (error.message.includes('EFTYPE') || error.code === 'EFTYPE') {
        console.error('🚨 EFTYPE错误 - 这通常表示:');
        console.error('   1. FFmpeg文件损坏或不完整');
        console.error('   2. FFmpeg文件不是有效的可执行文件');
        console.error('   3. 缺少必要的运行时库');
        console.error('   4. 文件权限问题');

        // 尝试检查文件头
        try {
          const buffer = fs.readFileSync(this.ffmpegPath, { start: 0, end: 16 });
          const hex = buffer.toString('hex');
          console.error('📄 FFmpeg文件头 (hex):', hex);

          // Windows PE文件应该以MZ开头
          if (!hex.startsWith('4d5a')) { // MZ in hex
            console.error('❌ FFmpeg文件不是有效的Windows PE可执行文件');
          }
        } catch (readError) {
          console.error('❌ 无法读取FFmpeg文件头:', readError.message);
        }
      }

      throw error;
    }
  }

  /**
   * 测试FFmpeg命令 - 使用多种执行方式
   */
  testFFmpegCommand(command, args) {
    return new Promise(async (resolve, reject) => {
      // 多种执行方式，参考ExifTool的成功方案
      const executionMethods = [
        // 方式1: 直接执行
        () => this.executeWithMethod(command, args, {
          stdio: 'pipe',
          timeout: 10000,
          windowsHide: true
        }),
        // 方式2: 使用shell执行
        () => this.executeWithMethod(command, args, {
          stdio: 'pipe',
          timeout: 10000,
          windowsHide: true,
          shell: true
        }),
        // 方式3: 使用cmd包装执行
        () => {
          const quotedPath = command.includes(' ') ? `"${command}"` : command;
          return this.executeWithMethod('cmd', ['/c', quotedPath, ...args], {
            stdio: 'pipe',
            timeout: 10000,
            windowsHide: true
          });
        }
      ];

      let lastError = null;

      for (let i = 0; i < executionMethods.length; i++) {
        try {
          console.log(`🧪 尝试FFmpeg执行方法 ${i + 1}: ${command} ${args.join(' ')}`);
          const result = await executionMethods[i]();
          console.log(`✅ FFmpeg执行方法 ${i + 1} 成功`);
          resolve(result);
          return;
        } catch (error) {
          console.log(`❌ FFmpeg执行方法 ${i + 1} 失败:`, error.message);
          lastError = error;
        }
      }

      // 所有方法都失败
      console.error('❌ 所有FFmpeg执行方法都失败');
      reject(lastError || new Error('FFmpeg执行失败'));
    });
  }

  /**
   * 具体的执行方法 - 参考ExifTool的实现
   */
  executeWithMethod(command, args, spawnOptions) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, spawnOptions);

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        // FFmpeg通常在stderr输出版本信息
        if (code === 0 || stdout.includes('ffmpeg') || stderr.includes('ffmpeg')) {
          resolve({ stdout, stderr, code });
        } else {
          reject(new Error(`命令执行失败: ${command} ${args.join(' ')}, 退出码: ${code}, stderr: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(new Error(`命令执行错误: ${error.message}`));
      });
    });
  }

  /**
   * 设置临时目录
   */
  setupTempDirectory() {
    console.log('📁 设置临时目录...');
    
    try {
      // 使用应用数据目录下的temp文件夹
      const userDataPath = app.getPath('userData');
      this.tempDir = path.join(userDataPath, 'temp', 'screenshots');
      
      if (!fs.existsSync(this.tempDir)) {
        fs.mkdirSync(this.tempDir, { recursive: true });
        console.log('✅ 创建临时目录:', this.tempDir);
      }
      
      // 测试写入权限
      const testFile = path.join(this.tempDir, 'test-write.tmp');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log('✅ 临时目录写入权限正常');
      
    } catch (error) {
      console.error('❌ 临时目录设置失败:', error.message);
      
      // 回退到系统临时目录
      try {
        const os = require('os');
        this.tempDir = path.join(os.tmpdir(), 'meea-screenshots');
        
        if (!fs.existsSync(this.tempDir)) {
          fs.mkdirSync(this.tempDir, { recursive: true });
        }
        
        console.log('⚠️ 使用系统临时目录:', this.tempDir);
      } catch (fallbackError) {
        throw new Error(`临时目录设置完全失败: ${fallbackError.message}`);
      }
    }
  }

  /**
   * 检查文件权限
   */
  checkFilePermissions() {
    console.log('🔐 检查文件权限...');
    
    try {
      // 检查用户数据目录权限
      const userDataPath = app.getPath('userData');
      const testFile = path.join(userDataPath, 'permission-test.tmp');
      
      fs.writeFileSync(testFile, 'permission test');
      const content = fs.readFileSync(testFile, 'utf8');
      fs.unlinkSync(testFile);
      
      if (content === 'permission test') {
        console.log('✅ 用户数据目录权限正常');
      } else {
        throw new Error('文件读写测试失败');
      }
      
    } catch (error) {
      console.error('❌ 文件权限检查失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取修复后的FFmpeg路径
   */
  getFFmpegPaths() {
    if (!this.isInitialized) {
      throw new Error('Windows截图剪辑修复未初始化');
    }
    
    return {
      ffmpegPath: this.ffmpegPath,
      ffprobePath: this.ffprobePath,
      tempDir: this.tempDir
    };
  }

  /**
   * 创建安全的输出路径
   */
  createSafeOutputPath(filename) {
    if (!this.tempDir) {
      throw new Error('临时目录未设置');
    }
    
    // 清理文件名中的非法字符
    const safeFilename = filename.replace(/[<>:"/\\|?*]/g, '_');
    return path.join(this.tempDir, safeFilename);
  }

  /**
   * 清理临时文件
   */
  cleanupTempFiles(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
    if (!this.tempDir || !fs.existsSync(this.tempDir)) {
      return;
    }
    
    try {
      const files = fs.readdirSync(this.tempDir);
      const now = Date.now();
      
      files.forEach(file => {
        const filePath = path.join(this.tempDir, file);
        try {
          const stats = fs.statSync(filePath);
          if (now - stats.mtime.getTime() > maxAge) {
            fs.unlinkSync(filePath);
            console.log('🗑️ 清理临时文件:', file);
          }
        } catch (error) {
          // 忽略单个文件的错误
        }
      });
    } catch (error) {
      console.error('❌ 清理临时文件失败:', error.message);
    }
  }

  /**
   * 输出诊断信息
   */
  logDiagnosticInfo() {
    console.log('🔍 Windows截图剪辑诊断信息:');
    console.log('   平台:', process.platform);
    console.log('   架构:', process.arch);
    console.log('   是否打包:', this.isPackaged);
    console.log('   应用路径:', process.execPath);
    console.log('   资源路径:', process.resourcesPath);
    console.log('   用户数据路径:', app.getPath('userData'));
    console.log('   临时目录:', app.getPath('temp'));
    
    if (this.ffmpegPath) {
      console.log('   FFmpeg路径:', this.ffmpegPath);
      console.log('   FFmpeg存在:', fs.existsSync(this.ffmpegPath));
    }
    
    if (this.ffprobePath) {
      console.log('   FFprobe路径:', this.ffprobePath);
      console.log('   FFprobe存在:', fs.existsSync(this.ffprobePath));
    }
  }

  /**
   * 创建FFmpeg包装器 - 参考ExifTool的成功方案
   */
  createFFmpegWrapper() {
    if (!this.isAvailable()) {
      throw new Error('Windows截图剪辑修复不可用');
    }

    const ffmpegPath = this.ffmpegPath;
    const ffprobePath = this.ffprobePath;
    const self = this; // 保存this引用

    return {
      /**
       * 执行FFmpeg命令
       */
      executeFFmpeg: async (args, options = {}) => {
        console.log('🎬 执行FFmpeg命令:', args.slice(0, 10).join(' '), '...');

        // 多种执行方式，参考ExifTool的成功方案
        const executionMethods = [
          // 方式1: 直接执行
          () => self.executeWithMethod(ffmpegPath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 300000, // FFmpeg可能需要更长时间
            windowsHide: true,
            ...options
          }),
          // 方式2: 使用shell执行
          () => self.executeWithMethod(ffmpegPath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 300000,
            windowsHide: true,
            shell: true,
            ...options
          }),
          // 方式3: 使用cmd包装执行
          () => {
            const quotedPath = ffmpegPath.includes(' ') ? `"${ffmpegPath}"` : ffmpegPath;
            return self.executeWithMethod('cmd', ['/c', quotedPath, ...args], {
              stdio: 'pipe',
              timeout: options.timeout || 300000,
              windowsHide: true,
              ...options
            });
          }
        ];

        let lastError = null;

        for (let i = 0; i < executionMethods.length; i++) {
          try {
            console.log(`🧪 尝试FFmpeg执行方法 ${i + 1}...`);
            const result = await executionMethods[i]();
            console.log('✅ FFmpeg执行成功');
            return result;
          } catch (error) {
            console.log(`❌ FFmpeg执行方法 ${i + 1} 失败:`, error.message);
            lastError = error;
          }
        }

        // 所有方法都失败
        console.error('❌ 所有FFmpeg执行方法都失败');
        throw lastError || new Error('FFmpeg执行失败');
      },

      /**
       * 执行FFprobe命令
       */
      executeFFprobe: async (args, options = {}) => {
        console.log('🔍 执行FFprobe命令:', args.slice(0, 10).join(' '), '...');

        // 多种执行方式
        const executionMethods = [
          // 方式1: 直接执行
          () => self.executeWithMethod(ffprobePath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 30000,
            windowsHide: true,
            ...options
          }),
          // 方式2: 使用shell执行
          () => self.executeWithMethod(ffprobePath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 30000,
            windowsHide: true,
            shell: true,
            ...options
          }),
          // 方式3: 使用cmd包装执行
          () => {
            const quotedPath = ffprobePath.includes(' ') ? `"${ffprobePath}"` : ffprobePath;
            return self.executeWithMethod('cmd', ['/c', quotedPath, ...args], {
              stdio: 'pipe',
              timeout: options.timeout || 30000,
              windowsHide: true,
              ...options
            });
          }
        ];

        let lastError = null;

        for (let i = 0; i < executionMethods.length; i++) {
          try {
            console.log(`🧪 尝试FFprobe执行方法 ${i + 1}...`);
            const result = await executionMethods[i]();
            console.log('✅ FFprobe执行成功');
            return result;
          } catch (error) {
            console.log(`❌ FFprobe执行方法 ${i + 1} 失败:`, error.message);
            lastError = error;
          }
        }

        // 所有方法都失败
        console.error('❌ 所有FFprobe执行方法都失败');
        throw lastError || new Error('FFprobe执行失败');
      },

      /**
       * 获取路径信息
       */
      getPaths: () => ({
        ffmpegPath: ffmpegPath,
        ffprobePath: ffprobePath,
        tempDir: self.tempDir
      })
    };
  }

  /**
   * 检查是否可用
   */
  isAvailable() {
    return this.isInitialized && this.ffmpegPath && this.ffprobePath;
  }
}

// 创建全局实例
const windowsClipFix = new WindowsClipFix();

module.exports = { windowsClipFix };
