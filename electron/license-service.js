/**
 * MEEA-VIOFO 许可证服务
 * 
 * 提供基于RSA加密的许可证认证功能
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class LicenseService {
  constructor() {
    // 公钥文件路径
    this.commPublicKeyPath = path.join(__dirname, '..', 'keys', 'comm_public_key.pem');
    this.signPublicKeyPath = path.join(__dirname, '..', 'keys', 'sign_public_key.pem');

    // 服务端通信公钥（用于加密发送给服务端的数据）
    this.serverPublicKey = null;

    // 服务端签名验证公钥（用于验证服务端返回的证书签名）
    this.serverVerifyPublicKey = null;

    // 许可证文件路径（延迟初始化）
    this.licenseFilePath = null;

    // 服务端API地址 - 根据环境自动选择
    // 在 Electron 中，检测开发环境的更可靠方法
    // 优先使用 app.isPackaged 来判断，这是最可靠的方法
    const isDev = !app.isPackaged;
    this.serverUrl = isDev
      ? 'http://127.0.0.1:37080/api/v1/license'  // 开发环境地址
      : 'https://api.icey.pism.com.cn/api/v1/license'; // 生产环境地址

    console.log('🌍 环境检测:', {
      NODE_ENV: process.env.NODE_ENV,
      isPackaged: app.isPackaged,
      isDev: isDev,
      serverUrl: this.serverUrl
    });

    // 初始化公钥
    this.initializeKeys();
  }

  /**
   * 初始化公钥
   * 从文件中读取RSA公钥
   */
  initializeKeys() {
    try {
      // 读取通信公钥
      if (fs.existsSync(this.commPublicKeyPath)) {
        this.serverPublicKey = fs.readFileSync(this.commPublicKeyPath, 'utf8');
        console.log('✅ 通信公钥加载成功:', this.commPublicKeyPath);
      } else {
        console.error('❌ 通信公钥文件不存在:', this.commPublicKeyPath);
        throw new Error('通信公钥文件不存在');
      }

      // 读取签名验证公钥
      if (fs.existsSync(this.signPublicKeyPath)) {
        this.serverVerifyPublicKey = fs.readFileSync(this.signPublicKeyPath, 'utf8');
        console.log('✅ 签名验证公钥加载成功:', this.signPublicKeyPath);
      } else {
        console.error('❌ 签名验证公钥文件不存在:', this.signPublicKeyPath);
        throw new Error('签名验证公钥文件不存在');
      }
    } catch (error) {
      console.error('❌ 初始化公钥失败:', error);
      throw new Error('公钥初始化失败: ' + error.message);
    }
  }

  /**
   * 获取许可证文件路径
   * @returns {string} 许可证文件路径
   */
  getLicenseFilePath() {
    if (!this.licenseFilePath) {
      this.licenseFilePath = path.join(app.getPath('userData'), 'license.dat');
    }
    return this.licenseFilePath;
  }

  /**
   * 获取构建信息
   * @returns {Object} 构建信息
   */
  getBuildInfo() {
    if (!this.buildInfo) {
      try {
        this.buildInfo = require('./build-info.json');
      } catch (error) {
        console.warn('⚠️ 无法加载构建信息:', error.message);
        this.buildInfo = {
          version: 'unknown',
          buildTime: 'unknown',
          gitHash: 'unknown'
        };
      }
    }
    return this.buildInfo;
  }

  /**
   * 获取应用版本号
   * @returns {string} 版本号
   */
  getVersion() {
    return this.getBuildInfo().version;
  }

  /**
   * 获取构建时间
   * @returns {string} 构建时间
   */
  getBuildTime() {
    return this.getBuildInfo().buildDateTime;
  }

  /**
   * 获取Git提交哈希
   * @returns {string} Git提交哈希
   */
  getGitHash() {
    return this.getBuildInfo().gitHash;
  }

  /**
   * 使用服务端公钥加密数据
   * @param {string} data 要加密的数据
   * @returns {string} Base64编码的加密数据
   */
  encryptWithServerPublicKey(data) {
    try {
      // 检查公钥是否已加载
      if (!this.serverPublicKey) {
        throw new Error('通信公钥未加载');
      }

      console.log('🔒 开始RSA加密...');
      console.log('📝 原始数据长度:', data.length, 'bytes');
      console.log('🔑 使用公钥长度:', this.serverPublicKey.length, 'bytes');

      const buffer = Buffer.from(data, 'utf8');
      console.log('📦 转换为Buffer，长度:', buffer.length, 'bytes');

      const encrypted = crypto.publicEncrypt({
        key: this.serverPublicKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256'
      }, buffer);

      console.log('🔒 加密完成，加密数据长度:', encrypted.length, 'bytes');
      const base64Result = encrypted.toString('base64');
      console.log('📋 Base64编码后长度:', base64Result.length, 'chars');

      return base64Result;
    } catch (error) {
      console.error('❌ RSA加密失败:', error);
      console.error('📊 加密错误详情:', {
        name: error.name,
        message: error.message,
        code: error.code
      });
      throw new Error('数据加密失败');
    }
  }

  /**
   * 请求许可证
   * @param {string} verificationCode - 验证码
   * @param {string} machineId - 机器码
   * @returns {Promise<boolean>} - 是否成功获取许可证
   */
  async requestLicense(verificationCode, machineId) {
    try {
      console.log('🔐 开始请求许可证...');
      console.log('📋 请求参数:', {
        verificationCode: verificationCode,
        machineId: machineId.substring(0, 8) + '...',
        serverUrl: this.serverUrl
      });

      // 1. 准备要发送的数据
      const requestData = {
        verificationCode: verificationCode,
        machineId: machineId
      };
      console.log('📦 准备发送的原始数据:', JSON.stringify(requestData));

      // 2. 使用服务端公钥加密数据
      console.log('🔒 开始RSA加密数据...');
      const encryptedData = this.encryptWithServerPublicKey(JSON.stringify(requestData));
      console.log('✅ RSA加密完成，加密数据长度:', encryptedData.length);

      // 3. 发送请求到服务端
      const requestBody = {
        encryptedData: encryptedData
      };
      console.log('🌐 发送HTTP请求到服务端...');
      console.log('📡 请求URL:', this.serverUrl);
      console.log('📤 请求体大小:', JSON.stringify(requestBody).length, 'bytes');

      const response = await fetch(this.serverUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `MEEA-VIOFO/${app.getVersion()}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 收到服务端响应:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 服务器响应错误:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 服务端响应数据:', JSON.stringify(result, null, 2));

      if (!result.success) {
        console.error('❌ 服务端返回失败:', {
          error: result.error,
          code: result.code,
          message: result.message
        });
        throw new Error(result.error || '许可证请求失败');
      }

      console.log('✅ 服务端返回成功，开始验证证书...');

      // 4. 验证并存储证书
      const certificate = result.certificate;
      console.log('🔍 开始验证证书...');

      if (this.verifyCertificate(certificate, machineId)) {
        console.log('✅ 证书验证成功，开始存储...');
        this.storeCertificate(certificate, machineId);
        console.log('🎉 许可证获取并存储成功');
        return true;
      } else {
        console.error('❌ 证书验证失败');
        throw new Error('证书验证失败');
      }

    } catch (error) {
      console.error('❌ 请求许可证失败:', error);
      return false;
    }
  }

  /**
   * 验证证书
   * @param {Object} certificate - 服务端返回的证书
   * @param {string} machineId - 当前机器码
   * @returns {boolean} - 证书是否有效
   */
  verifyCertificate(certificate, machineId) {
    try {
      console.log('🔍 开始验证证书结构...');

      // 1. 检查证书结构
      if (!certificate || !certificate.data || !certificate.signature) {
        console.error('❌ 证书结构无效:', {
          hasCertificate: !!certificate,
          hasData: !!(certificate && certificate.data),
          hasSignature: !!(certificate && certificate.signature)
        });
        return false;
      }
      console.log('✅ 证书结构验证通过');

      const certData = certificate.data;
      console.log('📋 证书数据:', {
        machineId: certData.machineId ? certData.machineId.substring(0, 8) + '...' : 'undefined',
        verificationCode: certData.verificationCode,
        issuedAt: certData.issuedAt ? new Date(certData.issuedAt).toISOString() : 'undefined',
        version: certData.version,
        issuer: certData.issuer
      });

      // 2. 验证机器码匹配
      console.log('🔍 验证机器码匹配...');
      console.log('📱 当前机器码:', machineId.substring(0, 8) + '...');
      console.log('📜 证书机器码:', certData.machineId ? certData.machineId.substring(0, 8) + '...' : 'undefined');

      if (certData.machineId !== machineId) {
        console.error('❌ 机器码不匹配:', {
          expected: machineId.substring(0, 8) + '...',
          actual: certData.machineId ? certData.machineId.substring(0, 8) + '...' : 'undefined'
        });
        return false;
      }
      console.log('✅ 机器码匹配验证通过');

      // 3. 验证数字签名
      console.log('🔍 开始验证数字签名...');

      // 检查签名验证公钥是否已加载
      if (!this.serverVerifyPublicKey) {
        console.error('❌ 签名验证公钥未加载');
        return false;
      }

      const dataString = JSON.stringify(certData);
      console.log('📝 待验证数据:', dataString);
      console.log('🔑 签名数据长度:', certificate.signature.length);

      const signature = Buffer.from(certificate.signature, 'base64');
      console.log('🔑 解码后签名长度:', signature.length, 'bytes');

      console.log('🔍 使用公钥验证签名...');
      const isSignatureValid = crypto.verify(
        'sha256',
        Buffer.from(dataString),
        this.serverVerifyPublicKey,
        signature
      );

      if (!isSignatureValid) {
        console.error('❌ 证书签名验证失败');
        console.log('🔑 使用的验证公钥:', this.serverVerifyPublicKey.substring(0, 100) + '...');
        return false;
      }

      console.log('✅ 数字签名验证成功');
      console.log('🎉 证书验证完全通过');
      return true;

    } catch (error) {
      console.error('❌ 证书验证过程中发生错误:', error);
      console.error('📊 错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * 存储证书（加密存储）
   * @param {Object} certificate - 证书对象
   * @param {string} machineId - 机器码
   */
  storeCertificate(certificate, machineId) {
    try {
      // 使用机器码派生加密密钥
      const key = crypto.scryptSync(machineId, 'meea-viofo-salt-2024', 32);
      const iv = crypto.randomBytes(16);

      // 加密证书数据
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

      let encrypted = cipher.update(JSON.stringify(certificate), 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // 准备存储的数据
      const encryptedData = {
        iv: iv.toString('hex'),
        data: encrypted,
        timestamp: Date.now()
      };
      
      // 写入文件
      fs.writeFileSync(this.getLicenseFilePath(), JSON.stringify(encryptedData));
      console.log('证书已安全存储');
      
    } catch (error) {
      console.error('存储证书失败:', error);
      throw new Error('证书存储失败');
    }
  }

  /**
   * 验证本地证书
   * @param {string} machineId - 当前机器码
   * @returns {Promise<boolean>} - 证书是否有效
   */
  async validateLocalCertificate(machineId) {
    try {
      // 1. 检查证书文件是否存在
      const licenseFilePath = this.getLicenseFilePath();
      if (!fs.existsSync(licenseFilePath)) {
        console.log('本地证书文件不存在');
        return false;
      }

      // 2. 读取加密的证书数据
      const encryptedData = JSON.parse(fs.readFileSync(licenseFilePath, 'utf8'));
      
      // 3. 解密证书
      const key = crypto.scryptSync(machineId, 'meea-viofo-salt-2024', 32);

      // 检查是否有 iv 字段（新格式）
      if (!encryptedData.iv) {
        console.log('本地证书格式过旧，需要重新获取');
        return false;
      }

      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      const certificate = JSON.parse(decrypted);

      // 4. 验证证书
      return this.verifyCertificate(certificate, machineId);
      
    } catch (error) {
      console.error('验证本地证书失败:', error);
      return false;
    }
  }

  /**
   * 清除本地证书
   */
  clearCertificate() {
    try {
      const licenseFilePath = this.getLicenseFilePath();
      if (fs.existsSync(licenseFilePath)) {
        fs.unlinkSync(licenseFilePath);
        console.log('本地证书已清除');
      }
    } catch (error) {
      console.error('清除证书失败:', error);
    }
  }

  /**
   * 获取证书信息
   * @param {string} machineId - 机器码
   * @returns {Object|null} - 证书信息或null
   */
  getCertificateInfo(machineId) {
    try {
      const licenseFilePath = this.getLicenseFilePath();
      if (!fs.existsSync(licenseFilePath)) {
        return null;
      }

      const encryptedData = JSON.parse(fs.readFileSync(licenseFilePath, 'utf8'));
      const key = crypto.scryptSync(machineId, 'meea-viofo-salt-2024', 32);

      // 检查是否有 iv 字段（新格式）
      if (!encryptedData.iv) {
        console.log('本地证书格式过旧，无法读取信息');
        return null;
      }

      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      const certificate = JSON.parse(decrypted);
      
      return {
        machineId: certificate.data.machineId,
        issuedAt: new Date(certificate.data.issuedAt),
        verificationCode: certificate.data.verificationCode,
        version: certificate.data.version,
        issuer: certificate.data.issuer
      };
      
    } catch (error) {
      console.error('获取证书信息失败:', error);
      return null;
    }
  }
}

module.exports = { LicenseService };