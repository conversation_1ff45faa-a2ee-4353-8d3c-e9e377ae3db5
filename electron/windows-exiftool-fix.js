const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 安全地导入Electron app，如果不在Electron环境中则使用模拟对象
let app;
try {
  app = require('electron').app;
} catch (error) {
  // 不在Electron环境中，创建模拟对象
  app = {
    isPackaged: false,
    getPath: () => '/tmp'
  };
}

/**
 * Windows平台ExifTool专用修复
 */
class WindowsExifToolFix {
  constructor() {
    this.isWindows = process.platform === 'win32';
    this.isPackaged = app ? app.isPackaged : false;
    this.exiftoolPath = null;
    this.isInitialized = false;
  }

  /**
   * 初始化ExifTool路径
   */
  initialize() {
    if (!this.isWindows || this.isInitialized) {
      return;
    }

    console.log('🪟 初始化Windows ExifTool...');

    try {
      this.exiftoolPath = this.findExifToolPath();
      
      if (this.exiftoolPath && fs.existsSync(this.exiftoolPath)) {
        console.log('✅ 找到ExifTool:', this.exiftoolPath);
        this.verifyExifTool();
        this.isInitialized = true;
      } else {
        console.error('❌ 未找到ExifTool二进制文件');
        this.logDiagnosticInfo();
      }
    } catch (error) {
      console.error('❌ ExifTool初始化失败:', error.message);
      this.logDiagnosticInfo();
    }
  }

  /**
   * 查找ExifTool路径
   */
  findExifToolPath() {
    const possiblePaths = [];

    if (this.isPackaged) {
      // 打包环境下的可能路径
      const resourcesPath = process.resourcesPath;

      // 优先查找自定义ExifTool目录中的Perl脚本
      const arch = process.arch === 'arm64' ? 'win-arm64' : 'win-x64';
      possiblePaths.push(
        // 自定义ExifTool目录中的Perl脚本
        path.join(resourcesPath, 'exiftool', arch, 'exiftool_files', 'exiftool.pl'),
        path.join(resourcesPath, 'app.asar.unpacked', 'exiftool', arch, 'exiftool_files', 'exiftool.pl'),

        // 标准的exiftool-vendored.pl路径
        path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool'),
        path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool.exe'),
        path.join(resourcesPath, 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool'),
        path.join(path.dirname(process.execPath), 'resources', 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool')
      );
    } else {
      // 开发环境下的路径
      const arch = process.arch === 'arm64' ? 'win-arm64' : 'win-x64';
      possiblePaths.push(
        // 开发环境中的自定义ExifTool
        path.join(__dirname, '..', 'exiftool', arch, 'exiftool_files', 'exiftool.pl'),

        // 标准的node_modules路径
        path.join(__dirname, '..', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool'),
        path.join(__dirname, '..', 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool.exe'),
        path.join(process.cwd(), 'node_modules', 'exiftool-vendored.pl', 'bin', 'exiftool')
      );
    }

    console.log('🔍 搜索ExifTool路径...');
    console.log('🏗️ 当前架构:', process.arch);
    for (const testPath of possiblePaths) {
      console.log(`   测试: ${testPath} - 存在: ${fs.existsSync(testPath)}`);
      if (fs.existsSync(testPath)) {
        return testPath;
      }
    }

    // 尝试系统PATH中的ExifTool
    console.log('🔍 尝试系统PATH中的ExifTool...');
    const systemPaths = ['exiftool', 'exiftool.exe'];
    for (const systemPath of systemPaths) {
      try {
        // 测试系统PATH中是否有ExifTool
        const { execSync } = require('child_process');
        execSync(`where ${systemPath}`, { stdio: 'ignore' });
        console.log(`   找到系统ExifTool: ${systemPath}`);
        return systemPath;
      } catch (error) {
        // 系统中没有找到
      }
    }

    return null;
  }

  /**
   * 验证ExifTool是否可执行
   */
  async verifyExifTool() {
    if (!this.exiftoolPath) {
      return false;
    }

    console.log('🧪 验证ExifTool可执行性...');

    return new Promise((resolve) => {
      // 尝试多种执行方式
      const attempts = [
        // 方式1: 直接执行
        () => this.tryExecuteExifTool(['-ver'], {}),
        // 方式2: 使用cmd包装
        () => this.tryExecuteExifTool(['-ver'], { shell: true }),
        // 方式3: 使用完整路径和引号
        () => this.tryExecuteExifTool(['-ver'], { shell: true, windowsVerbatimArguments: true })
      ];

      let attemptIndex = 0;

      const tryNext = async () => {
        if (attemptIndex >= attempts.length) {
          console.log('❌ 所有执行方式都失败');
          resolve(false);
          return;
        }

        try {
          console.log(`🧪 尝试执行方式 ${attemptIndex + 1}...`);
          const result = await attempts[attemptIndex]();

          if (result.success) {
            console.log('✅ ExifTool验证成功');
            console.log(`   版本: ${result.output.trim()}`);
            resolve(true);
            return;
          }
        } catch (error) {
          console.log(`❌ 执行方式 ${attemptIndex + 1} 失败:`, error.message);
        }

        attemptIndex++;
        tryNext();
      };

      tryNext();
    });
  }

  /**
   * 尝试执行ExifTool
   */
  tryExecuteExifTool(args, options = {}) {
    return new Promise((resolve, reject) => {
      const spawnOptions = {
        stdio: 'pipe',
        timeout: 10000,
        windowsHide: true,
        ...options
      };

      // 处理路径中的空格
      const exiftoolPath = this.exiftoolPath.includes(' ') ? `"${this.exiftoolPath}"` : this.exiftoolPath;

      console.log(`🔍 执行命令: ${exiftoolPath} ${args.join(' ')}`);
      console.log(`🔍 执行选项:`, spawnOptions);

      const child = spawn(this.exiftoolPath, args, spawnOptions);

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        console.log(`📊 执行结果: 退出码=${code}, 输出长度=${stdout.length}, 错误长度=${stderr.length}`);

        if (code === 0 && stdout.trim().length > 0) {
          resolve({ success: true, output: stdout, error: stderr });
        } else {
          resolve({ success: false, output: stdout, error: stderr, code });
        }
      });

      child.on('error', (error) => {
        console.error(`❌ 进程执行错误: ${error.message} (${error.code})`);
        reject(error);
      });
    });
  }

  /**
   * 获取ExifTool路径
   */
  getExifToolPath() {
    if (!this.isInitialized) {
      this.initialize();
    }
    return this.exiftoolPath;
  }

  /**
   * 检查ExifTool是否可用
   */
  isAvailable() {
    return this.isInitialized && this.exiftoolPath && fs.existsSync(this.exiftoolPath);
  }

  /**
   * 记录诊断信息
   */
  logDiagnosticInfo() {
    console.log('\n🔍 Windows ExifTool诊断信息:');
    console.log('   平台:', process.platform);
    console.log('   架构:', process.arch);
    console.log('   打包状态:', this.isPackaged);
    console.log('   进程路径:', process.execPath);
    console.log('   工作目录:', process.cwd());

    const arch = process.arch === 'arm64' ? 'win-arm64' : 'win-x64';
    console.log('   目标架构目录:', arch);

    if (this.isPackaged) {
      console.log('   资源路径:', process.resourcesPath);

      // 检查关键目录是否存在
      const resourcesPath = process.resourcesPath;
      const asarUnpackedPath = path.join(resourcesPath, 'app.asar.unpacked');
      const nodeModulesPath = path.join(asarUnpackedPath, 'node_modules');
      const exiftoolModulePath = path.join(nodeModulesPath, 'exiftool-vendored.pl');
      const exiftoolBinPath = path.join(exiftoolModulePath, 'bin');

      // 检查自定义ExifTool目录
      const customExiftoolPath = path.join(resourcesPath, 'exiftool', arch);
      const customExiftoolFilesPath = path.join(customExiftoolPath, 'exiftool_files');

      console.log('   关键目录检查:');
      console.log(`     app.asar.unpacked: ${fs.existsSync(asarUnpackedPath)}`);
      console.log(`     node_modules: ${fs.existsSync(nodeModulesPath)}`);
      console.log(`     exiftool-vendored.pl: ${fs.existsSync(exiftoolModulePath)}`);
      console.log(`     bin: ${fs.existsSync(exiftoolBinPath)}`);
      console.log(`     自定义exiftool/${arch}: ${fs.existsSync(customExiftoolPath)}`);
      console.log(`     自定义exiftool_files: ${fs.existsSync(customExiftoolFilesPath)}`);

      // 检查自定义ExifTool文件
      if (fs.existsSync(customExiftoolFilesPath)) {
        console.log('     自定义ExifTool文件:');
        const perlExePath = path.join(customExiftoolFilesPath, 'perl.exe');
        const exiftoolPlPath = path.join(customExiftoolFilesPath, 'exiftool.pl');
        console.log(`       perl.exe: ${fs.existsSync(perlExePath)}`);
        console.log(`       exiftool.pl: ${fs.existsSync(exiftoolPlPath)}`);
      }

      if (fs.existsSync(exiftoolBinPath)) {
        console.log('     标准bin目录内容:');
        try {
          const binFiles = fs.readdirSync(exiftoolBinPath);
          binFiles.slice(0, 10).forEach(file => { // 只显示前10个文件
            const filePath = path.join(exiftoolBinPath, file);
            const stats = fs.statSync(filePath);
            console.log(`       ${file} (${stats.size} bytes)`);
          });
          if (binFiles.length > 10) {
            console.log(`       ... 还有 ${binFiles.length - 10} 个文件`);
          }
        } catch (error) {
          console.log(`       读取失败: ${error.message}`);
        }
      }
    }

    // 测试Perl解释器
    console.log('\n🐪 Perl解释器测试:');
    const perlPath = this.findPerlInterpreter();
    if (perlPath) {
      console.log(`   找到Perl: ${perlPath}`);
    } else {
      console.log('   未找到Perl解释器');
    }
  }

  /**
   * 具体的执行方法
   */
  executeWithMethod(command, args, spawnOptions) {
    return new Promise((resolve, reject) => {
      // 对于Windows上的Perl脚本，需要特殊处理
      let actualCommand = command;
      let actualArgs = args;

      // 检查是否是ExifTool Perl脚本
      if (command.includes('exiftool') && !command.endsWith('.exe')) {
        // 查找Perl解释器
        const perlPath = this.findPerlInterpreter();
        if (perlPath) {
          console.log('🐪 使用Perl解释器执行ExifTool:', perlPath);
          actualCommand = perlPath;
          actualArgs = [command, ...args];
        } else {
          console.log('⚠️ 未找到Perl解释器，尝试直接执行');
        }
      }

      const child = spawn(actualCommand, actualArgs, spawnOptions);

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 查找Perl解释器
   */
  findPerlInterpreter() {
    const arch = process.arch === 'arm64' ? 'win-arm64' : 'win-x64';

    // 首先尝试查找打包的Perl解释器
    if (this.isPackaged) {
      const resourcesPath = process.resourcesPath;
      const possiblePerlPaths = [
        // 检查自定义ExifTool目录中的Perl（优先使用当前架构）
        path.join(resourcesPath, 'exiftool', arch, 'exiftool_files', 'perl.exe'),
        // 检查app.asar.unpacked中的Perl（如果有的话）
        path.join(resourcesPath, 'app.asar.unpacked', 'exiftool', arch, 'exiftool_files', 'perl.exe'),
        // 备用：检查其他架构的Perl
        path.join(resourcesPath, 'exiftool', 'win-x64', 'exiftool_files', 'perl.exe'),
        path.join(resourcesPath, 'exiftool', 'win-arm64', 'exiftool_files', 'perl.exe')
      ];

      console.log('🔍 搜索Perl解释器，当前架构:', arch);
      for (const perlPath of possiblePerlPaths) {
        console.log(`   测试Perl路径: ${perlPath} - 存在: ${fs.existsSync(perlPath)}`);
        if (fs.existsSync(perlPath)) {
          console.log('✅ 找到打包的Perl解释器:', perlPath);
          return perlPath;
        }
      }
    } else {
      // 开发环境
      const possiblePerlPaths = [
        path.join(__dirname, '..', 'exiftool', arch, 'exiftool_files', 'perl.exe'),
        path.join(__dirname, '..', 'exiftool', 'win-x64', 'exiftool_files', 'perl.exe'),
        path.join(__dirname, '..', 'exiftool', 'win-arm64', 'exiftool_files', 'perl.exe')
      ];

      console.log('🔍 搜索开发环境Perl解释器，当前架构:', arch);
      for (const perlPath of possiblePerlPaths) {
        console.log(`   测试Perl路径: ${perlPath} - 存在: ${fs.existsSync(perlPath)}`);
        if (fs.existsSync(perlPath)) {
          console.log('✅ 找到开发环境Perl解释器:', perlPath);
          return perlPath;
        }
      }
    }

    // 尝试系统Perl
    try {
      const { execSync } = require('child_process');
      execSync('perl -v', { stdio: 'ignore' });
      console.log('✅ 找到系统Perl解释器');
      return 'perl';
    } catch (error) {
      console.log('❌ 未找到系统Perl解释器');
    }

    return null;
  }

  /**
   * 创建ExifTool包装器
   */
  createExifToolWrapper() {
    if (!this.isAvailable()) {
      throw new Error('ExifTool不可用');
    }

    const exiftoolPath = this.exiftoolPath;
    const self = this; // 保存this引用

    return {
      /**
       * 执行ExifTool命令
       */
      execute: async (args, options = {}) => {
        console.log('🔍 执行ExifTool命令:', args.slice(0, -1).join(' '), '[文件路径]');

        // 尝试多种执行方式
        const executionMethods = [
          // 方式1: 使用Perl解释器执行（推荐方式）
          () => self.executeWithMethod(exiftoolPath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 30000,
            windowsHide: true,
            ...options
          }),
          // 方式2: 使用shell执行
          () => self.executeWithMethod(exiftoolPath, args, {
            stdio: 'pipe',
            timeout: options.timeout || 30000,
            windowsHide: true,
            shell: true,
            ...options
          }),
          // 方式3: 使用cmd包装执行
          () => {
            // 对于包含空格的路径，需要特殊处理
            const quotedPath = exiftoolPath.includes(' ') ? `"${exiftoolPath}"` : exiftoolPath;
            return self.executeWithMethod('cmd', ['/c', quotedPath, ...args], {
              stdio: 'pipe',
              timeout: options.timeout || 30000,
              windowsHide: true,
              ...options
            });
          }
        ];

        let lastError = null;

        for (let i = 0; i < executionMethods.length; i++) {
          try {
            console.log(`🧪 尝试执行方法 ${i + 1}...`);
            const result = await executionMethods[i]();
            console.log('✅ ExifTool执行成功');
            return result;
          } catch (error) {
            console.log(`❌ 执行方法 ${i + 1} 失败:`, error.message);
            lastError = error;
          }
        }

        // 所有方法都失败
        console.error('❌ 所有ExifTool执行方法都失败');
        throw lastError || new Error('ExifTool执行失败');
      },



      /**
       * 提取GPS数据
       */
      extractGPS: async (filePath) => {
        try {
          const args = [
            '-j',           // JSON输出
            '-n',           // 数字格式
            '-GPS*',        // 只提取GPS数据
            '-ee',          // 提取嵌入数据
            '-G3',          // 组名格式
            '-a',           // 允许重复标签
            filePath
          ];

          const output = await this.execute(args);
          const data = JSON.parse(output);
          return Array.isArray(data) ? data[0] : data;
        } catch (error) {
          console.error('❌ GPS提取失败:', error.message);
          throw error;
        }
      }
    };
  }
}

// 创建全局实例
const windowsExifToolFix = new WindowsExifToolFix();

module.exports = {
  WindowsExifToolFix,
  windowsExifToolFix
};
