const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// 运行时修复FFmpeg权限
function fixFFmpegPermissionsAtRuntime() {
  if (!app.isPackaged) {
    return; // 开发环境不需要修复
  }

  const platform = process.platform;
  const arch = process.arch;
  
  let platformName;
  if (platform === 'darwin') {
    platformName = 'mac';
  } else if (platform === 'win32') {
    platformName = 'win';
  } else {
    platformName = 'linux';
  }

  const resourcesPath = process.resourcesPath;
  const ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${arch}`);
  
  if (!fs.existsSync(ffmpegDir)) {
    console.warn('⚠️ 运行时FFmpeg目录不存在:', ffmpegDir);
    return;
  }

  const ffmpegName = platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';
  const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe';
  
  const binaries = [
    path.join(ffmpegDir, ffmpegName),
    path.join(ffmpegDir, ffprobeName)
  ];

  binaries.forEach(binaryPath => {
    if (fs.existsSync(binaryPath)) {
      try {
        const stats = fs.statSync(binaryPath);
        const isExecutable = !!(stats.mode & parseInt('111', 8));
        
        if (!isExecutable && platform !== 'win32') {
          fs.chmodSync(binaryPath, 0o755);
          console.log('✅ 运行时修复权限:', binaryPath);
        }
      } catch (error) {
        console.error('❌ 运行时权限修复失败:', binaryPath, error.message);
      }
    }
  });
}

module.exports = { fixFFmpegPermissionsAtRuntime };