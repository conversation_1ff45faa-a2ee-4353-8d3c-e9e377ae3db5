const ffmpeg = require('fluent-ffmpeg');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// 动态获取FFmpeg和FFprobe路径
function getFFmpegPaths() {
  if (app.isPackaged) {
    // 打包后的路径 - 根据平台和架构动态选择
    const resourcesPath = process.resourcesPath;
    const platform = process.platform;
    const arch = process.arch;

    // 映射平台名称
    let platformName;
    if (platform === 'darwin') {
      platformName = 'mac';
    } else if (platform === 'win32') {
      platformName = 'win';
    } else {
      platformName = 'linux';
    }

    // 构建FFmpeg目录路径
    const ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${arch}`);

    // 根据平台确定可执行文件名
    const ffmpegName = platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';
    const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe';

    const ffmpegPath = path.join(ffmpegDir, ffmpegName);
    const ffprobePath = path.join(ffmpegDir, ffprobeName);

    return { ffmpegPath, ffprobePath };
  } else {
    // 开发环境路径
    try {
      const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
      const ffprobePath = require('@ffprobe-installer/ffprobe').path;
      return { ffmpegPath, ffprobePath };
    } catch (error) {
      console.error('❌ 开发环境FFmpeg包未安装:', error.message);
      // 回退到系统FFmpeg
      return {
        ffmpegPath: 'ffmpeg',
        ffprobePath: 'ffprobe'
      };
    }
  }
}


// 增强的路径调试
console.log('🔧 FFmpeg路径调试信息:', {
  isPackaged: app.isPackaged,
  platform: process.platform,
  arch: process.arch,
  resourcesPath: process.resourcesPath,
  __dirname,
  appPath: app.getAppPath(),
  execPath: process.execPath
});

// 尝试多个可能的路径
function findFFmpegPath(basePath, platformName, arch) {
  const possiblePaths = [
    path.join(basePath, 'ffmpeg', `${platformName}-${arch}`),
    path.join(basePath, 'app.asar.unpacked', 'ffmpeg', `${platformName}-${arch}`),
    path.join(basePath, '..', 'ffmpeg', `${platformName}-${arch}`)
  ];

  for (const testPath of possiblePaths) {
    console.log('🔧 测试路径:', testPath, '存在:', fs.existsSync(testPath));
    if (fs.existsSync(testPath)) {
      return testPath;
    }
  }
  
  return possiblePaths[0]; // 返回默认路径
}

const { ffmpegPath, ffprobePath } = getFFmpegPaths();

console.log('🔧 FFmpeg路径配置:', {
  isPackaged: app.isPackaged,
  platform: process.platform,
  arch: process.arch,
  ffmpegPath: ffmpegPath,
  ffprobePath: ffprobePath,
  ffmpegExists: fs.existsSync(ffmpegPath),
  ffprobeExists: fs.existsSync(ffprobePath)
});

// 设置FFmpeg和FFprobe路径
ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

/**
 * 使用FFprobe提取视频元数据
 * @param {string} filePath 视频文件路径
 * @returns {Promise<Object>} 元数据对象
 */
async function extractMetadata(filePath) {
  return new Promise((resolve, reject) => {
    const args = [
      '-v', 'quiet',
      '-print_format', 'json',
      '-show_format',
      '-show_streams',
      '-show_entries', 'format_tags:stream_tags',
      filePath
    ];

    const ffprobe = spawn(ffprobePath, args);
    let stdout = '';
    let stderr = '';

    ffprobe.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    ffprobe.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ffprobe.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`FFprobe failed with code ${code}: ${stderr}`));
        return;
      }

      try {
        const metadata = JSON.parse(stdout);
        resolve(metadata);
      } catch (error) {
        reject(new Error(`Failed to parse FFprobe output: ${error.message}`));
      }
    });

    ffprobe.on('error', (error) => {
      reject(new Error(`Failed to spawn FFprobe: ${error.message}`));
    });
  });
}

/**
 * 从VIOFO视频文件中提取GPS数据
 * @param {string} filePath 视频文件路径
 * @returns {Promise<Object|null>} GPS轨迹数据或null
 */
async function extractGPSData(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.error('文件不存在:', filePath);
      return null;
    }

    console.log('开始使用FFmpeg提取GPS数据:', filePath);
    
    // 提取元数据
    const metadata = await extractMetadata(filePath);
    console.log('FFmpeg提取到的原始元数据:', JSON.stringify(metadata, null, 2));

    // 解析GPS数据
    const gpsPoints = [];
    
    // 检查format级别的标签
    if (metadata.format && metadata.format.tags) {
      const formatTags = metadata.format.tags;
      console.log('Format tags:', formatTags);
      
      // 查找GPS相关的标签
      const gpsData = extractGPSFromTags(formatTags);
      if (gpsData.length > 0) {
        gpsPoints.push(...gpsData);
      }
    }

    // 检查stream级别的标签
    if (metadata.streams && Array.isArray(metadata.streams)) {
      for (const stream of metadata.streams) {
        if (stream.tags) {
          console.log(`Stream ${stream.index} tags:`, stream.tags);
          const gpsData = extractGPSFromTags(stream.tags);
          if (gpsData.length > 0) {
            gpsPoints.push(...gpsData);
          }
        }
      }
    }

    if (gpsPoints.length === 0) {
      console.log('未找到GPS数据');
      return null;
    }

    // 计算轨迹统计信息
    const statistics = calculateGPSStatistics(gpsPoints);
    const track = {
      points: gpsPoints,
      totalDistance: statistics.totalDistance,
      duration: statistics.duration,
      averageSpeed: statistics.averageSpeed,
      maxSpeed: statistics.maxSpeed,
      minSpeed: statistics.minSpeed,
      startTime: gpsPoints[0]?.timestamp,
      endTime: gpsPoints[gpsPoints.length - 1]?.timestamp,
      pointCount: gpsPoints.length,
      statistics: statistics
    };

    console.log(`FFmpeg提取到${gpsPoints.length}个GPS点`);
    return track;

  } catch (error) {
    console.error('FFmpeg提取GPS数据时出错:', error);
    return null;
  }
}

/**
 * 从标签对象中提取GPS数据
 * @param {Object} tags 标签对象
 * @returns {Array} GPS点数组
 */
function extractGPSFromTags(tags) {
  const gpsPoints = [];
  
  // 检查常见的GPS标签格式
  const gpsKeys = Object.keys(tags).filter(key => 
    key.toLowerCase().includes('gps') || 
    key.toLowerCase().includes('location') ||
    key.toLowerCase().includes('coordinate')
  );

  console.log('找到的GPS相关键:', gpsKeys);

  // 尝试解析不同格式的GPS数据
  for (const key of gpsKeys) {
    const value = tags[key];
    console.log(`解析GPS键 ${key}:`, value);
    
    // 尝试解析坐标字符串
    const coords = parseCoordinateString(value);
    if (coords) {
      gpsPoints.push(coords);
    }
  }

  // 检查是否有分离的纬度和经度字段
  const lat = findLatitudeValue(tags);
  const lng = findLongitudeValue(tags);
  
  if (lat !== null && lng !== null) {
    const point = {
      latitude: lat,
      longitude: lng,
      timestamp: findTimestampValue(tags),
      speed: findSpeedValue(tags),
      heading: findHeadingValue(tags),
      altitude: findAltitudeValue(tags)
    };
    gpsPoints.push(point);
  }

  return gpsPoints;
}

/**
 * 解析坐标字符串
 * @param {string} coordStr 坐标字符串
 * @returns {Object|null} GPS点对象或null
 */
function parseCoordinateString(coordStr) {
  if (typeof coordStr !== 'string') return null;
  
  // 尝试解析 "lat,lng" 格式
  const coordMatch = coordStr.match(/(-?\d+\.?\d*),\s*(-?\d+\.?\d*)/);
  if (coordMatch) {
    return {
      latitude: parseFloat(coordMatch[1]),
      longitude: parseFloat(coordMatch[2])
    };
  }
  
  // 尝试解析 "lat,lng,alt" 格式
  const coordAltMatch = coordStr.match(/(-?\d+\.?\d*),\s*(-?\d+\.?\d*),\s*(-?\d+\.?\d*)/);
  if (coordAltMatch) {
    return {
      latitude: parseFloat(coordAltMatch[1]),
      longitude: parseFloat(coordAltMatch[2]),
      altitude: parseFloat(coordAltMatch[3])
    };
  }
  
  return null;
}

/**
 * 查找纬度值
 */
function findLatitudeValue(tags) {
  const latKeys = ['latitude', 'lat', 'gpslatitude', 'gps_latitude'];
  for (const key of latKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) return num;
    }
  }
  return null;
}

/**
 * 查找经度值
 */
function findLongitudeValue(tags) {
  const lngKeys = ['longitude', 'lng', 'lon', 'gpslongitude', 'gps_longitude'];
  for (const key of lngKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) return num;
    }
  }
  return null;
}

/**
 * 查找时间戳值
 */
function findTimestampValue(tags) {
  const timeKeys = ['creation_time', 'date', 'datetime', 'gpsdatetime', 'gps_datetime'];
  for (const key of timeKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) return value;
  }
  return undefined;
}

/**
 * 查找速度值
 */
function findSpeedValue(tags) {
  const speedKeys = ['speed', 'gps_speed', 'gpsspeed'];
  for (const key of speedKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) return num;
    }
  }
  return undefined;
}

/**
 * 查找方向值
 */
function findHeadingValue(tags) {
  const headingKeys = ['heading', 'direction', 'gps_track', 'gpstrack', 'bearing'];
  for (const key of headingKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) return num;
    }
  }
  return undefined;
}

/**
 * 查找高度值
 */
function findAltitudeValue(tags) {
  const altKeys = ['altitude', 'alt', 'gps_altitude', 'gpsaltitude'];
  for (const key of altKeys) {
    const value = tags[key] || tags[key.toUpperCase()];
    if (value !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) return num;
    }
  }
  return undefined;
}

/**
 * 解析GPS时间戳
 * @param {string|object} timestamp 时间戳
 * @returns {Date|null} 解析后的日期对象
 */
function parseGPSTimestamp(timestamp) {
  if (!timestamp) return null;

  try {
    // 如果是对象，尝试获取rawValue或直接使用
    if (typeof timestamp === 'object') {
      timestamp = timestamp.rawValue || timestamp.toString();
    }

    // 如果是字符串，尝试解析
    if (typeof timestamp === 'string') {
      // 处理常见的GPS时间格式
      let dateStr = timestamp;

      // 将EXIF格式转换为ISO格式
      if (dateStr.includes(':') && !dateStr.includes('T')) {
        dateStr = dateStr.replace(/^(\d{4}):(\d{2}):(\d{2})/, '$1-$2-$3');
        if (!dateStr.includes('T') && dateStr.includes(' ')) {
          dateStr = dateStr.replace(' ', 'T');
        }
      }

      // 确保有时区信息
      if (!dateStr.includes('Z') && !dateStr.includes('+') && !dateStr.includes('-', 10)) {
        dateStr += 'Z';
      }

      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    }

    // 如果是数字，假设是Unix时间戳
    if (typeof timestamp === 'number') {
      return new Date(timestamp * 1000);
    }

    return null;
  } catch (error) {
    console.warn('时间戳解析失败:', timestamp, error);
    return null;
  }
}

/**
 * 计算GPS轨迹的完整统计信息
 * @param {Array} points GPS点数组
 * @returns {Object} 统计信息对象
 */
function calculateGPSStatistics(points) {
  if (!points || points.length === 0) {
    return {
      totalDistance: 0,
      duration: 0,
      averageSpeed: 0,
      maxSpeed: 0,
      minSpeed: 0,
      speedFromGPS: 0,
      speedFromDistance: 0,
      validSpeedPoints: 0,
      totalPoints: 0
    };
  }

  if (points.length === 1) {
    const singlePoint = points[0];
    return {
      totalDistance: 0,
      duration: 0,
      averageSpeed: singlePoint.speed || 0,
      maxSpeed: singlePoint.speed || 0,
      minSpeed: singlePoint.speed || 0,
      speedFromGPS: singlePoint.speed || 0,
      speedFromDistance: 0,
      validSpeedPoints: singlePoint.speed ? 1 : 0,
      totalPoints: 1
    };
  }

  // 计算总距离和时间
  let totalDistance = 0;
  let duration = 0;

  // 速度统计
  let maxSpeed = 0;
  let minSpeed = Number.MAX_VALUE;
  let totalGPSSpeed = 0;
  let validSpeedPoints = 0;

  // 计算时间范围
  const validTimePoints = points.filter(p => p.timestamp).map(p => ({
    ...p,
    parsedTime: parseGPSTimestamp(p.timestamp)
  })).filter(p => p.parsedTime);

  if (validTimePoints.length >= 2) {
    try {
      const startTime = validTimePoints[0].parsedTime;
      const endTime = validTimePoints[validTimePoints.length - 1].parsedTime;
      duration = Math.max(0, (endTime - startTime) / 1000); // 秒数

      console.log(`GPS时间范围: ${startTime.toISOString()} 到 ${endTime.toISOString()}, 持续时间: ${duration}秒`);
    } catch (error) {
      console.warn('时间解析失败:', error);
      duration = 0;
    }
  } else {
    console.log(`有效时间点数量不足: ${validTimePoints.length}, 无法计算持续时间`);
  }

  // 遍历所有点计算距离和速度统计
  for (let i = 0; i < points.length; i++) {
    const point = points[i];

    // 计算距离（从第二个点开始）
    if (i > 0) {
      const prevPoint = points[i - 1];
      if (prevPoint.latitude && prevPoint.longitude && point.latitude && point.longitude) {
        const segmentDistance = calculateDistance(
          prevPoint.latitude, prevPoint.longitude,
          point.latitude, point.longitude
        );
        totalDistance += segmentDistance;
      }
    }

    // 统计GPS记录的速度
    if (point.speed !== undefined && point.speed !== null && !isNaN(point.speed)) {
      const speed = Math.abs(point.speed); // 确保速度为正数
      totalGPSSpeed += speed;
      validSpeedPoints++;
      maxSpeed = Math.max(maxSpeed, speed);
      minSpeed = Math.min(minSpeed, speed);
    }
  }

  // 如果没有有效的速度点，重置最小速度
  if (validSpeedPoints === 0) {
    minSpeed = 0;
  }

  // 计算平均速度
  const speedFromGPS = validSpeedPoints > 0 ? totalGPSSpeed / validSpeedPoints : 0;
  const speedFromDistance = duration > 0 ? (totalDistance / 1000) / (duration / 3600) : 0; // km/h

  // 选择更可靠的平均速度
  const averageSpeed = validSpeedPoints > points.length * 0.5 ? speedFromGPS : speedFromDistance;

  return {
    totalDistance: Math.round(totalDistance), // 米
    duration: Math.round(duration), // 秒
    averageSpeed: Math.round(averageSpeed * 10) / 10, // km/h，保留1位小数
    maxSpeed: Math.round(maxSpeed * 10) / 10, // km/h，保留1位小数
    minSpeed: Math.round(minSpeed * 10) / 10, // km/h，保留1位小数
    speedFromGPS: Math.round(speedFromGPS * 10) / 10, // 从GPS记录计算的平均速度
    speedFromDistance: Math.round(speedFromDistance * 10) / 10, // 从距离计算的平均速度
    validSpeedPoints: validSpeedPoints, // 有效速度点数量
    totalPoints: points.length // 总点数
  };
}

/**
 * 计算两点间距离（米）- Haversine公式
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

module.exports = {
  extractGPSData,
  extractMetadata
};
