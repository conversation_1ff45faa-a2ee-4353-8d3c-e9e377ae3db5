const { contextBridge, ipcRenderer } = require('electron');

// 暴露受保护的方法给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件夹选择
  selectFolder: () => ipcRenderer.invoke('dialog:selectFolder'),

  // 扫描视频文件
  scanVideoFiles: (folderPath) => ipcRenderer.invoke('fs:scanVideoFiles', folderPath),

  // 获取文件信息
  getFileInfo: (filePath) => ipcRenderer.invoke('fs:getFileInfo', filePath),

  // 获取视频文件URL
  getVideoUrl: (filePath) => ipcRenderer.invoke('fs:getVideoUrl', filePath),

  // 提取GPS数据
  extractGPSData: (filePath) => ipcRenderer.invoke('fs:extractGPSData', filePath),

  // 视频缩略图
  generateThumbnails: (videoPath, options) => ipcRenderer.invoke('video:generateThumbnails', videoPath, options),
  generateThumbnail: (videoPath, timestamp, options) => ipcRenderer.invoke('video:generateThumbnail', videoPath, timestamp, options),
  getVideoDuration: (videoPath) => ipcRenderer.invoke('video:getDuration', videoPath),
  preloadThumbnails: (videoPath, options) => ipcRenderer.invoke('video:preloadThumbnails', videoPath, options),
  getCacheStats: () => ipcRenderer.invoke('video:getCacheStats'),
  cleanupCache: (maxAge) => ipcRenderer.invoke('video:cleanupCache', maxAge),

  // 视频剪辑功能
  captureFrame: (videoPath, timestamp, options) => ipcRenderer.invoke('video:captureFrame', videoPath, timestamp, options),
  clipVideo: (params) => ipcRenderer.invoke('video:clipVideo', params),
  captureMultiVideoFrame: (params) => ipcRenderer.invoke('video:captureMultiVideoFrame', params),
  clipMultiVideo: (params) => ipcRenderer.invoke('video:clipMultiVideo', params),
  getVideoInfo: (videoPath) => ipcRenderer.invoke('video:getInfo', videoPath),

  // 剪辑进度监听
  onClipProgress: (callback) => {
    ipcRenderer.on('video:clipProgress', callback);
  },
  removeClipProgressListener: (callback) => {
    ipcRenderer.removeListener('video:clipProgress', callback);
  },

  // 图片预览功能
  openImagePreview: (imagePath, imageInfo) => ipcRenderer.invoke('image:openPreview', imagePath, imageInfo),
  openImagePreviewFromBase64: (base64Data, imageInfo) => ipcRenderer.invoke('image:openPreviewFromBase64', base64Data, imageInfo),

  // 保存base64图片
  saveBase64Image: (base64Data, defaultFilename) => ipcRenderer.invoke('image:saveBase64', base64Data, defaultFilename),

  // 示例：获取应用版本
  getVersion: () => ipcRenderer.invoke('app:getVersion'),

  // 窗口控制
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),
  toggleFullscreen: () => ipcRenderer.invoke('window:toggleFullscreen'),
  isFullscreen: () => ipcRenderer.invoke('window:isFullscreen'),

  // 配置管理
  loadConfig: () => ipcRenderer.invoke('config:load'),
  saveConfig: (config) => ipcRenderer.invoke('config:save', config),
  getLastVideoFolder: () => ipcRenderer.invoke('config:getLastVideoFolder'),
  setLastVideoFolder: (folderPath) => ipcRenderer.invoke('config:setLastVideoFolder', folderPath),
  getAmapApiKey: () => ipcRenderer.invoke('config:getAmapApiKey'),
  setAmapApiKey: (apiKey) => ipcRenderer.invoke('config:setAmapApiKey', apiKey),

  // 示例：通知
  showNotification: (title, body) => ipcRenderer.invoke('notification:show', title, body),

  // 打开外部链接
  openExternal: (url) => ipcRenderer.invoke('shell:openExternal', url),

  // 文件系统相关
  showSaveDialog: (options) => ipcRenderer.invoke('dialog:showSaveDialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('dialog:showOpenDialog', options),
  showItemInFolder: (filePath) => ipcRenderer.invoke('shell:showItemInFolder', filePath),
  getSuggestedPaths: (options) => ipcRenderer.invoke('file:getSuggestedPaths', options),

  // 许可证相关
  license: {
    // 请求许可证
    request: (verificationCode) => ipcRenderer.invoke('license:request', verificationCode),
    // 验证本地许可证
    validate: () => ipcRenderer.invoke('license:validate'),
    // 获取许可证信息
    getInfo: () => ipcRenderer.invoke('license:getInfo'),
    // 清除许可证
    clear: () => ipcRenderer.invoke('license:clear'),
    // 获取机器码
    getMachineId: () => ipcRenderer.invoke('license:getMachineId')
  },

  // 日志查看器相关（调试功能）
  getLogContent: () => ipcRenderer.invoke('log:getContent'),
  openLogFolder: () => ipcRenderer.invoke('log:openFolder'),
  exportLogs: () => ipcRenderer.invoke('log:export'),
  openLogViewer: () => ipcRenderer.invoke('log:openViewer')
});

// 监听来自主进程的消息
window.addEventListener('DOMContentLoaded', () => {
  console.log('Preload script loaded');
});
