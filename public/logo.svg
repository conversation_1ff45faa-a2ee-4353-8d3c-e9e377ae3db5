<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变定义 -->
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1f2937;stop-opacity:1" />
    </linearGradient>
    
    <!-- 摄像头渐变 -->
    <linearGradient id="cameraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 圆角矩形背景 -->
  <rect width="512" height="512" rx="115" ry="115" fill="url(#backgroundGradient)"/>

  <!-- 主要图标组 -->
  <g transform="translate(256, 256)">
    
    <!-- 摄像头主体 -->
    <rect x="-200" y="-120" width="400" height="240" rx="40" ry="40" 
          fill="url(#cameraGradient)" 
          stroke="#cbd5e1" 
          stroke-width="5"/>
    
    <!-- 完整圆形轨迹 - 紧贴镜头，从12点钟顺时针到9点钟 -->
    <!-- 圆心在(60, 0)，半径120与镜头外圈紧贴 -->
    <path d="M 60 -120 A 120 120 0 1 1 -60 0"
          stroke="#ef4444"
          stroke-width="16"
          fill="none"
          opacity="0.9"/>
    
    <!-- 镜头主体 -->
    <circle cx="60" cy="0" r="120" fill="#1e293b"/>
    
    <!-- 镜头内圈 -->
    <circle cx="60" cy="0" r="90" fill="#0f172a"/>
    
    <!-- 播放按钮 -->
    <polygon points="35,-40 35,40 105,0" fill="#ffffff" opacity="0.95"/>
    
    <!-- 镜头反光 -->
    <circle cx="90" cy="-40" r="15" fill="#ffffff" opacity="0.4"/>
    
    <!-- 录制指示灯 -->
    <circle cx="-150" cy="-80" r="18" fill="#ef4444">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    

    
  </g>
</svg>
