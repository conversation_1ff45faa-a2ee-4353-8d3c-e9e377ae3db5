# 🚀 MEEA-VIOFO 快速构建指南

## 一键构建所有平台

```bash
# 1. 安装依赖
yarn install

# 2. 设置 FFmpeg
yarn setup:ffmpeg:current

# 3. 验证 FFmpeg（可选）
yarn verify:ffmpeg

# 4. 构建所有平台
yarn dist:all
```

## 🎯 常用命令

### 构建命令
```bash
# 构建所有平台和架构（推荐）
yarn dist:all

# 只构建 macOS（当前平台）
yarn dist:mac:all

# 只构建 Windows
yarn dist:win:all

# 只构建 Linux
yarn dist:linux:all

# 使用构建脚本（更多选项）
yarn build:all --help
```

### FFmpeg 管理
```bash
# 设置 FFmpeg 目录
yarn setup:ffmpeg

# 复制当前平台 FFmpeg
yarn setup:ffmpeg:current

# 验证 FFmpeg 文件
yarn verify:ffmpeg

# 清理 FFmpeg
yarn cleanup:ffmpeg
```

### 开发命令
```bash
# 开发模式
yarn dev

# 构建前端
yarn build

# 预览构建
yarn preview
```

## 📁 输出文件位置

所有构建产物位于 `dist/` 目录：

- **macOS**: `.dmg` 和 `.zip` 文件
- **Windows**: `.exe` 安装包、`.zip` 和便携版
- **Linux**: `.AppImage`、`.tar.gz`、`.deb` 和 `.rpm` 文件

## ⚡ 快速故障排除

### FFmpeg 问题
```bash
# 如果 FFmpeg 验证失败
yarn cleanup:ffmpeg
yarn setup:ffmpeg:current
yarn verify:ffmpeg
```

### 构建失败
```bash
# 清理并重新构建
yarn cleanup
yarn build
yarn dist:mac:all  # 或其他平台
```

### 内存不足
```bash
# 增加内存限制
export NODE_OPTIONS="--max-old-space-size=8192"
yarn dist:all
```

## 🔧 高级用法

### 使用构建脚本
```bash
# 查看所有选项
./scripts/build-all.sh --help

# 只构建 macOS，清理后重新设置 FFmpeg
./scripts/build-all.sh -p mac -c -s

# 预览构建命令（不实际执行）
./scripts/build-all.sh --dry-run

# 详细输出
./scripts/build-all.sh -v
```

### 环境变量
```bash
# 生产构建（禁用日志）
NODE_ENV=production yarn dist:all

# 开发构建（启用日志）
NODE_ENV=development yarn dist:all
```

## 📊 构建时间参考

在 MacBook Pro M1 上：
- 单平台：2-3 分钟
- 全平台：15-25 分钟

## 🆘 需要帮助？

1. 查看详细构建指南：`BUILD_GUIDE.md`
2. 检查 FFmpeg 设置：`yarn verify:ffmpeg`
3. 查看构建脚本帮助：`yarn build:help`
4. 提交 Issue 并附上错误日志
