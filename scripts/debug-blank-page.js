/**
 * 空白页面调试脚本
 * 在浏览器Console中运行此脚本来诊断空白页面问题
 */

(function() {
  console.log('🔧 开始空白页面诊断...\n');
  
  // 1. 基本页面信息
  console.log('📄 基本页面信息:');
  console.log('  URL:', location.href);
  console.log('  Title:', document.title);
  console.log('  Ready State:', document.readyState);
  console.log('  User Agent:', navigator.userAgent);
  console.log('');
  
  // 2. DOM 结构检查
  console.log('🏗️ DOM 结构检查:');
  const rootElement = document.getElementById('root');
  console.log('  Root element exists:', !!rootElement);
  if (rootElement) {
    console.log('  Root element children:', rootElement.children.length);
    console.log('  Root element innerHTML length:', rootElement.innerHTML.length);
  }
  console.log('  Body children:', document.body.children.length);
  console.log('  Head children:', document.head.children.length);
  console.log('');
  
  // 3. 脚本加载检查
  console.log('📜 脚本加载检查:');
  const scripts = Array.from(document.scripts);
  console.log('  Total scripts:', scripts.length);
  scripts.forEach((script, index) => {
    console.log(`  Script ${index + 1}:`, {
      src: script.src || '(inline)',
      loaded: script.readyState || 'unknown',
      type: script.type || 'text/javascript'
    });
  });
  console.log('');
  
  // 4. 样式表检查
  console.log('🎨 样式表检查:');
  const stylesheets = Array.from(document.styleSheets);
  console.log('  Total stylesheets:', stylesheets.length);
  stylesheets.forEach((sheet, index) => {
    try {
      console.log(`  Stylesheet ${index + 1}:`, {
        href: sheet.href || '(inline)',
        rules: sheet.cssRules ? sheet.cssRules.length : 'inaccessible'
      });
    } catch (e) {
      console.log(`  Stylesheet ${index + 1}: Error accessing rules`);
    }
  });
  console.log('');
  
  // 5. React 检查
  console.log('⚛️ React 检查:');
  console.log('  React available:', typeof React !== 'undefined');
  console.log('  ReactDOM available:', typeof ReactDOM !== 'undefined');
  if (typeof React !== 'undefined') {
    console.log('  React version:', React.version || 'unknown');
  }
  console.log('');
  
  // 6. 全局变量检查
  console.log('🌐 全局变量检查:');
  const globalVars = ['window', 'document', 'console', 'electronAPI'];
  globalVars.forEach(varName => {
    console.log(`  ${varName}:`, typeof window[varName]);
  });
  console.log('');
  
  // 7. 错误监听器设置
  console.log('🚨 设置错误监听器:');
  
  // JavaScript 错误
  window.addEventListener('error', (e) => {
    console.error('🚨 JavaScript Error:', {
      message: e.message,
      filename: e.filename,
      lineno: e.lineno,
      colno: e.colno,
      error: e.error
    });
  });
  
  // Promise 拒绝
  window.addEventListener('unhandledrejection', (e) => {
    console.error('🚨 Unhandled Promise Rejection:', e.reason);
  });
  
  console.log('  Error listeners set up ✅');
  console.log('');
  
  // 8. 网络请求检查
  console.log('🌐 网络请求检查:');
  if (window.performance && window.performance.getEntriesByType) {
    const resources = window.performance.getEntriesByType('resource');
    console.log('  Total resources loaded:', resources.length);
    
    const failedResources = resources.filter(r => r.transferSize === 0 && r.decodedBodySize === 0);
    if (failedResources.length > 0) {
      console.warn('  ⚠️ Potentially failed resources:');
      failedResources.forEach(r => {
        console.warn('    -', r.name);
      });
    } else {
      console.log('  All resources loaded successfully ✅');
    }
  } else {
    console.log('  Performance API not available');
  }
  console.log('');
  
  // 9. 本地存储检查
  console.log('💾 本地存储检查:');
  try {
    console.log('  localStorage available:', !!localStorage);
    console.log('  localStorage items:', localStorage.length);
    console.log('  sessionStorage available:', !!sessionStorage);
    console.log('  sessionStorage items:', sessionStorage.length);
  } catch (e) {
    console.log('  Storage access error:', e.message);
  }
  console.log('');
  
  // 10. 建议的解决方案
  console.log('💡 建议的解决方案:');
  console.log('  1. 检查上述输出中的错误信息');
  console.log('  2. 如果有脚本加载失败，检查文件是否存在');
  console.log('  3. 如果React未定义，检查React相关脚本');
  console.log('  4. 尝试强制刷新页面 (Ctrl+Shift+R)');
  console.log('  5. 清除缓存: localStorage.clear(); sessionStorage.clear();');
  console.log('');
  
  // 11. 导出调试信息
  const debugInfo = {
    timestamp: new Date().toISOString(),
    url: location.href,
    title: document.title,
    readyState: document.readyState,
    userAgent: navigator.userAgent,
    rootElement: !!document.getElementById('root'),
    scriptsCount: document.scripts.length,
    stylesheetsCount: document.styleSheets.length,
    reactAvailable: typeof React !== 'undefined',
    reactDOMAvailable: typeof ReactDOM !== 'undefined',
    electronAPIAvailable: typeof window.electronAPI !== 'undefined',
    localStorageItems: localStorage ? localStorage.length : 'unavailable',
    sessionStorageItems: sessionStorage ? sessionStorage.length : 'unavailable'
  };
  
  console.log('📋 调试信息摘要:');
  console.log(JSON.stringify(debugInfo, null, 2));
  console.log('');
  console.log('🔧 诊断完成！请检查上述输出中的问题。');
  
  // 返回调试信息供进一步使用
  return debugInfo;
})();
