#!/bin/bash

# MEEA-VIOFO 快速构建脚本
# 用于快速构建常用的平台组合

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${WHITE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${WHITE}📦 MEEA-VIOFO 快速构建脚本${NC}\n"
    echo -e "${YELLOW}用法:${NC}"
    echo "  ./scripts/quick-build.sh [选项]"
    echo ""
    echo -e "${CYAN}选项:${NC}"
    echo "  win-x64      🪟 构建 Windows x64"
    echo "  win-arm64    🪟 构建 Windows ARM64"
    echo "  mac-x64      🍎 构建 macOS Intel"
    echo "  mac-arm64    🍎 构建 macOS Apple Silicon"
    echo "  linux-x64    🐧 构建 Linux x64"
    echo "  linux-arm64  🐧 构建 Linux ARM64"
    echo ""
    echo "  windows      🪟 构建所有 Windows 版本"
    echo "  macos        🍎 构建所有 macOS 版本"
    echo "  linux        🐧 构建所有 Linux 版本"
    echo ""
    echo "  all          📦 构建所有平台"
    echo "  desktop      💻 构建桌面平台 (Windows + macOS)"
    echo ""
    echo -e "${GREEN}示例:${NC}"
    echo "  ./scripts/quick-build.sh win-x64"
    echo "  ./scripts/quick-build.sh macos"
    echo "  ./scripts/quick-build.sh desktop"
    echo ""
}

# 检查依赖
check_dependencies() {
    log "检查构建依赖..."
    
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v yarn &> /dev/null; then
        error "Yarn 未安装"
        exit 1
    fi
    
    success "依赖检查通过"
}

# 清理旧的构建产物
cleanup_old_builds() {
    log "清理旧的构建产物..."
    
    if [ -d "dist" ]; then
        rm -rf dist/*.exe dist/*.dmg dist/*.AppImage dist/*.tar.gz dist/*.deb dist/*.rpm 2>/dev/null || true
        success "清理完成"
    else
        info "无需清理"
    fi
}

# 执行构建命令
execute_build() {
    local command=$1
    local description=$2
    
    log "开始构建: $description"
    info "执行命令: $command"
    
    start_time=$(date +%s)
    
    if eval "$command"; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        success "构建完成: $description (耗时: ${duration}s)"
        return 0
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        error "构建失败: $description (耗时: ${duration}s)"
        return 1
    fi
}

# 显示构建结果
show_results() {
    log "构建结果总结"
    
    if [ -d "dist" ]; then
        echo -e "\n${CYAN}📁 构建产物位置:${NC}"
        echo "  $(pwd)/dist"
        
        echo -e "\n${CYAN}📦 生成的安装包:${NC}"
        find dist -name "MEEA-VIOFO*" \( -name "*.exe" -o -name "*.dmg" -o -name "*.AppImage" \) -exec ls -lh {} \; 2>/dev/null | while read -r line; do
            filename=$(echo "$line" | awk '{print $9}')
            size=$(echo "$line" | awk '{print $5}')
            echo "  📄 $(basename "$filename") ($size)"
        done
    fi
}

# 主构建函数
main() {
    local target=${1:-help}
    
    case $target in
        "help"|"-h"|"--help"|"")
            show_help
            exit 0
            ;;
        "win-x64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:win-x64" "Windows x64"
            ;;
        "win-arm64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:win-arm64" "Windows ARM64"
            ;;
        "mac-x64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:mac-x64" "macOS Intel"
            ;;
        "mac-arm64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
            ;;
        "linux-x64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:linux-x64" "Linux x64"
            ;;
        "linux-arm64")
            check_dependencies
            cleanup_old_builds
            execute_build "yarn dist:linux-arm64" "Linux ARM64"
            ;;
        "windows")
            check_dependencies
            cleanup_old_builds
            log "构建所有 Windows 版本"
            execute_build "yarn dist:win-x64" "Windows x64"
            execute_build "yarn dist:win-arm64" "Windows ARM64"
            ;;
        "macos")
            check_dependencies
            cleanup_old_builds
            log "构建所有 macOS 版本"
            execute_build "yarn dist:mac-x64" "macOS Intel"
            execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
            ;;
        "linux")
            check_dependencies
            cleanup_old_builds
            log "构建所有 Linux 版本"
            execute_build "yarn dist:linux-x64" "Linux x64"
            execute_build "yarn dist:linux-arm64" "Linux ARM64"
            ;;
        "desktop")
            check_dependencies
            cleanup_old_builds
            log "构建桌面平台 (Windows + macOS)"
            execute_build "yarn dist:win-x64" "Windows x64"
            execute_build "yarn dist:mac-x64" "macOS Intel"
            execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
            ;;
        "all")
            check_dependencies
            cleanup_old_builds
            log "构建所有平台"
            execute_build "yarn dist:win-x64" "Windows x64"
            execute_build "yarn dist:win-arm64" "Windows ARM64"
            execute_build "yarn dist:mac-x64" "macOS Intel"
            execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
            execute_build "yarn dist:linux-x64" "Linux x64"
            execute_build "yarn dist:linux-arm64" "Linux ARM64"
            ;;
        *)
            error "未知的构建目标: $target"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    show_results
}

# 运行主函数
main "$@"
