#!/usr/bin/env python3
"""
FFmpeg 高级浏览器模拟下载脚本
使用 Selenium 真正模拟人类浏览器行为下载 FFmpeg 和 FFprobe
"""

import os
import sys
import time
import random
import shutil
import zipfile
import tarfile
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"
DOWNLOADS_DIR = TEMP_DIR / "downloads"

def setup_chrome_driver(headless=False):
    """设置Chrome浏览器驱动，模拟真实用户"""
    print("🚀 启动Chrome浏览器...")

    chrome_options = Options()

    # 设置下载目录
    prefs = {
        "download.default_directory": str(DOWNLOADS_DIR.absolute()),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
        "profile.default_content_settings.popups": 0,
        "profile.default_content_setting_values.automatic_downloads": 1
    }
    chrome_options.add_experimental_option("prefs", prefs)

    # 网络优化设置
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--no-proxy-server")
    chrome_options.add_argument("--disable-proxy-certificate-handler")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")

    # 模拟真实用户的浏览器设置
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # 随机窗口大小，模拟不同用户
    window_sizes = [(1920, 1080), (1366, 768), (1440, 900), (1536, 864)]
    width, height = random.choice(window_sizes)
    chrome_options.add_argument(f"--window-size={width},{height}")

    # 设置用户代理
    user_agents = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")

    # 是否使用无头模式
    if headless:
        chrome_options.add_argument("--headless")
        print("🔇 使用无头模式（不显示浏览器窗口）")
    else:
        print("🖥️ 使用有头模式（将显示浏览器窗口）")

    try:
        # 尝试使用webdriver-manager自动管理ChromeDriver
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("✅ 使用webdriver-manager自动管理ChromeDriver")
        except ImportError:
            # 回退到系统ChromeDriver
            driver = webdriver.Chrome(options=chrome_options)
            print("✅ 使用系统ChromeDriver")

        # 设置页面加载超时
        driver.set_page_load_timeout(60)
        driver.implicitly_wait(10)

        # 执行脚本来隐藏webdriver属性
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")

        print(f"✅ Chrome浏览器启动成功 (窗口大小: {width}x{height})")
        print(f"📁 下载目录: {DOWNLOADS_DIR}")

        return driver

    except Exception as e:
        print(f"❌ Chrome浏览器启动失败: {e}")
        print("💡 请确保已安装Chrome浏览器和ChromeDriver")
        print("   macOS安装方法:")
        print("     brew install --cask google-chrome")
        print("     brew install chromedriver")
        print("   或者安装webdriver-manager:")
        print("     pip install webdriver-manager")
        return None

def human_like_scroll(driver, duration=2):
    """模拟人类滚动页面的行为"""
    print("📜 模拟人类滚动页面...")
    
    # 获取页面高度
    page_height = driver.execute_script("return document.body.scrollHeight")
    viewport_height = driver.execute_script("return window.innerHeight")
    
    # 随机滚动次数
    scroll_count = random.randint(3, 8)
    
    for i in range(scroll_count):
        # 随机滚动距离
        scroll_distance = random.randint(200, 500)
        
        # 执行滚动
        driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
        
        # 随机停顿时间
        pause_time = random.uniform(0.5, 1.5)
        time.sleep(pause_time)
    
    # 最后滚动到顶部
    driver.execute_script("window.scrollTo(0, 0);")
    time.sleep(1)

def human_like_click(driver, element):
    """模拟人类点击行为"""
    print("🖱️ 模拟人类点击...")
    
    # 先移动到元素附近
    actions = ActionChains(driver)
    actions.move_to_element(element)
    
    # 随机偏移一点，模拟不精确的点击
    offset_x = random.randint(-5, 5)
    offset_y = random.randint(-5, 5)
    actions.move_by_offset(offset_x, offset_y)
    
    # 短暂停顿，模拟瞄准时间
    time.sleep(random.uniform(0.3, 0.8))
    
    # 点击
    actions.click()
    actions.perform()
    
    # 点击后的短暂停顿
    time.sleep(random.uniform(0.5, 1.2))

def download_windows_ffmpeg_selenium(driver):
    """使用Selenium下载Windows FFmpeg"""
    print("\n🪟 使用浏览器下载 Windows FFmpeg...")
    
    try:
        # 访问gyan.dev
        print("🌐 访问 gyan.dev...")
        driver.get("https://www.gyan.dev/ffmpeg/builds/")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 模拟人类浏览页面
        time.sleep(random.uniform(2, 4))
        human_like_scroll(driver)
        
        # 寻找下载链接
        print("🔍 寻找FFmpeg下载链接...")
        
        # 尝试多种选择器来找到下载链接
        selectors = [
            "a[href*='ffmpeg'][href*='release-essentials.zip']",
            "a[href*='ffmpeg'][href*='release'][href$='.zip']",
            "a[href*='ffmpeg'][href$='.zip']"
        ]
        
        download_link = None
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    download_link = elements[0]
                    break
            except NoSuchElementException:
                continue
        
        if not download_link:
            print("❌ 未找到下载链接")
            return False
        
        download_url = download_link.get_attribute('href')
        print(f"✅ 找到下载链接: {download_url}")
        
        # 模拟人类思考时间
        time.sleep(random.uniform(1, 3))
        
        # 点击下载
        print("📥 开始下载...")
        human_like_click(driver, download_link)
        
        # 等待下载开始
        time.sleep(5)
        
        # 等待下载完成
        print("⏳ 等待下载完成...")
        max_wait_time = 300  # 最多等待5分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 检查下载目录中是否有zip文件
            zip_files = list(DOWNLOADS_DIR.glob("*.zip"))
            if zip_files:
                # 检查文件是否还在下载中（.crdownload文件）
                downloading_files = list(DOWNLOADS_DIR.glob("*.crdownload"))
                if not downloading_files:
                    print("✅ 下载完成!")
                    return zip_files[0]
            
            time.sleep(2)
            print(".", end="", flush=True)
        
        print("\n❌ 下载超时")
        return False
        
    except Exception as e:
        print(f"❌ 下载Windows FFmpeg失败: {e}")
        return False

def ensure_dir(path):
    """确保目录存在"""
    path.mkdir(parents=True, exist_ok=True)
    print(f"✅ 确保目录存在: {path}")

def main():
    """主函数"""
    print("🚀 FFmpeg 高级浏览器模拟下载脚本")
    print("=" * 60)
    
    # 创建目录结构
    ensure_dir(FFMPEG_DIR)
    ensure_dir(TEMP_DIR)
    ensure_dir(DOWNLOADS_DIR)
    
    platforms = ["win-x64", "win-arm64", "mac-x64", "mac-arm64", "linux-x64", "linux-arm64"]
    for platform in platforms:
        ensure_dir(FFMPEG_DIR / platform)
    
    # 启动浏览器
    driver = setup_chrome_driver()
    if not driver:
        print("❌ 无法启动浏览器，退出程序")
        sys.exit(1)
    
    try:
        success_count = 0
        
        # 下载Windows FFmpeg
        if download_windows_ffmpeg_selenium(driver):
            success_count += 1
            print("✅ Windows FFmpeg 下载成功")
        
        print(f"\n📊 下载结果: {success_count}/1 个任务完成")
        
        if success_count > 0:
            print("🎉 部分下载任务完成！")
            print("\n📝 下一步:")
            print("  1. 检查下载的文件")
            print("  2. 解压并复制到对应目录")
            print("  3. 运行: python scripts/download_ffmpeg.py")
        else:
            print("❌ 所有下载任务失败")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
    
    finally:
        # 关闭浏览器
        print("\n🔚 关闭浏览器...")
        driver.quit()
        print("✅ 浏览器已关闭")

if __name__ == "__main__":
    main()
