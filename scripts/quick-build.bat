@echo off
setlocal enabledelayedexpansion

REM MEEA-VIOFO 快速构建脚本 (Windows)
REM 用于快速构建常用的平台组合

title MEEA-VIOFO 构建工具

REM 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

REM 获取当前时间
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"

REM 日志函数
:log
echo %WHITE%[%HH%:%Min%:%Sec%]%NC% %~1
goto :eof

:success
echo %GREEN%✅ %~1%NC%
goto :eof

:error
echo %RED%❌ %~1%NC%
goto :eof

:warning
echo %YELLOW%⚠️  %~1%NC%
goto :eof

:info
echo %BLUE%ℹ️  %~1%NC%
goto :eof

REM 显示帮助信息
:show_help
echo %WHITE%📦 MEEA-VIOFO 快速构建脚本%NC%
echo.
echo %YELLOW%用法:%NC%
echo   quick-build.bat [选项]
echo.
echo %CYAN%选项:%NC%
echo   win-x64      🪟 构建 Windows x64
echo   win-arm64    🪟 构建 Windows ARM64
echo   mac-x64      🍎 构建 macOS Intel
echo   mac-arm64    🍎 构建 macOS Apple Silicon
echo   linux-x64    🐧 构建 Linux x64
echo   linux-arm64  🐧 构建 Linux ARM64
echo.
echo   windows      🪟 构建所有 Windows 版本
echo   macos        🍎 构建所有 macOS 版本
echo   linux        🐧 构建所有 Linux 版本
echo.
echo   all          📦 构建所有平台
echo   desktop      💻 构建桌面平台 (Windows + macOS)
echo.
echo %GREEN%示例:%NC%
echo   quick-build.bat win-x64
echo   quick-build.bat macos
echo   quick-build.bat desktop
echo.
goto :eof

REM 检查依赖
:check_dependencies
call :log "检查构建依赖..."

where node >nul 2>nul
if %errorlevel% neq 0 (
    call :error "Node.js 未安装"
    exit /b 1
)

where yarn >nul 2>nul
if %errorlevel% neq 0 (
    call :error "Yarn 未安装"
    exit /b 1
)

call :success "依赖检查通过"
goto :eof

REM 清理旧的构建产物
:cleanup_old_builds
call :log "清理旧的构建产物..."

if exist "dist" (
    del /q "dist\*.exe" 2>nul
    del /q "dist\*.dmg" 2>nul
    del /q "dist\*.AppImage" 2>nul
    del /q "dist\*.tar.gz" 2>nul
    del /q "dist\*.deb" 2>nul
    del /q "dist\*.rpm" 2>nul
    call :success "清理完成"
) else (
    call :info "无需清理"
)
goto :eof

REM 执行构建命令
:execute_build
set "command=%~1"
set "description=%~2"

call :log "开始构建: %description%"
call :info "执行命令: %command%"

set "start_time=%time%"

call %command%
set "build_result=%errorlevel%"

set "end_time=%time%"

if %build_result% equ 0 (
    call :success "构建完成: %description%"
) else (
    call :error "构建失败: %description%"
)

goto :eof

REM 显示构建结果
:show_results
call :log "构建结果总结"

if exist "dist" (
    echo.
    echo %CYAN%📁 构建产物位置:%NC%
    echo   %cd%\dist
    echo.
    echo %CYAN%📦 生成的安装包:%NC%
    
    for %%f in (dist\MEEA-VIOFO*.exe dist\MEEA-VIOFO*.dmg dist\MEEA-VIOFO*.AppImage) do (
        if exist "%%f" (
            for %%s in ("%%f") do (
                set "size=%%~zs"
                set /a "size_mb=!size!/1024/1024"
                echo   📄 %%~nxf (!size_mb! MB)
            )
        )
    )
)
goto :eof

REM 主函数
:main
set "target=%~1"

if "%target%"=="" set "target=help"
if "%target%"=="help" goto show_help
if "%target%"=="-h" goto show_help
if "%target%"=="--help" goto show_help

if "%target%"=="win-x64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:win-x64" "Windows x64"
    goto show_results
)

if "%target%"=="win-arm64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:win-arm64" "Windows ARM64"
    goto show_results
)

if "%target%"=="mac-x64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:mac-x64" "macOS Intel"
    goto show_results
)

if "%target%"=="mac-arm64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
    goto show_results
)

if "%target%"=="linux-x64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:linux-x64" "Linux x64"
    goto show_results
)

if "%target%"=="linux-arm64" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :execute_build "yarn dist:linux-arm64" "Linux ARM64"
    goto show_results
)

if "%target%"=="windows" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :log "构建所有 Windows 版本"
    call :execute_build "yarn dist:win-x64" "Windows x64"
    call :execute_build "yarn dist:win-arm64" "Windows ARM64"
    goto show_results
)

if "%target%"=="macos" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :log "构建所有 macOS 版本"
    call :execute_build "yarn dist:mac-x64" "macOS Intel"
    call :execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
    goto show_results
)

if "%target%"=="linux" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :log "构建所有 Linux 版本"
    call :execute_build "yarn dist:linux-x64" "Linux x64"
    call :execute_build "yarn dist:linux-arm64" "Linux ARM64"
    goto show_results
)

if "%target%"=="desktop" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :log "构建桌面平台 (Windows + macOS)"
    call :execute_build "yarn dist:win-x64" "Windows x64"
    call :execute_build "yarn dist:mac-x64" "macOS Intel"
    call :execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
    goto show_results
)

if "%target%"=="all" (
    call :check_dependencies
    if %errorlevel% neq 0 exit /b 1
    call :cleanup_old_builds
    call :log "构建所有平台"
    call :execute_build "yarn dist:win-x64" "Windows x64"
    call :execute_build "yarn dist:win-arm64" "Windows ARM64"
    call :execute_build "yarn dist:mac-x64" "macOS Intel"
    call :execute_build "yarn dist:mac-arm64" "macOS Apple Silicon"
    call :execute_build "yarn dist:linux-x64" "Linux x64"
    call :execute_build "yarn dist:linux-arm64" "Linux ARM64"
    goto show_results
)

call :error "未知的构建目标: %target%"
echo.
goto show_help

:show_results
call :show_results
goto :eof

REM 运行主函数
call :main %1

pause
