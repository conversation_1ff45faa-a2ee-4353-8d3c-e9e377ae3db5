#!/usr/bin/env node

/**
 * 获取当前应用版本信息的工具脚本
 */

const fs = require('fs');
const path = require('path');

function getCurrentVersion() {
  try {
    const buildInfoPath = path.join(__dirname, '..', 'electron', 'build-info.json');
    if (fs.existsSync(buildInfoPath)) {
      const buildInfo = JSON.parse(fs.readFileSync(buildInfoPath, 'utf8'));
      return buildInfo;
    }
  } catch (error) {
    console.warn('⚠️ 无法读取构建信息:', error.message);
  }
  
  // 回退到package.json
  try {
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return {
      version: packageJson.version,
      name: packageJson.name,
      buildTime: 'unknown'
    };
  } catch (error) {
    console.error('❌ 无法获取版本信息:', error.message);
    return null;
  }
}

function main() {
  const versionInfo = getCurrentVersion();
  
  if (!versionInfo) {
    console.error('❌ 无法获取版本信息');
    process.exit(1);
  }
  
  console.log('📊 当前版本信息:');
  console.log('   应用名称:', versionInfo.name || 'unknown');
  console.log('   版本号:', versionInfo.version);
  console.log('   构建时间:', versionInfo.buildDateTime || versionInfo.buildTime || 'unknown');
  console.log('   Git提交:', versionInfo.gitHash || 'unknown');
  console.log('   Git分支:', versionInfo.gitBranch || 'unknown');
  
  return versionInfo;
}

if (require.main === module) {
  main();
}

module.exports = { getCurrentVersion };
