#!/usr/bin/env node

/**
 * FFmpeg 路径修复脚本
 * 确保所有使用 FFmpeg 的文件都使用正确的路径逻辑
 */

const fs = require('fs');
const path = require('path');

const projectRoot = path.join(__dirname, '..');

// 需要检查的文件列表
const filesToCheck = [
  'electron/thumbnail-generator.js',
  'electron/ffmpeg-gps-extractor.js',
  'electron/main.js'
];

// FFmpeg 路径获取函数模板
const ffmpegPathTemplate = `
// 获取 FFmpeg 路径的函数
function getFFmpegPaths() {
  const isDev = process.env.NODE_ENV === 'development';
  const isPackaged = app.isPackaged;
  
  if (isDev && !isPackaged) {
    // 开发环境：尝试使用 npm 包
    try {
      const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
      const ffprobePath = require('@ffprobe-installer/ffprobe').path;
      return { ffmpegPath, ffprobePath };
    } catch (error) {
      console.warn('开发环境 FFmpeg 包未找到，使用打包版本');
    }
  }
  
  // 生产环境或开发环境回退：使用打包的 FFmpeg 文件
  const platform = process.platform;
  const arch = process.arch;
  
  let platformName;
  if (platform === 'darwin') {
    platformName = 'mac';
  } else if (platform === 'win32') {
    platformName = 'win';
  } else {
    platformName = 'linux';
  }
  
  const ffmpegDir = isPackaged 
    ? path.join(process.resourcesPath, 'ffmpeg', \`\${platformName}-\${arch}\`)
    : path.join(__dirname, '..', 'ffmpeg', \`\${platformName}-\${arch}\`);
  
  const ffmpegExt = platform === 'win32' ? '.exe' : '';
  const ffmpegPath = path.join(ffmpegDir, \`ffmpeg\${ffmpegExt}\`);
  const ffprobePath = path.join(ffmpegDir, \`ffprobe\${ffmpegExt}\`);
  
  return { ffmpegPath, ffprobePath };
}
`;

function checkFile(filePath) {
  const fullPath = path.join(projectRoot, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  
  // 检查是否直接引用了 @ffmpeg-installer
  const hasDirectImport = content.includes("require('@ffmpeg-installer/ffmpeg')") && 
                         !content.includes('try {') && 
                         !content.includes('catch');
  
  if (hasDirectImport) {
    console.log(`❌ ${filePath}: 发现直接引用 @ffmpeg-installer，需要修复`);
    return false;
  }
  
  // 检查是否有正确的路径处理逻辑
  const hasPathLogic = content.includes('getFFmpegPaths') || 
                      content.includes('app.isPackaged') ||
                      content.includes('process.resourcesPath');
  
  if (!hasPathLogic) {
    console.log(`⚠️  ${filePath}: 缺少 FFmpeg 路径处理逻辑`);
    return false;
  }
  
  console.log(`✅ ${filePath}: FFmpeg 路径处理正确`);
  return true;
}

function main() {
  console.log('🔍 检查 FFmpeg 路径配置...\n');
  
  let allGood = true;
  
  for (const file of filesToCheck) {
    if (!checkFile(file)) {
      allGood = false;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allGood) {
    console.log('✅ 所有文件的 FFmpeg 路径配置都正确！');
    console.log('\n💡 建议测试步骤:');
    console.log('1. yarn build');
    console.log('2. yarn dist:mac');
    console.log('3. 测试生成的应用是否能正常启动');
  } else {
    console.log('❌ 发现 FFmpeg 路径配置问题！');
    console.log('\n🔧 修复建议:');
    console.log('1. 确保所有 FFmpeg 相关文件都使用动态路径');
    console.log('2. 在生产环境中使用 process.resourcesPath');
    console.log('3. 在开发环境中优雅降级到 npm 包或本地文件');
    console.log('\n📝 参考模板:');
    console.log(ffmpegPathTemplate);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkFile, ffmpegPathTemplate };
