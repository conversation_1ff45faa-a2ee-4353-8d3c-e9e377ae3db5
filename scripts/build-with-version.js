#!/usr/bin/env node

/**
 * 动态版本构建脚本
 * 在构建时动态获取最新版本号并应用到构建过程中
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function getLatestVersion() {
  console.log('🔍 获取最新版本号...');
  
  try {
    // 使用版本生成脚本获取最新版本号
    const version = execSync('node scripts/generate-build-version.js --output-only', { 
      encoding: 'utf8',
      cwd: path.join(__dirname, '..')
    }).trim();
    
    console.log('✅ 获取到版本号:', version);
    return version;
  } catch (error) {
    console.error('❌ 获取版本号失败:', error.message);
    throw error;
  }
}

function updateBuildInfo(version) {
  console.log('📝 更新构建信息...');
  
  try {
    // 执行版本生成脚本，使用指定的版本号
    execSync(`node scripts/generate-build-version.js --version ${version}`, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('✅ 构建信息已更新');
  } catch (error) {
    console.error('❌ 更新构建信息失败:', error.message);
    throw error;
  }
}

function runBuild(target = 'all') {
  console.log(`🔨 开始构建 ${target} 版本...`);
  
  try {
    let buildCommand;
    
    switch (target.toLowerCase()) {
      case 'win':
      case 'windows':
        buildCommand = 'yarn build && electron-builder --win --publish=never';
        break;
      case 'mac':
      case 'macos':
        buildCommand = 'yarn build && electron-builder --mac --publish=never';
        break;
      case 'linux':
        buildCommand = 'yarn build && electron-builder --linux --publish=never';
        break;
      case 'all':
        buildCommand = 'yarn build && electron-builder --mac --win --linux --publish=never';
        break;
      default:
        throw new Error(`不支持的构建目标: ${target}`);
    }
    
    console.log(`📋 执行命令: ${buildCommand}`);
    
    execSync(buildCommand, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log(`✅ ${target} 构建完成！`);
    return true;
  } catch (error) {
    console.error(`❌ ${target} 构建失败:`, error.message);
    return false;
  }
}

function showBuildResults(version) {
  console.log('\n📊 构建结果检查...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distDir)) {
    console.error('❌ dist目录不存在');
    return;
  }
  
  const files = fs.readdirSync(distDir);
  const buildFiles = files.filter(file => 
    file.includes(version) && (
      file.endsWith('.exe') || 
      file.endsWith('.zip') || 
      file.endsWith('.dmg') ||
      file.endsWith('.AppImage') ||
      file.endsWith('.deb') ||
      file.endsWith('.rpm')
    )
  );
  
  if (buildFiles.length > 0) {
    console.log('✅ 找到构建文件:');
    buildFiles.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
      console.log(`   📦 ${file} (${sizeMB} MB)`);
    });
  } else {
    console.warn('⚠️ 未找到包含版本号的构建文件');
    console.log('📁 dist目录内容:');
    files.forEach(file => {
      console.log(`   📄 ${file}`);
    });
  }
}

function main() {
  const args = process.argv.slice(2);
  const target = args[0] || 'all';
  const customVersion = args.find(arg => arg.startsWith('--version='))?.split('=')[1];
  
  console.log('🚀 动态版本构建工具');
  console.log('====================');
  
  if (!['win', 'windows', 'mac', 'macos', 'linux', 'all'].includes(target.toLowerCase())) {
    console.error('❌ 无效的构建目标:', target);
    console.log('💡 支持的目标: win, mac, linux, all');
    process.exit(1);
  }
  
  try {
    // 获取版本号
    const version = customVersion || getLatestVersion();
    console.log('🏷️ 使用版本号:', version);
    
    // 更新构建信息
    updateBuildInfo(version);
    
    // 执行构建
    const success = runBuild(target);
    
    if (success) {
      // 显示构建结果
      showBuildResults(version);
      
      console.log('\n🎉 构建完成！');
      console.log('📋 后续步骤:');
      console.log('1. 检查生成的安装包文件');
      console.log('2. 测试安装和运行');
      console.log('3. 验证版本号是否正确显示');
    } else {
      console.error('\n❌ 构建失败！');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 构建过程出错:', error.message);
    process.exit(1);
  }
}

function showUsage() {
  console.log(`
📋 动态版本构建工具使用说明:

用法: node scripts/build-with-version.js [目标] [选项]

目标:
  win, windows  - 构建Windows版本
  mac, macos    - 构建macOS版本
  linux         - 构建Linux版本
  all           - 构建所有平台版本 (默认)

选项:
  --version=<版本号>  - 指定自定义版本号

示例:
  node scripts/build-with-version.js                    # 自动生成版本号并构建所有平台
  node scripts/build-with-version.js win                # 自动生成版本号并构建Windows版本
  node scripts/build-with-version.js mac --version=25.07.18-210000  # 指定版本号构建macOS版本
`);
}

if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
  } else {
    main();
  }
}

module.exports = {
  getLatestVersion,
  updateBuildInfo,
  runBuild,
  showBuildResults
};
