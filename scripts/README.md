# MEEA-VIOFO 构建脚本

本目录包含 MEEA-VIOFO 项目的构建脚本，专为 Linux 环境下的多平台构建优化。

## 🚀 主要脚本

### 构建脚本
- `build-all-platforms.sh` - **一键构建所有平台**（推荐）
- `cleanup-space.sh` - 磁盘空间清理工具

## 📦 使用方法

### 一键构建所有平台（推荐）
```bash
# 使用 yarn 命令
yarn build:all
```

### 单平台构建
```bash
yarn build:mac      # 构建 macOS
yarn build:windows  # 构建 Windows
yarn build:linux    # 构建 Linux
```

### 磁盘空间管理
```bash
yarn cleanup        # 清理磁盘空间
```

## 🎯 构建特性

### 智能空间管理
- 自动检测磁盘空间
- 构建过程中实时清理临时文件
- 优化内存和存储使用

### 构建优先级
1. **macOS** - 优先构建
2. **Windows** - 次优先
3. **Linux** - 最后构建

## 💾 磁盘空间要求
- **最小**: 2GB 可用空间
- **推荐**: 8GB+ 可用空间

## 📁 构建产物

构建完成后，安装包位于 `release/` 目录：
- `*.dmg` - macOS 安装包
- `*.exe` - Windows 安装包
- `*.AppImage` - Linux AppImage
- `*.deb` - Linux DEB 包
- `*.rpm` - Linux RPM 包

## ⚠️ 注意事项

1. **仅支持 Linux 环境**
2. **确保依赖完整**：Node.js、Yarn、Electron Builder
3. **网络要求**：首次构建需下载 Electron 二进制文件
4. **权限要求**：确保脚本有执行权限
