#!/bin/bash

# macOS 应用程序修复脚本
# 解决 "应用程序已损坏" 问题

set -e

APP_NAME="MEEA-VIOFO"
DOWNLOADS_DIR="$HOME/Downloads"
APPLICATIONS_DIR="/Applications"

echo "🍎 MEEA-VIOFO macOS 修复工具"
echo "================================"

# 检查是否为 macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ 此脚本仅适用于 macOS 系统"
    exit 1
fi

# 函数：查找下载的文件
find_downloaded_files() {
    echo "🔍 查找下载的文件..."
    
    # 查找 DMG 文件
    DMG_FILES=($(find "$DOWNLOADS_DIR" -name "${APP_NAME}*.dmg" -type f 2>/dev/null))
    
    # 查找 APP 文件
    APP_FILES=($(find "$DOWNLOADS_DIR" -name "${APP_NAME}*.app" -type d 2>/dev/null))
    APP_FILES+=($(find "$APPLICATIONS_DIR" -name "${APP_NAME}*.app" -type d 2>/dev/null))
    
    if [ ${#DMG_FILES[@]} -eq 0 ] && [ ${#APP_FILES[@]} -eq 0 ]; then
        echo "❌ 未找到 ${APP_NAME} 相关文件"
        echo "请确保已下载应用程序到下载文件夹"
        exit 1
    fi
    
    echo "✅ 找到以下文件:"
    for file in "${DMG_FILES[@]}"; do
        echo "  📦 DMG: $(basename "$file")"
    done
    for file in "${APP_FILES[@]}"; do
        echo "  📱 APP: $(basename "$file")"
    done
}

# 函数：移除隔离属性
remove_quarantine() {
    echo ""
    echo "🔧 移除隔离属性..."
    
    # 处理 DMG 文件
    for file in "${DMG_FILES[@]}"; do
        echo "  处理: $(basename "$file")"
        if xattr -l "$file" 2>/dev/null | grep -q "com.apple.quarantine"; then
            sudo xattr -rd com.apple.quarantine "$file"
            echo "  ✅ 已移除 DMG 隔离属性"
        else
            echo "  ℹ️  DMG 文件无隔离属性"
        fi
    done
    
    # 处理 APP 文件
    for file in "${APP_FILES[@]}"; do
        echo "  处理: $(basename "$file")"
        if xattr -l "$file" 2>/dev/null | grep -q "com.apple.quarantine"; then
            sudo xattr -rd com.apple.quarantine "$file"
            echo "  ✅ 已移除 APP 隔离属性"
        else
            echo "  ℹ️  APP 文件无隔离属性"
        fi
    done
}

# 函数：检查 Gatekeeper 状态
check_gatekeeper() {
    echo ""
    echo "🛡️  检查 Gatekeeper 状态..."
    
    if spctl --status | grep -q "assessments enabled"; then
        echo "  ℹ️  Gatekeeper 已启用（推荐状态）"
        echo "  💡 如果仍有问题，可以临时禁用 Gatekeeper"
        echo "     命令: sudo spctl --master-disable"
        echo "     记得稍后重新启用: sudo spctl --master-enable"
    else
        echo "  ⚠️  Gatekeeper 已禁用"
        echo "  建议启用以提高安全性: sudo spctl --master-enable"
    fi
}

# 函数：验证应用程序
verify_app() {
    echo ""
    echo "🔍 验证应用程序..."
    
    for file in "${APP_FILES[@]}"; do
        if [ -d "$file" ]; then
            echo "  检查: $(basename "$file")"
            
            # 检查签名状态
            if codesign -dv "$file" 2>/dev/null; then
                echo "  ✅ 应用程序结构正常"
            else
                echo "  ⚠️  应用程序未签名（正常情况）"
            fi
            
            # 检查权限
            if [ -x "$file/Contents/MacOS"/* 2>/dev/null ]; then
                echo "  ✅ 执行权限正常"
            else
                echo "  🔧 修复执行权限..."
                chmod +x "$file/Contents/MacOS"/*
            fi
        fi
    done
}

# 函数：提供安装指导
installation_guide() {
    echo ""
    echo "📋 安装指导"
    echo "============"
    
    if [ ${#DMG_FILES[@]} -gt 0 ]; then
        echo "DMG 文件安装步骤:"
        echo "1. 双击 DMG 文件挂载"
        echo "2. 将应用程序拖拽到 Applications 文件夹"
        echo "3. 从 Applications 文件夹启动应用程序"
    fi
    
    if [ ${#APP_FILES[@]} -gt 0 ]; then
        echo "APP 文件启动:"
        echo "1. 直接双击应用程序启动"
        echo "2. 或从 Applications 文件夹启动"
    fi
    
    echo ""
    echo "如果仍遇到问题:"
    echo "• 右键点击应用程序，选择'打开'"
    echo "• 在弹出对话框中点击'打开'"
    echo "• 查看详细指南: docs/MACOS_INSTALL_GUIDE.md"
}

# 主函数
main() {
    find_downloaded_files
    remove_quarantine
    check_gatekeeper
    verify_app
    installation_guide
    
    echo ""
    echo "🎉 修复完成！"
    echo "现在可以尝试安装和运行 ${APP_NAME} 了"
}

# 运行主函数
main
