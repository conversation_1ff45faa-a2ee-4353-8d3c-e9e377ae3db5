#!/usr/bin/env python3
"""
FFmpeg 网络优化下载脚本
专门针对网络延迟、代理问题、连接超时等网络问题进行优化
支持多种网络环境和故障恢复
"""

import os
import sys
import time
import random
import requests
import zipfile
import tarfile
import shutil
from pathlib import Path
from urllib.parse import urljoin
import re
import socket
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"

class NetworkOptimizedDownloader:
    """网络优化的下载器"""
    
    def __init__(self):
        self.session = None
        self.proxy_settings = self._detect_proxy()
        self.timeout_settings = (30, 60)  # (连接超时, 读取超时)
        self.max_retries = 3
        
    def _detect_proxy(self):
        """强制禁用代理设置"""
        # 清除所有代理环境变量
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'http_proxy', 'https_proxy', 'ftp_proxy', 'no_proxy', 'all_proxy']
        for var in proxy_vars:
            if var in os.environ:
                print(f"🚫 清除代理环境变量: {var}={os.environ[var]}")
                del os.environ[var]

        # 强制使用直连
        print("🌐 强制使用直连模式（无代理）")
        return {
            'http': '',
            'https': '',
            'ftp': '',
            'no_proxy': '*'
        }
    
    def create_session(self):
        """创建优化的网络会话"""
        print("🔧 创建网络会话...")
        
        session = requests.Session()
        
        # 设置代理
        if self.proxy_settings:
            session.proxies.update(self.proxy_settings)
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        session.headers.update(headers)
        
        # 设置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置超时
        session.timeout = self.timeout_settings
        
        self.session = session
        print("✅ 网络会话创建完成")
        return session
    
    def test_connectivity(self):
        """测试网络连接"""
        print("🌐 测试网络连接...")
        
        test_urls = [
            "https://www.google.com",
            "https://www.github.com",
            "https://httpbin.org/get"
        ]
        
        for url in test_urls:
            try:
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ 网络连接正常: {url}")
                    return True
            except Exception as e:
                print(f"❌ 连接失败 {url}: {e}")
                continue
        
        print("❌ 网络连接测试失败")
        return False
    
    def download_with_retry(self, url, output_path, description="文件"):
        """带重试的下载"""
        print(f"📥 下载 {description}...")
        print(f"🔗 URL: {url}")
        
        for attempt in range(self.max_retries):
            try:
                print(f"🔄 尝试 {attempt + 1}/{self.max_retries}")
                
                # 发起请求
                response = self.session.get(url, stream=True, timeout=self.timeout_settings)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                start_time = time.time()
                
                print(f"📦 文件大小: {total_size // 1024 // 1024 if total_size > 0 else '未知'}MB")
                
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                elapsed_time = time.time() - start_time
                                speed = downloaded_size / elapsed_time / 1024 / 1024 if elapsed_time > 0 else 0
                                print(f"\r📊 进度: {progress:.1f}% ({downloaded_size // 1024 // 1024}MB/{total_size // 1024 // 1024}MB) 速度: {speed:.1f}MB/s", end='', flush=True)
                
                elapsed_time = time.time() - start_time
                print(f"\n✅ {description} 下载完成 (耗时: {elapsed_time:.1f}s)")
                return True
                
            except requests.exceptions.Timeout:
                print(f"\n⏰ 下载超时 (尝试 {attempt + 1})")
                if attempt < self.max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"⏳ 等待 {wait_time}s 后重试...")
                    time.sleep(wait_time)
                
            except requests.exceptions.ConnectionError as e:
                print(f"\n🔌 连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    wait_time = (attempt + 1) * 10
                    print(f"⏳ 等待 {wait_time}s 后重试...")
                    time.sleep(wait_time)
                
            except Exception as e:
                print(f"\n❌ 下载错误 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"⏳ 等待 {wait_time}s 后重试...")
                    time.sleep(wait_time)
        
        print(f"\n❌ {description} 下载失败 (已重试 {self.max_retries} 次)")
        return False

def get_direct_download_urls():
    """获取直接下载链接（绕过页面解析）"""
    return {
        "windows": [
            "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip",
            "https://github.com/BtbN/FFmpeg-Builds/releases/latest/download/ffmpeg-master-latest-win64-gpl.zip"
        ],
        "linux_x64": [
            "https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz"
        ],
        "linux_arm64": [
            "https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz"
        ],
        "macos": [
            "https://evermeet.cx/ffmpeg/getrelease/zip"
        ]
    }

def download_platform(downloader, platform, urls):
    """下载指定平台的FFmpeg"""
    print(f"\n🎯 下载 {platform} 版本...")
    
    for i, url in enumerate(urls):
        print(f"\n📍 尝试源 {i + 1}/{len(urls)}: {url}")
        
        # 确定文件扩展名
        if url.endswith('.zip'):
            temp_file = TEMP_DIR / f"ffmpeg-{platform}.zip"
        elif url.endswith('.tar.xz'):
            temp_file = TEMP_DIR / f"ffmpeg-{platform}.tar.xz"
        else:
            temp_file = TEMP_DIR / f"ffmpeg-{platform}.download"
        
        if downloader.download_with_retry(url, temp_file, f"{platform} FFmpeg"):
            print(f"✅ {platform} 版本下载成功")
            return temp_file
        else:
            print(f"❌ 源 {i + 1} 下载失败，尝试下一个源...")
    
    print(f"❌ {platform} 版本所有源都下载失败")
    return None

def main():
    """主函数"""
    print("🚀 FFmpeg 网络优化下载工具")
    print("=" * 50)
    print("🔧 专门针对网络问题优化:")
    print("   - 自动检测代理设置")
    print("   - 智能重试机制")
    print("   - 多源下载支持")
    print("   - 连接超时处理")
    print()
    
    # 创建目录
    TEMP_DIR.mkdir(parents=True, exist_ok=True)
    
    # 创建下载器
    downloader = NetworkOptimizedDownloader()
    downloader.create_session()
    
    # 测试网络连接
    if not downloader.test_connectivity():
        print("⚠️ 网络连接测试失败，但继续尝试下载...")
    
    # 获取下载链接
    download_urls = get_direct_download_urls()
    
    print("\n📋 可用平台:")
    platforms = list(download_urls.keys())
    for i, platform in enumerate(platforms, 1):
        print(f"   {i}. {platform}")
    print("   0. 全部下载")
    
    try:
        choice = input("\n请选择要下载的平台 (0-{}): ".format(len(platforms))).strip()
        
        if choice == "0":
            # 下载所有平台
            success_count = 0
            for platform in platforms:
                urls = download_urls[platform]
                if download_platform(downloader, platform, urls):
                    success_count += 1
            
            print(f"\n📊 下载结果: {success_count}/{len(platforms)} 个平台成功")
            
        else:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(platforms):
                platform = platforms[choice_idx]
                urls = download_urls[platform]
                result = download_platform(downloader, platform, urls)
                
                if result:
                    print(f"🎉 {platform} 下载成功!")
                    print(f"📁 文件: {result}")
                else:
                    print(f"❌ {platform} 下载失败")
            else:
                print("❌ 无效选择")
    
    except ValueError:
        print("❌ 请输入数字")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断下载")
    
    print(f"\n📁 下载目录: {TEMP_DIR}")
    print("📝 下一步:")
    print("   1. 解压下载的文件")
    print("   2. 复制到对应的ffmpeg目录")
    print("   3. 运行: yarn test:ffmpeg")

if __name__ == "__main__":
    main()
