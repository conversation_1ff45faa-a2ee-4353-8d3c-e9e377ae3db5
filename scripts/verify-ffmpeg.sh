#!/bin/bash

# FFmpeg 文件验证脚本
# 用于验证所有平台的 FFmpeg 二进制文件是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证单个文件
verify_file() {
    local file_path="$1"
    local platform="$2"
    local arch="$3"
    local binary_name="$4"
    
    if [ ! -f "$file_path" ]; then
        log_error "$platform-$arch $binary_name: 文件不存在"
        return 1
    fi
    
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo "0")
    
    if [ "$file_size" -eq 0 ]; then
        log_error "$platform-$arch $binary_name: 文件为空 (0 bytes)"
        return 1
    fi
    
    # 检查文件权限
    if [ ! -x "$file_path" ]; then
        log_warning "$platform-$arch $binary_name: 文件不可执行，正在修复..."
        chmod +x "$file_path"
    fi
    
    # 格式化文件大小
    local size_mb=$((file_size / 1024 / 1024))
    local size_kb=$((file_size / 1024))
    
    if [ $size_mb -gt 0 ]; then
        log_success "$platform-$arch $binary_name: ${size_mb}MB"
    else
        log_success "$platform-$arch $binary_name: ${size_kb}KB"
    fi
    
    return 0
}

# 验证平台
verify_platform() {
    local platform="$1"
    local arch="$2"
    local platform_dir="ffmpeg/$platform-$arch"
    
    log_info "验证 $platform-$arch..."
    
    if [ ! -d "$platform_dir" ]; then
        log_error "$platform-$arch: 目录不存在"
        return 1
    fi
    
    local ffmpeg_ext=""
    local ffprobe_ext=""
    
    if [[ "$platform" == "win" ]]; then
        ffmpeg_ext=".exe"
        ffprobe_ext=".exe"
    fi
    
    local ffmpeg_path="$platform_dir/ffmpeg$ffmpeg_ext"
    local ffprobe_path="$platform_dir/ffprobe$ffprobe_ext"
    
    local success=true
    
    if ! verify_file "$ffmpeg_path" "$platform" "$arch" "ffmpeg"; then
        success=false
    fi
    
    if ! verify_file "$ffprobe_path" "$platform" "$arch" "ffprobe"; then
        success=false
    fi
    
    if [ "$success" = true ]; then
        log_success "$platform-$arch: 验证通过"
        return 0
    else
        log_error "$platform-$arch: 验证失败"
        return 1
    fi
}

# 主验证函数
main() {
    log_info "开始验证 FFmpeg 二进制文件..."
    
    if [ ! -d "ffmpeg" ]; then
        log_error "ffmpeg 目录不存在，请先运行 yarn setup:ffmpeg"
        exit 1
    fi
    
    local platforms=("mac" "win" "linux")
    local architectures=("x64" "arm64")
    local total_success=true
    local verified_count=0
    local total_count=0
    
    for platform in "${platforms[@]}"; do
        for arch in "${architectures[@]}"; do
            total_count=$((total_count + 1))
            if verify_platform "$platform" "$arch"; then
                verified_count=$((verified_count + 1))
            else
                total_success=false
            fi
            echo  # 空行分隔
        done
    done
    
    echo "=================================="
    log_info "验证结果汇总:"
    log_info "总计: $total_count 个平台"
    log_info "成功: $verified_count 个平台"
    log_info "失败: $((total_count - verified_count)) 个平台"
    
    if [ "$total_success" = true ]; then
        log_success "所有 FFmpeg 文件验证通过！"
        echo
        log_info "可以开始构建:"
        echo "  yarn dist:all      # 构建所有平台"
        echo "  yarn dist:mac:all  # 只构建 macOS"
        echo "  yarn dist:win:all  # 只构建 Windows"
        echo "  yarn dist:linux:all # 只构建 Linux"
    else
        log_error "部分 FFmpeg 文件验证失败！"
        echo
        log_info "修复建议:"
        echo "  yarn cleanup:ffmpeg    # 清理 FFmpeg 文件"
        echo "  yarn download:ffmpeg   # 重新下载 FFmpeg"
        echo "  yarn setup:ffmpeg:current  # 复制当前平台文件"
        exit 1
    fi
}

# 运行主函数
main
