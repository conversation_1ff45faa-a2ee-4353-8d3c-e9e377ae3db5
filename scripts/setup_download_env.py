#!/usr/bin/env python3
"""
FFmpeg下载环境设置脚本
自动安装所需的Python依赖包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    print(f"   命令: {command}")
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"   输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"   错误: {e.stderr.strip()}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_basic_packages():
    """安装基础包"""
    packages = [
        "requests",
        "beautifulsoup4",
        "lxml"
    ]
    
    print("\n📦 安装基础Python包...")
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"安装 {package}"
        )
        if not success:
            print(f"⚠️ {package} 安装失败，但可能不影响基本功能")

def install_advanced_packages():
    """安装高级包（可选）"""
    packages = [
        ("requests-html", "支持JavaScript渲染的HTTP库"),
        ("selenium", "浏览器自动化库"),
        ("webdriver-manager", "自动管理浏览器驱动")
    ]
    
    print("\n🚀 安装高级Python包（可选）...")
    
    for package, description in packages:
        print(f"\n📋 {package}: {description}")
        choice = input(f"是否安装 {package}? (y/N): ").strip().lower()
        
        if choice in ['y', 'yes']:
            success = run_command(
                f"{sys.executable} -m pip install {package}",
                f"安装 {package}"
            )
            if success:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
        else:
            print(f"⏭️ 跳过 {package}")

def check_chrome_installation():
    """检查Chrome浏览器安装"""
    print("\n🌐 检查Chrome浏览器...")
    
    chrome_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",  # macOS
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",     # Windows
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",  # Windows 32-bit
        "/usr/bin/google-chrome",  # Linux
        "/usr/bin/chromium-browser"  # Linux Chromium
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("⚠️ 未找到Chrome浏览器")
        print("💡 如需使用Selenium功能，请安装Chrome浏览器:")
        print("   macOS: brew install --cask google-chrome")
        print("   Windows: 从 https://www.google.com/chrome/ 下载")
        print("   Linux: sudo apt install google-chrome-stable")

def install_chromedriver():
    """安装ChromeDriver"""
    print("\n🚗 检查ChromeDriver...")
    
    try:
        result = subprocess.run(
            ["chromedriver", "--version"],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ ChromeDriver已安装: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ChromeDriver未安装")
        
        print("💡 安装ChromeDriver:")
        print("   macOS: brew install chromedriver")
        print("   Windows: choco install chromedriver")
        print("   Linux: sudo apt install chromium-chromedriver")
        
        choice = input("是否尝试自动安装ChromeDriver? (仅macOS) (y/N): ").strip().lower()
        if choice in ['y', 'yes'] and sys.platform == 'darwin':
            return run_command("brew install chromedriver", "安装ChromeDriver")
        
        return False

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
"""
测试FFmpeg下载环境
"""

def test_basic_imports():
    """测试基础导入"""
    try:
        import requests
        import zipfile
        import tarfile
        print("✅ 基础模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False

def test_advanced_imports():
    """测试高级导入"""
    results = {}
    
    try:
        from requests_html import HTMLSession
        results['requests-html'] = True
        print("✅ requests-html 可用")
    except ImportError:
        results['requests-html'] = False
        print("⚠️ requests-html 不可用")
    
    try:
        from selenium import webdriver
        results['selenium'] = True
        print("✅ selenium 可用")
    except ImportError:
        results['selenium'] = False
        print("⚠️ selenium 不可用")
    
    return results

def test_network():
    """测试网络连接"""
    import requests
    
    test_urls = [
        "https://www.gyan.dev/ffmpeg/builds/",
        "https://johnvansickle.com/ffmpeg/",
        "https://evermeet.cx/ffmpeg/"
    ]
    
    print("🌐 测试网络连接...")
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")

if __name__ == "__main__":
    print("🧪 FFmpeg下载环境测试")
    print("=" * 40)
    
    test_basic_imports()
    print()
    test_advanced_imports()
    print()
    test_network()
    
    print("\\n🎉 环境测试完成!")
'''
    
    test_file = Path(__file__).parent / "test_download_env.py"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 测试脚本已创建: {test_file}")
    return test_file

def main():
    """主函数"""
    print("🚀 FFmpeg下载环境设置")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装基础包
    install_basic_packages()
    
    # 安装高级包
    install_advanced_packages()
    
    # 检查Chrome
    check_chrome_installation()
    
    # 安装ChromeDriver
    install_chromedriver()
    
    # 创建测试脚本
    test_file = create_test_script()
    
    print("\n" + "=" * 40)
    print("🎉 环境设置完成!")
    print("\n📝 下一步:")
    print(f"  1. 运行测试: python {test_file}")
    print("  2. 运行基础下载: python scripts/download_ffmpeg.py")
    print("  3. 运行高级下载: python scripts/download_ffmpeg_advanced.py")
    print("  4. 运行Selenium下载: python scripts/download_ffmpeg_selenium.py")
    
    print("\n💡 提示:")
    print("  - 基础脚本使用requests，兼容性最好")
    print("  - 高级脚本支持JavaScript渲染，更真实")
    print("  - Selenium脚本完全模拟真实浏览器，最强大但需要Chrome")

if __name__ == "__main__":
    main()
