#!/usr/bin/env node

/**
 * FFmpeg 路径测试脚本
 * 
 * 测试不同平台和架构下的 FFmpeg 路径解析逻辑
 */

const path = require('path');
const fs = require('fs');

// 模拟不同的平台和架构组合
const testCases = [
  { platform: 'darwin', arch: 'x64', expected: 'mac-x64' },
  { platform: 'darwin', arch: 'arm64', expected: 'mac-arm64' },
  { platform: 'win32', arch: 'x64', expected: 'win-x64' },
  { platform: 'win32', arch: 'arm64', expected: 'win-arm64' },
  { platform: 'linux', arch: 'x64', expected: 'linux-x64' },
  { platform: 'linux', arch: 'arm64', expected: 'linux-arm64' }
];

// 模拟 FFmpeg 路径解析逻辑
function getFFmpegPaths(platform, arch, isPackaged = true) {
  if (isPackaged) {
    // 模拟打包后的路径
    const resourcesPath = '/app/resources'; // 模拟路径
    
    // 映射平台名称
    let platformName;
    if (platform === 'darwin') {
      platformName = 'mac';
    } else if (platform === 'win32') {
      platformName = 'win';
    } else {
      platformName = 'linux';
    }

    // 构建FFmpeg目录路径
    const ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${arch}`);
    
    // 根据平台确定可执行文件名
    const ffmpegName = platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';
    const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe';

    const ffmpegPath = path.join(ffmpegDir, ffmpegName);
    const ffprobePath = path.join(ffmpegDir, ffprobeName);

    return { ffmpegPath, ffprobePath, platformDir: `${platformName}-${arch}` };
  } else {
    // 开发环境路径（模拟）
    return {
      ffmpegPath: '/usr/local/bin/ffmpeg',
      ffprobePath: '/usr/local/bin/ffprobe',
      platformDir: 'development'
    };
  }
}

// 检查项目中的 FFmpeg 目录结构
function checkFFmpegDirectories() {
  const projectRoot = path.join(__dirname, '..');
  const ffmpegDir = path.join(projectRoot, 'ffmpeg');
  
  console.log('🔍 检查 FFmpeg 目录结构...\n');
  
  if (!fs.existsSync(ffmpegDir)) {
    console.log('❌ ffmpeg/ 目录不存在');
    return false;
  }
  
  let allGood = true;
  
  testCases.forEach(({ platform, arch, expected }) => {
    const platformDir = path.join(ffmpegDir, expected);
    const exists = fs.existsSync(platformDir);
    
    console.log(`${exists ? '✅' : '❌'} ${expected}/`);
    
    if (exists) {
      // 检查二进制文件
      const ffmpegName = platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';
      const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe';
      
      const ffmpegPath = path.join(platformDir, ffmpegName);
      const ffprobePath = path.join(platformDir, ffprobeName);
      
      const ffmpegExists = fs.existsSync(ffmpegPath);
      const ffprobeExists = fs.existsSync(ffprobePath);
      
      console.log(`   ${ffmpegExists ? '✅' : '❌'} ${ffmpegName}`);
      console.log(`   ${ffprobeExists ? '✅' : '❌'} ${ffprobeName}`);
      
      if (!ffmpegExists || !ffprobeExists) {
        allGood = false;
      }
    } else {
      allGood = false;
    }
  });
  
  return allGood;
}

// 测试路径解析逻辑
function testPathResolution() {
  console.log('\n🧪 测试路径解析逻辑...\n');
  
  testCases.forEach(({ platform, arch, expected }) => {
    const result = getFFmpegPaths(platform, arch, true);
    const success = result.platformDir === expected;
    
    console.log(`${success ? '✅' : '❌'} ${platform}-${arch} -> ${result.platformDir}`);
    
    if (!success) {
      console.log(`   期望: ${expected}, 实际: ${result.platformDir}`);
    }
    
    console.log(`   FFmpeg: ${result.ffmpegPath}`);
    console.log(`   FFprobe: ${result.ffprobePath}`);
    console.log('');
  });
}

// 显示当前系统信息
function showSystemInfo() {
  console.log('💻 当前系统信息:');
  console.log(`   平台: ${process.platform}`);
  console.log(`   架构: ${process.arch}`);
  console.log(`   Node.js: ${process.version}`);
  
  const currentPaths = getFFmpegPaths(process.platform, process.arch, true);
  console.log(`   预期平台目录: ${currentPaths.platformDir}`);
  console.log('');
}

// 主函数
function main() {
  console.log('🚀 FFmpeg 路径配置测试\n');
  
  showSystemInfo();
  testPathResolution();
  checkFFmpegDirectories();
  
  console.log('\n📝 说明:');
  console.log('• 如果某些平台目录不存在，请运行: yarn setup:ffmpeg');
  console.log('• 如果当前平台的二进制文件不存在，请运行: yarn setup:ffmpeg:current');
  console.log('• 对于生产构建，需要手动下载所有平台的 FFmpeg 二进制文件');
}

if (require.main === module) {
  main();
}

module.exports = { getFFmpegPaths, checkFFmpegDirectories, testPathResolution };
