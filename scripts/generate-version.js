#!/usr/bin/env node

/**
 * 生成时间戳版本号脚本
 * 
 * 格式: YYMMDDHHMM (年月日时分)
 * 例如: 2507181030 表示 2025年07月18日10时30分
 */

const fs = require('fs');
const path = require('path');

function generateTimestampVersion() {
  const now = new Date();

  // 获取年份的后两位
  const year = now.getFullYear().toString().slice(-2);

  // 获取月份 (01-12)
  const month = (now.getMonth() + 1).toString().padStart(2, '0');

  // 获取日期 (01-31)
  const day = now.getDate().toString().padStart(2, '0');

  // 获取小时 (00-23)
  const hour = now.getHours().toString().padStart(2, '0');

  // 获取分钟 (00-59)
  const minute = now.getMinutes().toString().padStart(2, '0');

  // 组合成时间戳版本号: yy.MM.dd-HHmm (使用连字符避免语义版本解析问题)
  const version = `${year}.${month}.${day}-${hour}${minute}`;

  return version;
}

function updatePackageVersion(version) {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  
  try {
    // 读取 package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 更新版本号 (直接使用时间戳格式)
    packageJson.version = version;

    // 写回 package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n', 'utf8');

    console.log(`✅ 版本号已更新为: ${packageJson.version}`);
    return packageJson.version;
  } catch (error) {
    console.error('❌ 更新 package.json 失败:', error.message);
    throw error;
  }
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📦 时间戳版本号生成器

用法:
  node scripts/generate-version.js [选项]

选项:
  --help, -h     显示此帮助信息
  --dry-run      只生成版本号，不更新 package.json
  --format       显示版本号格式说明

格式说明:
  版本号格式: YY.MM.DD.HHmm

  其中:
  - YY: 年份后两位 (25 = 2025年)
  - MM: 月份 (01-12)
  - DD: 日期 (01-31)
  - HH: 小时 (00-23)
  - mm: 分钟 (00-59)

  示例:
  - 2025年07月18日10时30分 -> 25.07.18.1030
  - 2025年12月31日23时59分 -> 25.12.31.2359

环境变量:
  CI_TIMESTAMP   在 CI 环境中可以设置固定的时间戳
`);
    return;
  }
  
  if (args.includes('--format')) {
    console.log(`
📅 时间戳版本号格式说明:

格式: YY.MM.DD.HHmm

组成部分:
  YY - 年份后两位
  MM - 月份 (01-12)
  DD - 日期 (01-31)
  HH - 小时 (00-23)
  mm - 分钟 (00-59)

示例:
  当前时间: ${new Date().toLocaleString('zh-CN')}
  生成版本: ${generateTimestampVersion()}
`);
    return;
  }
  
  try {
    // 检查是否有环境变量指定的时间戳
    let version;
    if (process.env.CI_TIMESTAMP) {
      version = process.env.CI_TIMESTAMP;
      console.log(`🔧 使用环境变量指定的时间戳: ${version}`);
    } else {
      version = generateTimestampVersion();
      console.log(`⏰ 生成时间戳版本号: ${version}`);
      console.log(`📅 对应时间: ${new Date().toLocaleString('zh-CN')}`);
    }
    
    if (args.includes('--dry-run')) {
      console.log(`🔍 预览版本号: ${version}`);
      console.log('💡 使用 --dry-run 参数，未更新 package.json');
    } else {
      const fullVersion = updatePackageVersion(version);
      console.log(`🎉 版本号更新完成: ${fullVersion}`);
    }
    
  } catch (error) {
    console.error('❌ 生成版本号失败:', error.message);
    process.exit(1);
  }
}

// 导出函数供其他脚本使用
module.exports = {
  generateTimestampVersion,
  updatePackageVersion
};

// 如果直接运行此脚本
if (require.main === module) {
  main();
}
