#!/usr/bin/env python3
"""
FFmpeg 直连下载脚本
强制禁用所有代理，使用直连方式下载
专门解决代理导致的网络连接问题
"""

import os
import sys
import time
import random
import requests
import zipfile
import tarfile
import shutil
from pathlib import Path
from urllib.parse import urljoin
import re
import subprocess

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"

def clear_proxy_environment():
    """清除所有代理相关的环境变量"""
    print("🚫 清除所有代理设置...")
    
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'SOCKS_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy', 'socks_proxy',
        'no_proxy', 'NO_PROXY', 'all_proxy', 'ALL_PROXY'
    ]
    
    cleared_count = 0
    for var in proxy_vars:
        if var in os.environ:
            print(f"   清除: {var}={os.environ[var]}")
            del os.environ[var]
            cleared_count += 1
    
    if cleared_count > 0:
        print(f"✅ 已清除 {cleared_count} 个代理环境变量")
    else:
        print("✅ 未发现代理环境变量")

def create_direct_session():
    """创建强制直连的会话"""
    print("🌐 创建直连会话...")
    
    # 先清除代理环境变量
    clear_proxy_environment()
    
    session = requests.Session()
    
    # 强制禁用代理
    session.proxies = {
        'http': '',
        'https': '',
        'ftp': '',
        'socks': '',
        'no_proxy': '*'
    }
    
    # 设置真实的浏览器头
    user_agents = [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    }
    
    session.headers.update(headers)
    
    # 设置超时
    session.timeout = (30, 120)  # 连接超时30s，读取超时120s
    
    print("✅ 直连会话创建完成")
    print(f"🔧 User-Agent: {session.headers.get('User-Agent', 'Unknown')[:60]}...")
    
    return session

def test_direct_connection(session):
    """测试直连是否正常"""
    print("🔍 测试直连网络...")
    
    test_urls = [
        "https://httpbin.org/ip",
        "https://www.google.com",
        "https://github.com"
    ]
    
    for url in test_urls:
        try:
            print(f"   测试: {url}")
            response = session.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ 连接成功 (状态码: {response.status_code})")
                if 'httpbin.org/ip' in url:
                    try:
                        ip_info = response.json()
                        print(f"   🌐 您的IP: {ip_info.get('origin', 'Unknown')}")
                    except:
                        pass
                return True
            else:
                print(f"   ⚠️ 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
    
    print("❌ 所有测试连接都失败")
    return False

def download_file_direct(session, url, output_path, description="文件"):
    """直连下载文件"""
    print(f"\n📥 直连下载 {description}...")
    print(f"🔗 URL: {url}")
    print(f"📁 保存到: {output_path}")
    
    # 模拟人类访问延迟
    delay = random.uniform(1, 3)
    print(f"⏳ 模拟访问延迟 {delay:.1f}s...")
    time.sleep(delay)
    
    try:
        # 发起下载请求
        response = session.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        start_time = time.time()
        
        print(f"📦 文件大小: {total_size // 1024 // 1024 if total_size > 0 else '未知'}MB")
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    # 模拟网络波动
                    if random.random() < 0.01:  # 1%概率暂停
                        time.sleep(random.uniform(0.1, 0.3))
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        elapsed_time = time.time() - start_time
                        speed = downloaded_size / elapsed_time / 1024 / 1024 if elapsed_time > 0 else 0
                        print(f"\r📊 进度: {progress:.1f}% ({downloaded_size // 1024 // 1024}MB/{total_size // 1024 // 1024}MB) 速度: {speed:.1f}MB/s", end='', flush=True)
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ {description} 下载完成 (耗时: {elapsed_time:.1f}s)")
        return True
        
    except Exception as e:
        print(f"\n❌ {description} 下载失败: {e}")
        return False

def setup_macos_ffmpeg():
    """设置macOS FFmpeg（从系统复制）"""
    print("\n🍎 设置 macOS FFmpeg...")
    
    try:
        # 检查系统是否有 ffmpeg
        ffmpeg_path = subprocess.check_output(['which', 'ffmpeg'], text=True).strip()
        ffprobe_path = subprocess.check_output(['which', 'ffprobe'], text=True).strip()
        
        print(f"✅ 找到系统 FFmpeg: {ffmpeg_path}")
        print(f"✅ 找到系统 FFprobe: {ffprobe_path}")
        
        # 复制到项目目录
        platforms = ["mac-x64", "mac-arm64"]
        for platform in platforms:
            platform_dir = FFMPEG_DIR / platform
            platform_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(ffmpeg_path, platform_dir / "ffmpeg")
            shutil.copy2(ffprobe_path, platform_dir / "ffprobe")
            
            # 设置执行权限
            os.chmod(platform_dir / "ffmpeg", 0o755)
            os.chmod(platform_dir / "ffprobe", 0o755)
            
            print(f"✅ 复制到 {platform}")
        
        print("✅ macOS FFmpeg 设置完成")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ 系统中未找到 FFmpeg，请先安装:")
        print("   brew install ffmpeg")
        return False
    except Exception as e:
        print(f"❌ 设置 macOS FFmpeg 失败: {e}")
        return False

def download_windows_ffmpeg_direct(session):
    """直连下载Windows FFmpeg"""
    print("\n🪟 直连下载 Windows FFmpeg...")
    
    # 使用直接下载链接，避免页面解析
    direct_urls = [
        "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip",
        "https://github.com/BtbN/FFmpeg-Builds/releases/latest/download/ffmpeg-master-latest-win64-gpl.zip"
    ]
    
    for i, url in enumerate(direct_urls, 1):
        print(f"\n📍 尝试源 {i}/{len(direct_urls)}: {url}")
        
        temp_file = TEMP_DIR / f"ffmpeg-windows-{i}.zip"
        if download_file_direct(session, url, temp_file, f"Windows FFmpeg (源{i})"):
            print(f"✅ Windows FFmpeg 下载成功 (使用源{i})")
            return temp_file
        else:
            print(f"❌ 源{i} 下载失败，尝试下一个...")
    
    print("❌ 所有Windows FFmpeg源都下载失败")
    return None

def download_linux_ffmpeg_direct(session, arch="amd64"):
    """直连下载Linux FFmpeg"""
    arch_name = "x64" if arch == "amd64" else "arm64"
    print(f"\n🐧 直连下载 Linux {arch_name} FFmpeg...")
    
    # 使用直接下载链接
    url = f"https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-{arch}-static.tar.xz"
    
    temp_file = TEMP_DIR / f"ffmpeg-linux-{arch}.tar.xz"
    if download_file_direct(session, url, temp_file, f"Linux {arch_name} FFmpeg"):
        print(f"✅ Linux {arch_name} FFmpeg 下载成功")
        return temp_file
    else:
        print(f"❌ Linux {arch_name} FFmpeg 下载失败")
        return None

def main():
    """主函数"""
    print("🚀 FFmpeg 直连下载脚本")
    print("=" * 50)
    print("🎯 特点:")
    print("   - 强制禁用所有代理")
    print("   - 使用直连方式下载")
    print("   - 模拟真人浏览器行为")
    print("   - 支持多源下载")
    print()
    
    # 创建目录结构
    TEMP_DIR.mkdir(parents=True, exist_ok=True)
    platforms = ["win-x64", "win-arm64", "mac-x64", "mac-arm64", "linux-x64", "linux-arm64"]
    for platform in platforms:
        (FFMPEG_DIR / platform).mkdir(parents=True, exist_ok=True)
    
    # 创建直连会话
    session = create_direct_session()
    
    # 测试网络连接
    if not test_direct_connection(session):
        print("⚠️ 网络连接测试失败，但继续尝试下载...")
    
    success_count = 0
    total_count = 4  # macOS, Windows, Linux x64, Linux ARM64
    
    try:
        # 1. 设置 macOS FFmpeg
        if setup_macos_ffmpeg():
            success_count += 1
        
        # 2. 下载 Windows FFmpeg
        if download_windows_ffmpeg_direct(session):
            success_count += 1
        
        # 3. 下载 Linux x64 FFmpeg
        if download_linux_ffmpeg_direct(session, "amd64"):
            success_count += 1
        
        # 4. 下载 Linux ARM64 FFmpeg
        if download_linux_ffmpeg_direct(session, "arm64"):
            success_count += 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print(f"📊 下载结果: {success_count}/{total_count} 个平台成功")
    
    if success_count >= 2:
        print("🎉 主要平台的 FFmpeg 下载完成！")
        print("\n📝 下一步:")
        print("  1. 解压下载的文件")
        print("  2. 运行: yarn test:ffmpeg")
        print("  3. 运行: yarn version:generate")
        print("  4. 运行: yarn dist")
    else:
        print("❌ 大部分平台下载失败")
        print("💡 建议:")
        print("  1. 检查网络连接")
        print("  2. 尝试使用浏览器手动下载")
        print("  3. 联系网络管理员")

if __name__ == "__main__":
    main()
