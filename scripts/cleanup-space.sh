#!/bin/bash

# MEEA-VIOFO 磁盘空间清理脚本

echo "🧹 MEEA-VIOFO 磁盘空间清理工具"
echo "============================="

# 显示清理前状态
echo "📊 清理前磁盘状态:"
df -h .

echo ""
echo "🧹 开始清理..."

# 清理各种缓存
echo "🔧 清理构建缓存..."
rm -rf ~/.cache/electron* 2>/dev/null && echo "  ✅ Electron 缓存已清理"
rm -rf ~/.cache/electron-builder* 2>/dev/null && echo "  ✅ Electron Builder 缓存已清理"
rm -rf ~/.npm 2>/dev/null && echo "  ✅ NPM 缓存已清理"
rm -rf ~/.yarn 2>/dev/null && echo "  ✅ Yarn 缓存已清理"

# 清理构建文件
echo "📦 清理构建文件..."
rm -rf release/ 2>/dev/null && echo "  ✅ release 目录已清理"
rm -rf dist/ 2>/dev/null && echo "  ✅ dist 目录已清理"

# 清理 node_modules 缓存
echo "📁 清理 node_modules 缓存..."
if [ -d "node_modules" ]; then
    find node_modules -name ".cache" -type d -exec rm -rf {} + 2>/dev/null
    find node_modules -name "*.log" -delete 2>/dev/null
    echo "  ✅ node_modules 缓存已清理"
fi

# 清理临时文件
echo "🗑️  清理临时文件..."
rm -rf /tmp/electron-* /tmp/npm-* 2>/dev/null && echo "  ✅ 临时文件已清理"

echo ""
echo "✅ 清理完成！"

# 显示清理后状态
echo ""
echo "📊 清理后磁盘状态:"
df -h .

# 计算可用空间
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
AVAILABLE_GB=$((AVAILABLE_SPACE / 1024 / 1024))

echo ""
echo "💾 当前可用空间: ${AVAILABLE_GB}GB"

if [ $AVAILABLE_GB -lt 3 ]; then
    echo "⚠️  空间仍然不足，建议："
    echo "   - 删除不需要的文件"
    echo "   - 使用 docker system prune -a 清理 Docker"
    echo "   - 清理系统日志"
elif [ $AVAILABLE_GB -lt 8 ]; then
    echo "⚠️  空间有限，建议分平台构建"
else
    echo "✅ 空间充足，可以进行全平台构建"
fi
