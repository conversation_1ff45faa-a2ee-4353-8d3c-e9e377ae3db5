#!/usr/bin/env node

/**
 * 构建产物验证脚本
 * 验证构建产物是否包含正确的架构文件
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkDistDirectory() {
  const distPath = path.resolve(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distPath)) {
    log('❌ dist 目录不存在，请先运行构建命令', 'red');
    return false;
  }
  
  return true;
}

function getBuiltFiles() {
  const distPath = path.resolve(__dirname, '..', 'dist');
  const files = fs.readdirSync(distPath);
  
  const installers = files.filter(file => 
    file.includes('MEEA-VIOFO') && 
    (file.endsWith('.exe') || file.endsWith('.dmg') || file.endsWith('.AppImage'))
  );
  
  return installers;
}

function analyzeWindowsInstaller(filename) {
  log(`\n🔍 分析 Windows 安装包: ${filename}`, 'cyan');
  
  // 从文件名判断架构
  if (filename.includes('-x64.exe')) {
    log('  📋 检测到架构: x64', 'blue');
    return 'x64';
  } else if (filename.includes('-arm64.exe')) {
    log('  📋 检测到架构: ARM64', 'blue');
    return 'arm64';
  } else {
    log('  ⚠️  无法从文件名确定架构', 'yellow');
    return 'unknown';
  }
}

function analyzeMacInstaller(filename) {
  log(`\n🔍 分析 macOS 安装包: ${filename}`, 'cyan');
  
  // 从文件名判断架构
  if (filename.includes('-x64.dmg')) {
    log('  📋 检测到架构: x64 (Intel)', 'blue');
    return 'x64';
  } else if (filename.includes('-arm64.dmg')) {
    log('  📋 检测到架构: ARM64 (Apple Silicon)', 'blue');
    return 'arm64';
  } else {
    log('  ⚠️  无法从文件名确定架构', 'yellow');
    return 'unknown';
  }
}

function analyzeLinuxInstaller(filename) {
  log(`\n🔍 分析 Linux 安装包: ${filename}`, 'cyan');
  
  // 从文件名判断架构
  if (filename.includes('-x64.AppImage')) {
    log('  📋 检测到架构: x64', 'blue');
    return 'x64';
  } else if (filename.includes('-arm64.AppImage')) {
    log('  📋 检测到架构: ARM64', 'blue');
    return 'arm64';
  } else {
    log('  ⚠️  无法从文件名确定架构', 'yellow');
    return 'unknown';
  }
}

function verifyArchitectureConsistency(files) {
  log('\n📊 架构一致性检查:', 'bright');
  
  const results = [];
  
  files.forEach(file => {
    const filePath = path.resolve(__dirname, '..', 'dist', file);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024 / 1024).toFixed(1);
    
    let arch = 'unknown';
    let platform = 'unknown';
    
    if (file.endsWith('.exe')) {
      platform = 'Windows';
      arch = analyzeWindowsInstaller(file);
    } else if (file.endsWith('.dmg')) {
      platform = 'macOS';
      arch = analyzeMacInstaller(file);
    } else if (file.endsWith('.AppImage')) {
      platform = 'Linux';
      arch = analyzeLinuxInstaller(file);
    }
    
    results.push({
      file,
      platform,
      arch,
      size: `${size} MB`
    });
  });
  
  return results;
}

function checkForArchitectureMixing(expectedArch, actualFiles) {
  log(`\n🔍 检查是否存在架构混合问题 (期望: ${expectedArch})`, 'cyan');
  
  const issues = [];
  
  actualFiles.forEach(result => {
    if (expectedArch === 'x64' && result.file.includes('arm64')) {
      issues.push(`❌ 发现 ARM64 文件: ${result.file}`);
    } else if (expectedArch === 'arm64' && result.file.includes('x64')) {
      issues.push(`❌ 发现 x64 文件: ${result.file}`);
    }
  });
  
  if (issues.length === 0) {
    log('  ✅ 没有发现架构混合问题', 'green');
    return true;
  } else {
    issues.forEach(issue => log(`  ${issue}`, 'red'));
    return false;
  }
}

function showSummary(results) {
  log('\n📋 构建产物总结:', 'bright');
  
  if (results.length === 0) {
    log('  ⚠️  没有找到构建产物', 'yellow');
    return;
  }
  
  console.log('\n  文件名'.padEnd(50) + '平台'.padEnd(10) + '架构'.padEnd(10) + '大小');
  console.log('  ' + '-'.repeat(75));
  
  results.forEach(result => {
    const archColor = result.arch === 'unknown' ? 'yellow' : 'green';
    const archText = result.arch.padEnd(10);
    
    console.log(`  ${result.file.padEnd(50)}${result.platform.padEnd(10)}${colors[archColor]}${archText}${colors.reset}${result.size}`);
  });
}

function main() {
  const args = process.argv.slice(2);
  const expectedArch = args[0]; // x64, arm64, 或 undefined
  
  log('🔍 MEEA-VIOFO 构建产物验证工具', 'bright');
  
  if (!checkDistDirectory()) {
    process.exit(1);
  }
  
  const files = getBuiltFiles();
  
  if (files.length === 0) {
    log('\n⚠️  没有找到构建产物', 'yellow');
    log('请先运行构建命令，例如:', 'blue');
    log('  yarn build:windows-x64', 'cyan');
    log('  yarn build:macos-arm64', 'cyan');
    log('  yarn build:linux-x64', 'cyan');
    process.exit(1);
  }
  
  const results = verifyArchitectureConsistency(files);
  
  let allGood = true;
  
  // 如果指定了期望的架构，检查是否有混合问题
  if (expectedArch) {
    allGood = checkForArchitectureMixing(expectedArch, results);
  }
  
  showSummary(results);
  
  if (allGood) {
    log('\n🎉 构建产物验证通过！', 'green');
  } else {
    log('\n❌ 构建产物验证失败，请检查上述问题', 'red');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  checkDistDirectory,
  getBuiltFiles,
  verifyArchitectureConsistency
};
