#!/usr/bin/env node

/**
 * electron-builder 配置验证脚本
 * 验证配置文件是否符合 electron-builder 的要求
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// electron-builder 不允许的顶级属性
const INVALID_TOP_LEVEL_PROPERTIES = [
  'dependencies',
  'devDependencies',
  'scripts',
  'keywords',
  'author',
  'license',
  'repository',
  'bugs',
  'homepage',
  'engines',
  'main',
  'type',
  'exports'
];

function validateConfig(configPath) {
  log(`\n🔍 验证配置文件: ${configPath}`, 'cyan');
  
  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    let isValid = true;
    const issues = [];
    
    // 检查是否包含不允许的属性
    INVALID_TOP_LEVEL_PROPERTIES.forEach(prop => {
      if (config.hasOwnProperty(prop)) {
        issues.push(`❌ 包含不允许的属性: ${prop}`);
        isValid = false;
      }
    });
    
    // 检查必需的属性
    const requiredProps = ['appId', 'productName'];
    requiredProps.forEach(prop => {
      if (!config.hasOwnProperty(prop)) {
        issues.push(`⚠️  缺少推荐属性: ${prop}`);
      }
    });
    
    // 检查 extends 属性
    if (config.extends) {
      issues.push(`⚠️  使用了 extends 属性，可能导致继承不需要的属性`);
    }
    
    // 显示结果
    if (isValid && issues.length === 0) {
      log('  ✅ 配置文件有效', 'green');
    } else {
      if (!isValid) {
        log('  ❌ 配置文件无效', 'red');
      } else {
        log('  ⚠️  配置文件有警告', 'yellow');
      }
      
      issues.forEach(issue => {
        log(`    ${issue}`, 'yellow');
      });
    }
    
    return isValid;
    
  } catch (error) {
    log(`  ❌ 读取或解析配置文件失败: ${error.message}`, 'red');
    return false;
  }
}

function validateAllConfigs() {
  log('🔧 electron-builder 配置验证工具', 'bright');
  
  const configFiles = [
    'electron-builder-win-x64.json',
    'electron-builder-win-arm64.json'
  ];
  
  let allValid = true;
  
  configFiles.forEach(configFile => {
    const configPath = path.resolve(__dirname, '..', configFile);
    
    if (fs.existsSync(configPath)) {
      const isValid = validateConfig(configPath);
      if (!isValid) {
        allValid = false;
      }
    } else {
      log(`\n⚠️  配置文件不存在: ${configFile}`, 'yellow');
    }
  });
  
  // 也验证 package.json 中的构建配置
  log(`\n🔍 验证 package.json 中的构建配置`, 'cyan');
  const packagePath = path.resolve(__dirname, '..', 'package.json');
  
  try {
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    if (packageJson.build) {
      log('  ✅ 找到 build 配置', 'green');
      
      // 检查是否有平台特定配置
      const platforms = ['win', 'mac', 'linux'];
      platforms.forEach(platform => {
        if (packageJson.build[platform]) {
          log(`  ✅ 找到 ${platform} 平台配置`, 'green');
        }
      });
    } else {
      log('  ❌ 未找到 build 配置', 'red');
      allValid = false;
    }
    
  } catch (error) {
    log(`  ❌ 读取 package.json 失败: ${error.message}`, 'red');
    allValid = false;
  }
  
  // 显示总结
  log('\n📊 验证总结:', 'bright');
  if (allValid) {
    log('✅ 所有配置文件都有效', 'green');
  } else {
    log('❌ 部分配置文件有问题，请修复后重试', 'red');
  }
  
  return allValid;
}

function showConfigStructure() {
  log('\n📋 推荐的配置文件结构:', 'cyan');
  
  const recommendedStructure = {
    appId: 'com.meea.viofo',
    productName: 'MEEA-VIOFO',
    directories: {
      output: 'dist'
    },
    files: [
      'dist/**/*',
      'electron/**/*',
      'package.json'
    ],
    win: {
      icon: 'build/icons/icon.ico',
      target: [
        {
          target: 'nsis',
          arch: ['x64'] // 或 ['arm64']
        }
      ]
    }
  };
  
  console.log(JSON.stringify(recommendedStructure, null, 2));
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    log('🔧 electron-builder 配置验证工具', 'bright');
    log('\n用法:', 'yellow');
    log('  node scripts/validate-electron-builder-config.js', 'cyan');
    log('  node scripts/validate-electron-builder-config.js --structure', 'cyan');
    log('\n选项:', 'yellow');
    log('  --structure  显示推荐的配置文件结构', 'cyan');
    return;
  }
  
  if (args.includes('--structure')) {
    showConfigStructure();
    return;
  }
  
  const isValid = validateAllConfigs();
  
  if (!isValid) {
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  validateConfig,
  validateAllConfigs
};
