#!/usr/bin/env python3
"""
FFmpeg 高级下载脚本
使用 requests-html 模拟JavaScript渲染和真人浏览器行为
更轻量级的解决方案，不需要安装Chrome浏览器
"""

import os
import sys
import time
import random
import shutil
import zipfile
import tarfile
import json
from pathlib import Path
from urllib.parse import urljoin, urlparse
import re

try:
    from requests_html import HTMLSession
    REQUESTS_HTML_AVAILABLE = True
except ImportError:
    REQUESTS_HTML_AVAILABLE = False
    print("⚠️ requests-html 未安装，将使用基础requests模式")
    print("💡 安装方法: pip install requests-html")

import requests

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"

# 真实的浏览器指纹数据
BROWSER_FINGERPRINTS = [
    {
        'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'accept_language': 'en-US,en;q=0.9',
        'platform': 'MacIntel',
        'screen_resolution': '1920x1080'
    },
    {
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'accept_language': 'en-US,en;q=0.9',
        'platform': 'Win32',
        'screen_resolution': '1366x768'
    },
    {
        'user_agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'accept_language': 'en-US,en;q=0.9',
        'platform': 'Linux x86_64',
        'screen_resolution': '1440x900'
    }
]

class HumanBrowserSimulator:
    """模拟人类浏览器行为的类"""
    
    def __init__(self):
        self.fingerprint = random.choice(BROWSER_FINGERPRINTS)
        self.session = self._create_session()
        self.visit_history = []
        
    def _create_session(self):
        """创建模拟真人的会话"""
        if REQUESTS_HTML_AVAILABLE:
            session = HTMLSession()
        else:
            session = requests.Session()
            
        # 设置真实的浏览器头
        session.headers.update({
            'User-Agent': self.fingerprint['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': self.fingerprint['accept_language'],
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        # 设置合理的超时和重试
        session.timeout = 30
        
        print(f"🌐 创建浏览器会话")
        print(f"   User-Agent: {self.fingerprint['user_agent'][:60]}...")
        print(f"   平台: {self.fingerprint['platform']}")
        
        return session
    
    def human_delay(self, min_seconds=1, max_seconds=3):
        """模拟人类操作延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        print(f"⏳ 模拟思考时间 {delay:.1f}s...")
        time.sleep(delay)
    
    def visit_page(self, url, description="页面"):
        """模拟人类访问页面"""
        print(f"🔍 访问 {description}: {url}")
        
        # 模拟打开新页面前的思考时间
        self.human_delay(0.5, 2.0)
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            # 记录访问历史
            self.visit_history.append({
                'url': url,
                'timestamp': time.time(),
                'status_code': response.status_code
            })
            
            # 模拟页面加载和阅读时间
            self.human_delay(1.0, 3.0)
            
            print(f"✅ 成功访问 {description} (状态码: {response.status_code})")
            
            # 如果支持JavaScript渲染，执行渲染
            if REQUESTS_HTML_AVAILABLE and hasattr(response, 'html'):
                print("🔄 渲染JavaScript内容...")
                try:
                    response.html.render(timeout=20)
                    print("✅ JavaScript渲染完成")
                except Exception as e:
                    print(f"⚠️ JavaScript渲染失败: {e}")
            
            return response
            
        except Exception as e:
            print(f"❌ 访问 {description} 失败: {e}")
            return None
    
    def find_download_links(self, response, patterns):
        """在页面中查找下载链接"""
        print("🔍 在页面中搜索下载链接...")
        
        # 模拟用户浏览页面的时间
        self.human_delay(2.0, 4.0)
        
        content = response.text
        base_url = response.url
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                download_url = matches[0]
                if not download_url.startswith('http'):
                    download_url = urljoin(base_url, download_url)
                
                print(f"✅ 找到下载链接 (模式 {i+1}): {download_url}")
                return download_url
        
        print("❌ 未找到匹配的下载链接")
        return None
    
    def download_file(self, url, output_path, description="文件"):
        """模拟人类下载文件"""
        print(f"📥 开始下载 {description}...")
        print(f"   URL: {url}")
        print(f"   保存到: {output_path}")
        
        # 模拟用户点击下载前的确认时间
        self.human_delay(1.0, 3.0)
        
        try:
            # 更新请求头，模拟下载请求
            download_headers = self.session.headers.copy()
            download_headers.update({
                'Referer': url.rsplit('/', 1)[0] + '/',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate'
            })
            
            response = self.session.get(url, stream=True, headers=download_headers)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            start_time = time.time()
            
            print(f"📦 文件大小: {total_size // 1024 // 1024 if total_size > 0 else '未知'}MB")
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 模拟网络波动
                        if random.random() < 0.005:  # 0.5%的概率暂停
                            time.sleep(random.uniform(0.1, 0.3))
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            elapsed_time = time.time() - start_time
                            speed = downloaded_size / elapsed_time / 1024 / 1024 if elapsed_time > 0 else 0
                            print(f"\r📊 进度: {progress:.1f}% ({downloaded_size // 1024 // 1024}MB/{total_size // 1024 // 1024}MB) 速度: {speed:.1f}MB/s", end='', flush=True)
            
            elapsed_time = time.time() - start_time
            print(f"\n✅ {description} 下载完成 (耗时: {elapsed_time:.1f}s)")
            
            # 模拟下载完成后的停顿
            self.human_delay(0.5, 1.5)
            
            return True
            
        except Exception as e:
            print(f"\n❌ {description} 下载失败: {e}")
            self.human_delay(2.0, 4.0)
            return False

def download_windows_ffmpeg_advanced():
    """使用高级模拟下载Windows FFmpeg"""
    print("\n🪟 使用高级浏览器模拟下载 Windows FFmpeg...")
    
    simulator = HumanBrowserSimulator()
    
    # 访问gyan.dev主页
    response = simulator.visit_page(
        "https://www.gyan.dev/ffmpeg/builds/",
        "gyan.dev FFmpeg构建页面"
    )
    
    if not response:
        return False
    
    # 查找下载链接的多种模式
    patterns = [
        r'href="([^"]*ffmpeg-[^"]*-release-essentials\.zip)"',
        r'href="([^"]*ffmpeg-[^"]*-release-full\.zip)"',
        r'href="([^"]*ffmpeg-[^"]*-release[^"]*\.zip)"'
    ]
    
    download_url = simulator.find_download_links(response, patterns)
    if not download_url:
        return False
    
    # 下载文件
    temp_file = TEMP_DIR / "ffmpeg-windows-advanced.zip"
    if not simulator.download_file(download_url, temp_file, "Windows FFmpeg"):
        return False
    
    print("✅ Windows FFmpeg 高级下载完成")
    return temp_file

def ensure_dir(path):
    """确保目录存在"""
    path.mkdir(parents=True, exist_ok=True)

def main():
    """主函数"""
    print("🚀 FFmpeg 高级下载脚本")
    print("=" * 50)
    
    if not REQUESTS_HTML_AVAILABLE:
        print("⚠️ 建议安装 requests-html 以获得更好的JavaScript支持")
        print("   pip install requests-html")
        print()
    
    # 创建目录结构
    ensure_dir(FFMPEG_DIR)
    ensure_dir(TEMP_DIR)
    
    try:
        # 下载Windows FFmpeg
        result = download_windows_ffmpeg_advanced()
        
        if result:
            print("🎉 高级下载完成！")
            print(f"📁 文件保存在: {result}")
            print("\n📝 下一步:")
            print("  1. 解压下载的文件")
            print("  2. 复制到对应的平台目录")
            print("  3. 运行完整的下载脚本")
        else:
            print("❌ 高级下载失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
        sys.exit(1)

if __name__ == "__main__":
    main()
