#!/usr/bin/env node

/**
 * MEEA-VIOFO Windows调试版本构建脚本
 * 
 * 功能：
 * - 构建Windows调试版本，启用日志输出
 * - 生成带有调试标识的安装包
 * - 验证ExifTool和GPS功能
 * - 生成详细的构建报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logDebug(message) {
  log(`🐛 ${message}`, 'magenta');
}

// 执行命令
function runCommand(command, description) {
  logInfo(`执行: ${description}`);
  logDebug(`命令: ${command}`);
  
  try {
    const result = execSync(command, { 
      stdio: 'inherit',
      env: {
        ...process.env,
        DEBUG_MODE: 'true',
        MEEA_DEBUG: 'true',
        NODE_ENV: 'production'
      }
    });
    logSuccess(`完成: ${description}`);
    return result;
  } catch (error) {
    logError(`失败: ${description}`);
    logError(`错误: ${error.message}`);
    throw error;
  }
}

// 检查依赖
function checkDependencies() {
  logInfo('检查构建依赖...');
  
  const requiredCommands = ['node', 'yarn', 'python3'];
  
  for (const cmd of requiredCommands) {
    try {
      execSync(`which ${cmd}`, { stdio: 'ignore' });
      logSuccess(`找到 ${cmd}`);
    } catch (error) {
      logError(`未找到 ${cmd}`);
      throw new Error(`缺少必需的命令: ${cmd}`);
    }
  }
}

// 更新版本号
function updateVersion() {
  logInfo('生成调试版本号...');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 生成带有调试标识的版本号
  const now = new Date();
  const timestamp = now.toISOString().slice(2, 16).replace(/[-:T]/g, '').slice(0, 10);
  const debugVersion = `${packageJson.version}-debug-${timestamp}`;
  
  // 临时更新版本号
  packageJson.version = debugVersion;
  packageJson.build.buildVersion = debugVersion;
  
  // 添加调试标识到产品名称
  packageJson.build.productName = "MEEA-VIOFO-DEBUG";
  packageJson.build.artifactName = "${productName}-${version}-${os}-${arch}.${ext}";
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  
  logSuccess(`版本号更新为: ${debugVersion}`);
  return { originalPackageJson: JSON.parse(fs.readFileSync(packageJsonPath, 'utf8')), debugVersion };
}

// 恢复版本号
function restoreVersion(originalPackageJson) {
  logInfo('恢复原始版本号...');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  fs.writeFileSync(packageJsonPath, JSON.stringify(originalPackageJson, null, 2));
  
  logSuccess('版本号已恢复');
}

// 构建前端
function buildFrontend() {
  logInfo('构建前端应用...');
  runCommand('yarn build', '前端构建');
}

// 设置FFmpeg
function setupFFmpeg() {
  logInfo('设置FFmpeg...');
  runCommand('yarn setup:ffmpeg:current', 'FFmpeg设置');
}

// 构建Windows包
function buildWindows() {
  logInfo('构建Windows调试版本...');
  
  // 设置环境变量并构建
  const buildCommand = process.platform === 'win32' 
    ? 'set DEBUG_MODE=true && set MEEA_DEBUG=true && yarn dist:win:all'
    : 'DEBUG_MODE=true MEEA_DEBUG=true yarn dist:win:all';
    
  runCommand(buildCommand, 'Windows包构建');
}

// 验证构建结果
function verifyBuild() {
  logInfo('验证构建结果...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distDir)) {
    throw new Error('构建目录不存在');
  }
  
  const files = fs.readdirSync(distDir);
  const windowsFiles = files.filter(file => 
    file.includes('win') && (file.endsWith('.exe') || file.endsWith('.zip'))
  );
  
  if (windowsFiles.length === 0) {
    throw new Error('未找到Windows构建文件');
  }
  
  logSuccess(`找到 ${windowsFiles.length} 个Windows构建文件:`);
  windowsFiles.forEach(file => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);
    const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
    logInfo(`  📦 ${file} (${sizeMB} MB)`);
  });
  
  return windowsFiles;
}

// 生成构建报告
function generateReport(debugVersion, buildFiles) {
  logInfo('生成构建报告...');
  
  const reportPath = path.join(__dirname, '..', 'WINDOWS_DEBUG_BUILD_REPORT.md');
  const now = new Date();
  
  const report = `# 🐛 Windows调试版本构建报告

## 📊 构建信息

- **构建时间**: ${now.toLocaleString()}
- **调试版本**: ${debugVersion}
- **环境变量**: DEBUG_MODE=true, MEEA_DEBUG=true
- **平台**: Windows (x64, arm64)

## 📦 构建产物

${buildFiles.map(file => {
  const filePath = path.join(__dirname, '..', 'dist', file);
  const stats = fs.statSync(filePath);
  const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
  return `- **${file}** (${sizeMB} MB)`;
}).join('\n')}

## 🐛 调试功能

### 启用的日志功能
- ✅ GPS数据提取详细日志
- ✅ ExifTool执行过程日志
- ✅ Windows平台特定修复日志
- ✅ 许可证验证详细日志
- ✅ 应用启动信息日志

### 日志标识
- 🐛 [DEBUG] - 调试信息
- ℹ️  [INFO] - 一般信息
- ⚠️  [WARN] - 警告信息
- ❌ [ERROR] - 错误信息

## 🧪 测试建议

### 1. 基本功能测试
\`\`\`bash
# 安装调试版本
./MEEA-VIOFO-DEBUG-${debugVersion}-win-x64.exe

# 启动应用，观察控制台输出
# 应该看到带有 🐛 [DEBUG] 标识的日志
\`\`\`

### 2. GPS功能测试
1. 导入VIOFO视频文件
2. 观察控制台GPS提取日志
3. 验证GPS点位数量和坐标转换
4. 检查ExifTool执行过程

### 3. 日志验证
- 开发环境：所有日志正常输出
- 调试版本：带有特殊标识的日志输出
- 正式版本：完全无日志输出

## ⚠️  重要提醒

**此版本仅用于调试目的，不应发布给最终用户！**

正式发布版本请使用：
\`\`\`bash
yarn dist:win:all  # 不设置DEBUG_MODE
\`\`\`

## 🔧 技术细节

### 环境变量控制
\`\`\`javascript
const isDebugMode = process.env.DEBUG_MODE === 'true' || process.env.MEEA_DEBUG === 'true';
const shouldEnableLogging = isDev || isDebugMode;
\`\`\`

### 日志增强
\`\`\`javascript
if (isPackaged && isDebugMode) {
  console.log = (...args) => originalLog('🐛 [DEBUG]', ...args);
  // ... 其他日志方法
}
\`\`\`

---
*构建时间: ${now.toISOString()}*
`;

  fs.writeFileSync(reportPath, report);
  logSuccess(`构建报告已生成: ${reportPath}`);
}

// 主函数
async function main() {
  log('\n🚀 开始构建Windows调试版本\n', 'cyan');
  
  let originalPackageJson = null;
  
  try {
    // 1. 检查依赖
    checkDependencies();
    
    // 2. 更新版本号
    const versionInfo = updateVersion();
    originalPackageJson = versionInfo.originalPackageJson;
    
    // 3. 设置FFmpeg
    setupFFmpeg();
    
    // 4. 构建前端
    buildFrontend();
    
    // 5. 构建Windows包
    buildWindows();
    
    // 6. 验证构建结果
    const buildFiles = verifyBuild();
    
    // 7. 生成构建报告
    generateReport(versionInfo.debugVersion, buildFiles);
    
    logSuccess('\n🎉 Windows调试版本构建完成！');
    logWarning('⚠️  此版本包含调试日志，仅用于开发调试！');
    
  } catch (error) {
    logError(`构建失败: ${error.message}`);
    process.exit(1);
  } finally {
    // 恢复原始版本号
    if (originalPackageJson) {
      restoreVersion(originalPackageJson);
    }
  }
}

// 运行
if (require.main === module) {
  main();
}

module.exports = { main };
