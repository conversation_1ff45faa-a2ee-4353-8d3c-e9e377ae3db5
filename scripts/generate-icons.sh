#!/bin/bash

# 跨平台图标生成脚本
# 检测操作系统并使用相应的工具

set -e

ICONS_DIR="build/icons"

echo "🎨 开始生成应用图标..."

# 创建图标目录
mkdir -p "$ICONS_DIR"

echo "📁 创建图标目录: $ICONS_DIR"

# 检测操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "🖥️  检测到操作系统: $MACHINE"

# 使用 Node.js 生成所有图标（跨平台解决方案）
echo "🔄 使用 Node.js 生成图标..."
if command -v node >/dev/null 2>&1; then
    node "$(dirname "$0")/generate-icons-node.js"
else
    echo "❌ 错误: 未找到 Node.js"
    echo "请安装 Node.js 或使用预生成的图标文件"

    # 作为备选方案，复制现有的图标文件
    if [ -f "assets/icon.ico" ]; then
        echo "📋 使用现有的图标文件..."
        cp "assets/icon.ico" "$ICONS_DIR/icon.ico" 2>/dev/null || true
    fi

    if [ -f "assets/logo.svg" ]; then
        # 创建基本的占位符文件
        echo "📋 创建占位符图标文件..."
        touch "$ICONS_DIR/icon.png"
        touch "$ICONS_DIR/icon.icns"
        touch "$ICONS_DIR/<EMAIL>"
    fi

    echo "⚠️  警告: 使用了占位符图标，建议安装 Node.js 生成正确的图标"
fi

echo "✅ 图标生成完成！"
echo ""
echo "📋 生成的文件:"
echo "   - $ICONS_DIR/icon.png (Linux, 512x512)"
echo "   - $ICONS_DIR/icon.ico (Windows, 多尺寸)"
echo "   - $ICONS_DIR/icon.icns (macOS, 多尺寸)"
echo "   - $ICONS_DIR/<EMAIL> (高分辨率, 1024x1024)"
echo ""
echo "🚀 现在可以运行构建命令了！"
