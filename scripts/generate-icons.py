#!/usr/bin/env python3
"""
生成 MEEA-VIOFO 应用的全平台图标
从 SVG 源文件生成各种尺寸的图标文件
"""

import os
import sys
from pathlib import Path
import subprocess

def check_dependencies():
    """检查必要的依赖"""
    try:
        import cairosvg
        from PIL import Image
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装依赖:")
        print("pip install cairosvg pillow")
        return False

def svg_to_png(svg_path, png_path, size):
    """将 SVG 转换为指定尺寸的 PNG"""
    import cairosvg
    
    cairosvg.svg2png(
        url=str(svg_path),
        write_to=str(png_path),
        output_width=size,
        output_height=size
    )
    print(f"✅ 生成 {size}x{size} PNG: {png_path}")

def create_ico(png_files, ico_path):
    """从多个 PNG 文件创建 ICO 文件"""
    from PIL import Image
    
    images = []
    for png_file in png_files:
        if os.path.exists(png_file):
            img = Image.open(png_file)
            images.append(img)
    
    if images:
        images[0].save(
            ico_path,
            format='ICO',
            sizes=[(img.width, img.height) for img in images]
        )
        print(f"✅ 生成 ICO: {ico_path}")

def create_icns(png_path, icns_path):
    """创建 macOS ICNS 文件"""
    try:
        # 使用 macOS 的 iconutil 工具
        iconset_path = icns_path.with_suffix('.iconset')
        iconset_path.mkdir(exist_ok=True)
        
        # 定义 macOS 需要的图标尺寸
        sizes = [
            (16, 'icon_16x16.png'),
            (32, '<EMAIL>'),
            (32, 'icon_32x32.png'),
            (64, '<EMAIL>'),
            (128, 'icon_128x128.png'),
            (256, '<EMAIL>'),
            (256, 'icon_256x256.png'),
            (512, '<EMAIL>'),
            (512, 'icon_512x512.png'),
            (1024, '<EMAIL>'),
        ]
        
        from PIL import Image
        base_img = Image.open(png_path)
        
        for size, filename in sizes:
            resized = base_img.resize((size, size), Image.Resampling.LANCZOS)
            resized.save(iconset_path / filename)
        
        # 使用 iconutil 创建 icns
        result = subprocess.run([
            'iconutil', '-c', 'icns', str(iconset_path), '-o', str(icns_path)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 生成 ICNS: {icns_path}")
            # 清理临时文件
            import shutil
            shutil.rmtree(iconset_path)
        else:
            print(f"❌ 生成 ICNS 失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 生成 ICNS 失败: {e}")
        print("注意: ICNS 生成需要在 macOS 系统上运行")

def main():
    """主函数"""
    if not check_dependencies():
        return 1
    
    # 路径设置
    project_root = Path(__file__).parent.parent
    svg_path = project_root / 'assets' / 'logo-new.svg'
    build_icons_dir = project_root / 'build' / 'icons'
    assets_dir = project_root / 'assets'
    public_dir = project_root / 'public'
    
    # 创建目录
    build_icons_dir.mkdir(parents=True, exist_ok=True)
    
    if not svg_path.exists():
        print(f"❌ SVG 文件不存在: {svg_path}")
        return 1
    
    print(f"🎨 开始从 {svg_path} 生成图标...")
    
    # 生成各种尺寸的 PNG
    sizes_and_paths = [
        # electron-builder 需要的尺寸
        (16, build_icons_dir / 'icon-16.png'),
        (32, build_icons_dir / 'icon-32.png'),
        (48, build_icons_dir / 'icon-48.png'),
        (64, build_icons_dir / 'icon-64.png'),
        (128, build_icons_dir / 'icon-128.png'),
        (256, build_icons_dir / 'icon-256.png'),
        (512, build_icons_dir / 'icon-512.png'),
        (1024, build_icons_dir / 'icon-1024.png'),
        
        # 标准图标文件
        (256, build_icons_dir / 'icon.png'),
        (512, build_icons_dir / '<EMAIL>'),
        
        # Web 使用
        (192, public_dir / 'icon-192.png'),
        (512, public_dir / 'icon-512.png'),
        (64, assets_dir / 'icon-64.png'),
    ]
    
    png_files = []
    for size, path in sizes_and_paths:
        try:
            svg_to_png(svg_path, path, size)
            png_files.append(str(path))
        except Exception as e:
            print(f"❌ 生成 {size}x{size} PNG 失败: {e}")
    
    # 生成 ICO 文件 (Windows)
    ico_sizes = [16, 32, 48, 64, 128, 256]
    ico_png_files = [str(build_icons_dir / f'icon-{size}.png') for size in ico_sizes]
    ico_png_files = [f for f in ico_png_files if os.path.exists(f)]
    
    if ico_png_files:
        create_ico(ico_png_files, build_icons_dir / 'icon.ico')
        create_ico(ico_png_files, assets_dir / 'icon.ico')
    
    # 生成 ICNS 文件 (macOS)
    if sys.platform == 'darwin':
        png_512_path = build_icons_dir / 'icon-512.png'
        if png_512_path.exists():
            create_icns(png_512_path, build_icons_dir / 'icon.icns')
    else:
        print("⚠️  ICNS 文件需要在 macOS 系统上生成")
    
    # 更新主 logo 文件
    try:
        svg_to_png(svg_path, assets_dir / 'logo.png', 512)
        svg_to_png(svg_path, public_dir / 'logo.png', 256)
        
        # 复制 SVG
        import shutil
        shutil.copy2(svg_path, assets_dir / 'logo.svg')
        shutil.copy2(svg_path, public_dir / 'logo.svg')
        
        print("✅ 更新主 logo 文件")
    except Exception as e:
        print(f"❌ 更新主 logo 失败: {e}")
    
    print("\n🎉 图标生成完成!")
    print(f"📁 图标文件位置: {build_icons_dir}")
    print("📋 生成的文件:")
    for file in build_icons_dir.glob('*'):
        if file.is_file():
            print(f"   - {file.name}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
