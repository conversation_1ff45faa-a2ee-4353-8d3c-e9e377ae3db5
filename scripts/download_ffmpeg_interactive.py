#!/usr/bin/env python3
"""
FFmpeg 交互式下载脚本
提供用户友好的命令行界面，让用户选择不同的下载方式
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """显示程序横幅"""
    print("=" * 60)
    print("🚀 FFmpeg 自动下载工具")
    print("   模拟真人浏览器行为下载 FFmpeg 和 FFprobe")
    print("=" * 60)

def print_menu():
    """显示主菜单"""
    print("\n📋 请选择下载方式:")
    print()
    print("1. 🔧 环境设置 (首次使用推荐)")
    print("   - 自动安装Python依赖")
    print("   - 检查系统环境")
    print("   - 创建测试脚本")
    print()
    print("2. ⚡ 基础下载 (推荐)")
    print("   - 使用requests库")
    print("   - 兼容性最好")
    print("   - 模拟真实浏览器头")
    print()
    print("3. 🚀 高级下载 (最推荐)")
    print("   - 支持JavaScript渲染")
    print("   - 更真实的浏览器模拟")
    print("   - 智能链接查找")
    print()
    print("4. 🌐 Selenium自动化")
    print("   - 完全模拟真实浏览器")
    print("   - 需要Chrome浏览器")
    print("   - 功能最强大")
    print()
    print("5. 🧪 测试环境")
    print("   - 检查依赖安装")
    print("   - 测试网络连接")
    print("   - 验证功能可用性")
    print()
    print("6. 📊 查看下载状态")
    print("   - 检查已下载的文件")
    print("   - 验证文件完整性")
    print()
    print("0. 🚪 退出程序")
    print()

def run_script(script_name, description):
    """运行指定的脚本"""
    script_path = Path(__file__).parent / script_name
    
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    print(f"\n🚀 启动 {description}...")
    print(f"📁 脚本路径: {script_path}")
    print("-" * 50)
    
    try:
        # 使用当前Python解释器运行脚本
        result = subprocess.run([sys.executable, str(script_path)], check=True)
        print("-" * 50)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print("-" * 50)
        print(f"❌ {description} 失败 (退出码: {e.returncode})")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False

def check_download_status():
    """检查下载状态"""
    print("\n📊 检查FFmpeg下载状态...")
    
    ffmpeg_dir = Path(__file__).parent.parent / "ffmpeg"
    
    if not ffmpeg_dir.exists():
        print("❌ ffmpeg目录不存在")
        return
    
    platforms = {
        "win-x64": ["ffmpeg.exe", "ffprobe.exe"],
        "win-arm64": ["ffmpeg.exe", "ffprobe.exe"],
        "mac-x64": ["ffmpeg", "ffprobe"],
        "mac-arm64": ["ffmpeg", "ffprobe"],
        "linux-x64": ["ffmpeg", "ffprobe"],
        "linux-arm64": ["ffmpeg", "ffprobe"]
    }
    
    total_files = 0
    found_files = 0
    
    print("\n📋 平台文件检查:")
    for platform, files in platforms.items():
        platform_dir = ffmpeg_dir / platform
        print(f"\n🔍 {platform}:")
        
        if not platform_dir.exists():
            print(f"  ❌ 目录不存在")
            continue
        
        for file_name in files:
            file_path = platform_dir / file_name
            total_files += 1
            
            if file_path.exists():
                size_mb = file_path.stat().st_size / 1024 / 1024
                print(f"  ✅ {file_name} ({size_mb:.1f}MB)")
                found_files += 1
            else:
                print(f"  ❌ {file_name} (缺失)")
    
    print(f"\n📊 总体状态: {found_files}/{total_files} 个文件已下载")
    
    if found_files == total_files:
        print("🎉 所有文件下载完成！")
    elif found_files > 0:
        print("⚠️ 部分文件已下载，建议重新运行下载脚本")
    else:
        print("❌ 未找到任何FFmpeg文件，请运行下载脚本")

def show_help():
    """显示帮助信息"""
    print("\n💡 使用建议:")
    print()
    print("🔰 首次使用:")
    print("  1. 选择 '1' 进行环境设置")
    print("  2. 选择 '5' 测试环境")
    print("  3. 选择 '3' 进行高级下载")
    print()
    print("🔄 日常使用:")
    print("  - 推荐使用 '3. 高级下载'")
    print("  - 网络受限时使用 '2. 基础下载'")
    print("  - 特殊需求时使用 '4. Selenium自动化'")
    print()
    print("🛠️ 故障排除:")
    print("  - 下载失败: 检查网络连接和防火墙设置")
    print("  - 依赖错误: 重新运行环境设置")
    print("  - Chrome问题: 确保已安装Chrome浏览器")
    print()
    print("📁 文件位置:")
    print("  - 下载目录: ffmpeg/")
    print("  - 临时文件: ffmpeg/.temp/")
    print("  - 脚本目录: scripts/")

def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用FFmpeg自动下载工具！")
                break
            
            elif choice == "1":
                run_script("setup_download_env.py", "环境设置")
            
            elif choice == "2":
                run_script("download_ffmpeg.py", "基础下载")
            
            elif choice == "3":
                run_script("download_ffmpeg_advanced.py", "高级下载")
            
            elif choice == "4":
                run_script("download_ffmpeg_selenium.py", "Selenium自动化下载")
            
            elif choice == "5":
                test_script = Path(__file__).parent / "test_download_env.py"
                if test_script.exists():
                    run_script("test_download_env.py", "环境测试")
                else:
                    print("❌ 测试脚本不存在，请先运行环境设置")
            
            elif choice == "6":
                check_download_status()
            
            elif choice.lower() in ["h", "help", "?"]:
                show_help()
            
            else:
                print("❌ 无效选项，请输入0-6之间的数字")
            
            # 等待用户确认后继续
            if choice != "0":
                input("\n按回车键继续...")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户退出程序")
            break
        except EOFError:
            print("\n\n👋 程序结束")
            break

if __name__ == "__main__":
    main()
