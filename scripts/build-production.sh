#!/bin/bash

# MEEA-VIOFO 生产环境优化构建脚本
# 专门用于生成小体积、高性能的生产版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
MEEA-VIOFO 生产环境优化构建脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -p, --platform      指定构建平台 (mac|win|linux|all)
    -c, --clean         构建前清理
    --skip-deps         跳过依赖检查
    --dry-run           只显示将要执行的命令

特性:
    ✅ 自动设置生产环境变量
    ✅ 优化包体积
    ✅ 验证 FFmpeg 路径
    ✅ 生成构建报告

示例:
    $0                  # 构建所有平台
    $0 -p mac           # 只构建 macOS
    $0 -c               # 清理后构建

EOF
}

# 默认参数
PLATFORM="all"
CLEAN=false
SKIP_DEPS=false
DRY_RUN=false

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行命令
run_command() {
    local cmd="$1"
    local desc="$2"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] $desc"
        echo "  命令: $cmd"
        return 0
    fi
    
    log_info "$desc"
    if eval "$cmd"; then
        log_success "$desc 完成"
    else
        log_error "$desc 失败"
        exit 1
    fi
}

# 设置生产环境
setup_production_env() {
    log_info "设置生产环境变量..."
    export NODE_ENV=production
    export ELECTRON_IS_DEV=false
    export DEBUG=""
    
    # 增加内存限制
    export NODE_OPTIONS="--max-old-space-size=8192"
    
    log_success "生产环境变量已设置"
}

# 检查依赖
check_dependencies() {
    if [ "$SKIP_DEPS" = true ]; then
        return 0
    fi
    
    log_info "检查构建依赖..."
    
    # 检查 Node.js 版本
    NODE_VERSION=$(node --version | sed 's/v//')
    log_info "Node.js 版本: $NODE_VERSION"
    
    # 检查 Yarn 版本
    YARN_VERSION=$(yarn --version)
    log_info "Yarn 版本: $YARN_VERSION"
    
    # 检查 FFmpeg 路径
    log_info "验证 FFmpeg 配置..."
    if ! yarn fix:ffmpeg > /dev/null 2>&1; then
        log_error "FFmpeg 路径配置有问题"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 清理
clean_build() {
    if [ "$CLEAN" = true ]; then
        log_info "清理构建产物..."
        run_command "rm -rf dist/*" "清理 dist 目录"
        run_command "rm -rf node_modules/.cache" "清理缓存"
    fi
}

# 生成版本号
generate_version() {
    log_info "生成时间戳版本号..."
    run_command "yarn version:generate" "更新版本号"
}

# 构建前端
build_frontend() {
    log_info "构建前端应用（生产模式）..."
    run_command "yarn build" "构建前端"
}

# 构建 Electron
build_electron() {
    local build_cmd=""
    
    case "$PLATFORM" in
        "mac")
            build_cmd="yarn dist:mac:all"
            ;;
        "win")
            build_cmd="yarn dist:win:all"
            ;;
        "linux")
            build_cmd="yarn dist:linux:all"
            ;;
        "all")
            build_cmd="yarn dist:all"
            ;;
        *)
            log_error "不支持的平台: $PLATFORM"
            exit 1
            ;;
    esac
    
    log_info "构建 Electron 应用（$PLATFORM）..."
    run_command "$build_cmd" "构建 Electron 应用"
}

# 生成构建报告
generate_report() {
    if [ "$DRY_RUN" = true ]; then
        return 0
    fi
    
    log_info "生成构建报告..."
    
    local report_file="BUILD_REPORT_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 构建报告

## 构建信息
- 构建时间: $(date)
- 平台: $PLATFORM
- Node.js: $(node --version)
- Yarn: $(yarn --version)
- 版本号: $(node -p "require('./package.json').version")

## 构建产物
EOF
    
    if [ -d "dist" ]; then
        echo "" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        ls -lh dist/*.{dmg,zip,exe,AppImage,tar.gz,deb,rpm} 2>/dev/null | while read line; do
            echo "$line" >> "$report_file"
        done
        echo "\`\`\`" >> "$report_file"
        
        # 计算总大小
        local total_size=$(du -sh dist/ 2>/dev/null | cut -f1)
        echo "" >> "$report_file"
        echo "总大小: $total_size" >> "$report_file"
    fi
    
    log_success "构建报告已生成: $report_file"
}

# 显示结果
show_results() {
    if [ "$DRY_RUN" = true ]; then
        return 0
    fi
    
    log_info "构建完成！"
    
    if [ -d "dist" ]; then
        echo ""
        log_info "构建产物:"
        ls -lh dist/*.{dmg,zip,exe,AppImage,tar.gz,deb,rpm} 2>/dev/null | while read line; do
            echo "  $line"
        done
        
        echo ""
        local total_size=$(du -sh dist/ 2>/dev/null | cut -f1)
        log_info "总大小: $total_size"
    fi
}

# 主函数
main() {
    log_info "开始 MEEA-VIOFO 生产环境构建"
    log_info "平台: $PLATFORM"
    
    setup_production_env
    check_dependencies
    clean_build
    generate_version
    build_frontend
    build_electron
    generate_report
    show_results
    
    log_success "生产环境构建完成！"
}

# 运行
main
