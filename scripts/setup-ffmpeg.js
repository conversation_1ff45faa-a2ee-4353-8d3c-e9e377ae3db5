#!/usr/bin/env node

/**
 * FFmpeg 二进制文件设置脚本
 * 
 * 此脚本帮助从 @ffmpeg-installer 包中复制 FFmpeg 二进制文件到项目的 ffmpeg 目录
 * 用于开发和测试目的
 */

const fs = require('fs');
const path = require('path');

const projectRoot = path.join(__dirname, '..');
const ffmpegDir = path.join(projectRoot, 'ffmpeg');

// 确保 ffmpeg 目录存在
const platforms = ['win-x64', 'win-arm64', 'mac-x64', 'mac-arm64', 'linux-x64', 'linux-arm64'];

platforms.forEach(platform => {
  const platformDir = path.join(ffmpegDir, platform);
  if (!fs.existsSync(platformDir)) {
    fs.mkdirSync(platformDir, { recursive: true });
    console.log(`✅ 创建目录: ${platform}`);
  }
});

// 尝试从 node_modules 复制当前平台的 FFmpeg
function copyCurrentPlatformFFmpeg() {
  try {
    const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
    const ffprobePath = require('@ffprobe-installer/ffprobe').path;
    
    const platform = process.platform;
    const arch = process.arch;
    
    let platformName;
    if (platform === 'darwin') {
      platformName = 'mac';
    } else if (platform === 'win32') {
      platformName = 'win';
    } else {
      platformName = 'linux';
    }
    
    const targetDir = path.join(ffmpegDir, `${platformName}-${arch}`);
    const ffmpegName = platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';
    const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe';
    
    const targetFFmpegPath = path.join(targetDir, ffmpegName);
    const targetFFprobePath = path.join(targetDir, ffprobeName);
    
    // 复制 FFmpeg
    if (fs.existsSync(ffmpegPath)) {
      fs.copyFileSync(ffmpegPath, targetFFmpegPath);
      // 设置执行权限 (Unix 系统)
      if (platform !== 'win32') {
        fs.chmodSync(targetFFmpegPath, 0o755);
      }
      console.log(`✅ 复制 FFmpeg 到: ${targetFFmpegPath}`);
    }
    
    // 复制 FFprobe
    if (fs.existsSync(ffprobePath)) {
      fs.copyFileSync(ffprobePath, targetFFprobePath);
      // 设置执行权限 (Unix 系统)
      if (platform !== 'win32') {
        fs.chmodSync(targetFFprobePath, 0o755);
      }
      console.log(`✅ 复制 FFprobe 到: ${targetFFprobePath}`);
    }
    
    console.log(`\n🎉 当前平台 (${platformName}-${arch}) 的 FFmpeg 设置完成!`);
    
  } catch (error) {
    console.error('❌ 复制当前平台 FFmpeg 失败:', error.message);
    console.log('\n💡 请确保已安装 @ffmpeg-installer/ffmpeg 和 @ffprobe-installer/ffprobe 包');
    console.log('   运行: yarn add @ffmpeg-installer/ffmpeg @ffprobe-installer/ffprobe');
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
📦 FFmpeg 设置脚本

用法:
  node scripts/setup-ffmpeg.js [选项]

选项:
  --help, -h     显示此帮助信息
  --copy-current 复制当前平台的 FFmpeg 二进制文件

说明:
  此脚本会创建 ffmpeg 目录结构，并可以从 @ffmpeg-installer 包中
  复制当前平台的 FFmpeg 二进制文件用于测试。

  对于生产构建，您需要手动下载所有平台的 FFmpeg 二进制文件：
  
  📁 ffmpeg/
  ├── win-x64/          (Windows x64)
  ├── win-arm64/        (Windows ARM64)
  ├── mac-x64/          (macOS Intel)
  ├── mac-arm64/        (macOS Apple Silicon)
  ├── linux-x64/        (Linux x64)
  └── linux-arm64/      (Linux ARM64)

下载地址:
  • Windows: https://ffmpeg.org/download.html#build-windows
  • macOS: https://ffmpeg.org/download.html#build-mac
  • Linux: https://ffmpeg.org/download.html#build-linux
`);
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  console.log('🚀 开始设置 FFmpeg 目录结构...\n');
  
  if (args.includes('--copy-current')) {
    copyCurrentPlatformFFmpeg();
  } else {
    console.log('✅ FFmpeg 目录结构已创建');
    console.log('\n💡 使用 --copy-current 参数可以复制当前平台的 FFmpeg 二进制文件');
    console.log('   或使用 --help 查看详细说明');
  }
}

if (require.main === module) {
  main();
}

module.exports = { copyCurrentPlatformFFmpeg, showHelp };
