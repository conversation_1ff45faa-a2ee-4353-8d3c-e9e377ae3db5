#!/bin/bash

# MEEA-VIOFO 全平台构建脚本
# 用法: ./scripts/build-all.sh [选项]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
MEEA-VIOFO 全平台构建脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -p, --platform      指定构建平台 (mac|win|linux|all)
    -a, --arch          指定架构 (x64|arm64|all)
    -c, --clean         构建前清理
    -s, --setup-ffmpeg  重新设置 FFmpeg
    -v, --verbose       详细输出
    --skip-deps         跳过依赖检查
    --skip-build        跳过前端构建
    --dry-run           只显示将要执行的命令

示例:
    $0                          # 构建所有平台和架构
    $0 -p mac                   # 只构建 macOS
    $0 -p win -a x64            # 只构建 Windows x64
    $0 -c -s                    # 清理并重新设置 FFmpeg 后构建
    $0 --dry-run                # 预览构建命令

EOF
}

# 默认参数
PLATFORM="all"
ARCH="all"
CLEAN=false
SETUP_FFMPEG=false
VERBOSE=false
SKIP_DEPS=false
SKIP_BUILD=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -a|--arch)
            ARCH="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -s|--setup-ffmpeg)
            SETUP_FFMPEG=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行命令函数
run_command() {
    local cmd="$1"
    local desc="$2"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] $desc"
        echo "  命令: $cmd"
        return 0
    fi
    
    log_info "$desc"
    if [ "$VERBOSE" = true ]; then
        echo "  执行: $cmd"
    fi
    
    if eval "$cmd"; then
        log_success "$desc 完成"
    else
        log_error "$desc 失败"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    if [ "$SKIP_DEPS" = true ]; then
        log_info "跳过依赖检查"
        return 0
    fi
    
    log_info "检查依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查 Yarn
    if ! command -v yarn &> /dev/null; then
        log_error "Yarn 未安装"
        exit 1
    fi
    
    # 检查版本
    NODE_VERSION=$(node --version | sed 's/v//')
    YARN_VERSION=$(yarn --version)
    
    log_info "Node.js 版本: $NODE_VERSION"
    log_info "Yarn 版本: $YARN_VERSION"
    
    # 检查 node_modules
    if [ ! -d "node_modules" ]; then
        log_warning "node_modules 不存在，正在安装依赖..."
        run_command "yarn install" "安装项目依赖"
    fi
}

# 清理函数
clean_build() {
    if [ "$CLEAN" = true ]; then
        log_info "清理构建产物..."
        run_command "yarn cleanup" "清理构建目录"
        run_command "rm -rf dist/*" "清理 dist 目录"
    fi
}

# 设置 FFmpeg
setup_ffmpeg() {
    if [ "$SETUP_FFMPEG" = true ]; then
        log_info "重新设置 FFmpeg..."
        run_command "yarn cleanup:ffmpeg" "清理 FFmpeg 文件"
        run_command "yarn setup:ffmpeg" "创建 FFmpeg 目录结构"
        run_command "yarn setup:ffmpeg:current" "复制当前平台 FFmpeg"
        
        # 检查是否需要下载其他平台的 FFmpeg
        if [ "$PLATFORM" = "all" ] || [ "$PLATFORM" != "mac" ]; then
            log_warning "跨平台构建需要下载所有平台的 FFmpeg 文件"
            log_info "请运行: yarn download:ffmpeg"
        fi
    fi
}

# 构建前端
build_frontend() {
    if [ "$SKIP_BUILD" = true ]; then
        log_info "跳过前端构建"
        return 0
    fi
    
    run_command "yarn build" "构建前端应用"
}

# 构建 Electron 应用
build_electron() {
    local build_cmd=""
    
    case "$PLATFORM" in
        "mac")
            if [ "$ARCH" = "all" ]; then
                build_cmd="yarn dist:mac:all"
            else
                build_cmd="yarn dist:mac"
            fi
            ;;
        "win")
            if [ "$ARCH" = "all" ]; then
                build_cmd="yarn dist:win:all"
            else
                build_cmd="yarn dist:win"
            fi
            ;;
        "linux")
            if [ "$ARCH" = "all" ]; then
                build_cmd="yarn dist:linux:all"
            else
                build_cmd="yarn dist:linux"
            fi
            ;;
        "all")
            if [ "$ARCH" = "all" ]; then
                build_cmd="yarn dist:all"
            else
                build_cmd="yarn dist"
            fi
            ;;
        *)
            log_error "不支持的平台: $PLATFORM"
            exit 1
            ;;
    esac
    
    run_command "$build_cmd" "构建 Electron 应用 ($PLATFORM-$ARCH)"
}

# 显示构建结果
show_results() {
    if [ "$DRY_RUN" = true ]; then
        return 0
    fi
    
    log_info "构建完成！"
    
    if [ -d "dist" ]; then
        log_info "构建产物:"
        ls -lh dist/*.{dmg,zip,exe,AppImage,tar.gz,deb,rpm} 2>/dev/null | while read line; do
            echo "  $line"
        done
    fi
}

# 主函数
main() {
    log_info "开始 MEEA-VIOFO 构建流程"
    log_info "平台: $PLATFORM, 架构: $ARCH"
    
    check_dependencies
    clean_build
    setup_ffmpeg
    build_frontend
    build_electron
    show_results
    
    log_success "构建流程完成！"
}

# 运行主函数
main
