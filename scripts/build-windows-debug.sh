#!/bin/bash

# MEEA-VIOFO Windows调试版本构建脚本
# 功能：构建带有日志输出的Windows调试版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_debug() {
    echo -e "${MAGENTA}🐛 $1${NC}"
}

log_header() {
    echo -e "${CYAN}🚀 $1${NC}"
}

# 执行命令
run_command() {
    local cmd="$1"
    local desc="$2"
    
    log_info "执行: $desc"
    log_debug "命令: $cmd"
    
    if eval "$cmd"; then
        log_success "完成: $desc"
    else
        log_error "失败: $desc"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    local deps=("node" "yarn")
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" >/dev/null 2>&1; then
            log_success "找到 $dep"
        else
            log_error "未找到 $dep"
            exit 1
        fi
    done
}

# 备份package.json
backup_package_json() {
    log_info "备份package.json..."
    cp package.json package.json.backup
    log_success "已备份package.json"
}

# 恢复package.json
restore_package_json() {
    if [ -f "package.json.backup" ]; then
        log_info "恢复package.json..."
        mv package.json.backup package.json
        log_success "已恢复package.json"
    fi
}

# 更新版本号为调试版本
update_debug_version() {
    log_info "生成调试版本号..."

    # 生成时间戳
    local timestamp=$(date +"%y%m%d-%H%M")
    local debug_version="25.07.18-debug-$timestamp"

    # 使用node脚本更新package.json
    node -e "
        const fs = require('fs');
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        pkg.version = '$debug_version';
        pkg.build.buildVersion = '$debug_version';
        pkg.build.productName = 'MEEA-VIOFO-DEBUG';
        pkg.build.artifactName = '\${productName}-Setup-\${version}-\${os}-\${arch}.\${ext}';

        // 添加调试标识到应用ID
        pkg.build.appId = 'com.meea.viofo.debug';

        // 确保Windows配置正确
        if (!pkg.build.win) pkg.build.win = {};
        pkg.build.win.artifactName = '\${productName}-Setup-\${version}-\${os}-\${arch}.\${ext}';

        fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
        console.log('调试版本号: $debug_version');
    "

    log_success "版本号更新为: $debug_version"

    # 验证更新
    local updated_name=$(node -e "console.log(JSON.parse(require('fs').readFileSync('package.json', 'utf8')).build.productName)")
    log_debug "产品名称已更新为: $updated_name"
}

# 设置FFmpeg
setup_ffmpeg() {
    log_info "设置FFmpeg..."
    run_command "yarn setup:ffmpeg:current" "FFmpeg设置"
}

# 构建前端
build_frontend() {
    log_info "构建前端应用..."
    run_command "yarn build" "前端构建"
}

# 构建Windows包（调试模式）
build_windows_debug() {
    log_info "构建Windows调试版本..."
    
    # 设置调试环境变量
    export DEBUG_MODE=true
    export MEEA_DEBUG=true
    export NODE_ENV=production
    
    log_debug "环境变量: DEBUG_MODE=$DEBUG_MODE, MEEA_DEBUG=$MEEA_DEBUG"
    
    run_command "yarn dist:win:all" "Windows调试包构建"
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    if [ ! -d "dist" ]; then
        log_error "构建目录不存在"
        exit 1
    fi
    
    local win_files=$(find dist -name "*win*" \( -name "*.exe" -o -name "*.zip" \) 2>/dev/null || true)
    
    if [ -z "$win_files" ]; then
        log_error "未找到Windows构建文件"
        exit 1
    fi
    
    log_success "找到Windows构建文件:"
    echo "$win_files" | while read -r file; do
        if [ -f "$file" ]; then
            local size=$(du -h "$file" | cut -f1)
            log_info "  📦 $(basename "$file") ($size)"
        fi
    done
}

# 生成构建报告
generate_report() {
    log_info "生成构建报告..."
    
    local report_file="WINDOWS_DEBUG_BUILD_REPORT.md"
    local build_time=$(date)
    local version=$(node -e "console.log(JSON.parse(require('fs').readFileSync('package.json', 'utf8')).version)")
    
    cat > "$report_file" << EOF
# 🐛 Windows调试版本构建报告

## 📊 构建信息

- **构建时间**: $build_time
- **调试版本**: $version
- **环境变量**: DEBUG_MODE=true, MEEA_DEBUG=true
- **平台**: Windows (x64, arm64)

## 📦 构建产物

$(find dist -name "*win*" \( -name "*.exe" -o -name "*.zip" \) 2>/dev/null | while read -r file; do
    if [ -f "$file" ]; then
        local size=$(du -h "$file" | cut -f1)
        echo "- **$(basename "$file")** ($size)"
    fi
done)

## 🐛 调试功能

### 启用的日志功能
- ✅ GPS数据提取详细日志
- ✅ ExifTool执行过程日志  
- ✅ Windows平台特定修复日志
- ✅ 许可证验证详细日志
- ✅ 应用启动信息日志

### 日志标识
- 🐛 [DEBUG] - 调试信息
- ℹ️  [INFO] - 一般信息
- ⚠️  [WARN] - 警告信息
- ❌ [ERROR] - 错误信息

## 🧪 测试建议

### 1. 基本功能测试
\`\`\`bash
# 安装调试版本
./MEEA-VIOFO-DEBUG-$version-win-x64.exe

# 启动应用，观察控制台输出
# 应该看到带有 🐛 [DEBUG] 标识的日志
\`\`\`

### 2. GPS功能测试
1. 导入VIOFO视频文件
2. 观察控制台GPS提取日志
3. 验证GPS点位数量和坐标转换
4. 检查ExifTool执行过程

## ⚠️  重要提醒

**此版本仅用于调试目的，不应发布给最终用户！**

正式发布版本请使用：
\`\`\`bash
yarn dist:win:all  # 不设置DEBUG_MODE
\`\`\`

---
*构建时间: $build_time*
EOF

    log_success "构建报告已生成: $report_file"
}

# 清理函数
cleanup() {
    log_info "执行清理..."
    restore_package_json
    unset DEBUG_MODE MEEA_DEBUG NODE_ENV
}

# 设置退出时清理
trap cleanup EXIT

# 主函数
main() {
    log_header "开始构建Windows调试版本"
    echo
    
    # 1. 检查依赖
    check_dependencies
    
    # 2. 备份配置
    backup_package_json
    
    # 3. 更新版本号
    update_debug_version
    
    # 4. 设置FFmpeg
    setup_ffmpeg
    
    # 5. 构建前端
    build_frontend
    
    # 6. 构建Windows包
    build_windows_debug
    
    # 7. 验证构建结果
    verify_build
    
    # 8. 生成构建报告
    generate_report
    
    echo
    log_success "🎉 Windows调试版本构建完成！"
    log_warning "⚠️  此版本包含调试日志，仅用于开发调试！"
    echo
    log_info "构建产物位于 dist/ 目录"
    log_info "构建报告: WINDOWS_DEBUG_BUILD_REPORT.md"
}

# 显示帮助
show_help() {
    cat << EOF
MEEA-VIOFO Windows调试版本构建脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息

功能:
    ✅ 构建带有调试日志的Windows版本
    ✅ 自动生成调试版本号
    ✅ 设置调试环境变量
    ✅ 验证构建结果
    ✅ 生成详细构建报告

环境变量:
    DEBUG_MODE=true     启用调试模式
    MEEA_DEBUG=true     启用MEEA调试日志

示例:
    $0                  # 构建Windows调试版本
    $0 --help           # 显示帮助

注意:
    - 此脚本会临时修改package.json，构建完成后自动恢复
    - 生成的调试版本仅用于开发调试，不应发布给最终用户
    - 正式版本请使用: yarn dist:win:all
EOF
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main
        ;;
esac
