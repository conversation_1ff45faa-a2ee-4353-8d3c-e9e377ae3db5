@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: MEEA-VIOFO 完全卸载脚本
:: 版本: 1.0
:: 作者: PerccyKing

echo.
echo ========================================
echo    MEEA-VIOFO 完全卸载工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  警告：需要管理员权限才能完全卸载
    echo 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo ✅ 管理员权限验证通过
echo.

:: 确认卸载
echo 🔍 此脚本将完全删除 MEEA-VIOFO 及其所有数据
echo    包括：程序文件、用户数据、配置文件、日志文件
echo.
set /p confirm="确定要继续吗？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 取消卸载操作
    pause
    exit /b 0
)

echo.
echo 🚀 开始卸载 MEEA-VIOFO...
echo.

:: 1. 停止相关进程
echo 1️⃣ 停止相关进程...
taskkill /f /im "MEEA-VIOFO.exe" >nul 2>&1
taskkill /f /im "meea-viofo-all.exe" >nul 2>&1
taskkill /f /im "*MEEA*.exe" >nul 2>&1
taskkill /f /im "*viofo*.exe" >nul 2>&1
echo    ✅ 进程已停止

:: 2. 删除程序文件
echo.
echo 2️⃣ 删除程序文件...

set "paths[0]=%ProgramFiles%\MEEA-VIOFO"
set "paths[1]=%ProgramFiles(x86)%\MEEA-VIOFO"
set "paths[2]=%ProgramFiles%\Video\MEEA-VIOFO"
set "paths[3]=%ProgramFiles(x86)%\Video\MEEA-VIOFO"
set "paths[4]=%ProgramFiles%\Video\MEEA-VIOFO-DEBUG"
set "paths[5]=%ProgramFiles(x86)%\Video\MEEA-VIOFO-DEBUG"

for /l %%i in (0,1,5) do (
    if exist "!paths[%%i]!" (
        echo    删除: !paths[%%i]!
        rmdir /s /q "!paths[%%i]!" >nul 2>&1
    )
)
echo    ✅ 程序文件已删除

:: 3. 删除用户数据
echo.
echo 3️⃣ 删除用户数据...

set "userPaths[0]=%APPDATA%\MEEA-VIOFO"
set "userPaths[1]=%APPDATA%\meea-viofo-all"
set "userPaths[2]=%APPDATA%\MEEA-VIOFO-DEBUG"
set "userPaths[3]=%LOCALAPPDATA%\MEEA-VIOFO"
set "userPaths[4]=%LOCALAPPDATA%\meea-viofo-all"
set "userPaths[5]=%LOCALAPPDATA%\MEEA-VIOFO-DEBUG"

for /l %%i in (0,1,5) do (
    if exist "!userPaths[%%i]!" (
        echo    删除: !userPaths[%%i]!
        rmdir /s /q "!userPaths[%%i]!" >nul 2>&1
    )
)
echo    ✅ 用户数据已删除

:: 4. 清理注册表
echo.
echo 4️⃣ 清理注册表...
reg delete "HKCU\Software\MEEA-VIOFO" /f >nul 2>&1
reg delete "HKCU\Software\meea-viofo-all" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\MEEA-VIOFO" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\meea-viofo-all" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\MEEA-VIOFO" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\meea-viofo-all" /f >nul 2>&1
echo    ✅ 注册表已清理

:: 5. 清理开始菜单和桌面
echo.
echo 5️⃣ 清理快捷方式...

:: 开始菜单
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO" (
    rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO" >nul 2>&1
)
if exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO" (
    rmdir /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO" >nul 2>&1
)

:: 桌面快捷方式
del "%USERPROFILE%\Desktop\MEEA-VIOFO.lnk" >nul 2>&1
del "%PUBLIC%\Desktop\MEEA-VIOFO.lnk" >nul 2>&1

echo    ✅ 快捷方式已清理

:: 6. 清理临时文件
echo.
echo 6️⃣ 清理临时文件...
del /q "%TEMP%\MEEA-VIOFO*" >nul 2>&1
del /q "%TEMP%\meea-viofo*" >nul 2>&1
del /q "%WINDIR%\Temp\MEEA-VIOFO*" >nul 2>&1
del /q "%WINDIR%\Temp\meea-viofo*" >nul 2>&1
echo    ✅ 临时文件已清理

:: 7. 清理卸载信息
echo.
echo 7️⃣ 清理卸载信息...

:: 查找并删除卸载注册表项
for /f "tokens=*" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s /k 2^>nul ^| findstr /i "MEEA-VIOFO\|meea-viofo-all"') do (
    reg delete "%%a" /f >nul 2>&1
)

for /f "tokens=*" %%a in ('reg query "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall" /s /k 2^>nul ^| findstr /i "MEEA-VIOFO\|meea-viofo-all"') do (
    reg delete "%%a" /f >nul 2>&1
)

echo    ✅ 卸载信息已清理

:: 完成
echo.
echo ========================================
echo 🎉 MEEA-VIOFO 卸载完成！
echo ========================================
echo.
echo ✅ 已删除的内容：
echo    • 程序文件
echo    • 用户数据和配置
echo    • 注册表项
echo    • 快捷方式
echo    • 临时文件
echo    • 卸载信息
echo.
echo 💡 建议：
echo    • 重启计算机以确保完全清理
echo    • 如需重新安装，请下载最新版本
echo.
echo 📞 技术支持：<EMAIL>
echo.

:: 询问是否重启
set /p restart="是否立即重启计算机？(Y/N): "
if /i "%restart%"=="Y" (
    echo 正在重启计算机...
    shutdown /r /t 10 /c "MEEA-VIOFO卸载完成，系统将在10秒后重启"
) else (
    echo 请稍后手动重启计算机以完成清理
)

echo.
pause
