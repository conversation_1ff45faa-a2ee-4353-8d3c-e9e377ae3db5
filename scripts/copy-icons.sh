#!/bin/bash

# 复制预生成的图标文件
# 这个脚本用于在 CI/CD 环境中确保图标文件存在

set -e

ICONS_DIR="build/icons"

echo "🎨 准备应用图标..."

# 创建图标目录
mkdir -p "$ICONS_DIR"

echo "📁 确保图标目录存在: $ICONS_DIR"

# 检查所有必需的图标文件是否存在
REQUIRED_FILES=(
    "$ICONS_DIR/icon.png"
    "$ICONS_DIR/icon.ico"
    "$ICONS_DIR/icon.icns"
    "$ICONS_DIR/<EMAIL>"
)

ALL_EXIST=true

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        ALL_EXIST=false
    else
        echo "✅ 找到文件: $file ($(du -h "$file" | cut -f1))"
    fi
done

if [ "$ALL_EXIST" = true ]; then
    echo ""
    echo "🎉 所有图标文件都已准备就绪！"
    echo ""
    echo "📋 图标文件列表:"
    echo "   - $ICONS_DIR/icon.png (Linux, 512x512)"
    echo "   - $ICONS_DIR/icon.ico (Windows, 多尺寸)"
    echo "   - $ICONS_DIR/icon.icns (macOS, 多尺寸)"
    echo "   - $ICONS_DIR/<EMAIL> (高分辨率, 1024x1024)"
    echo ""
    echo "🚀 可以开始构建了！"
else
    echo ""
    echo "❌ 部分图标文件缺失，请运行完整的图标生成脚本"
    echo "   ./scripts/generate-icons.sh"
    exit 1
fi
