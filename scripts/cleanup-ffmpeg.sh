#!/bin/bash

# FFmpeg 清理脚本
# 删除错误的和空的 FFmpeg 文件

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FFMPEG_DIR="$PROJECT_ROOT/ffmpeg"

echo "🧹 清理错误的 FFmpeg 文件..."
echo "📁 FFmpeg 目录: $FFMPEG_DIR"

# 检查当前文件状态
echo ""
echo "📊 当前文件状态:"
find "$FFMPEG_DIR" -name "ffmpeg*" -exec ls -lh {} \; 2>/dev/null || echo "未找到 FFmpeg 文件"

echo ""
echo "🔍 分析问题文件..."

# 检查 Linux 文件是否是错误的 macOS 文件
LINUX_X64_SIZE=$(stat -f%z "$FFMPEG_DIR/linux-x64/ffmpeg" 2>/dev/null || echo "0")
LINUX_ARM64_SIZE=$(stat -f%z "$FFMPEG_DIR/linux-arm64/ffmpeg" 2>/dev/null || echo "0")
MAC_SIZE=$(stat -f%z "$FFMPEG_DIR/mac-arm64/ffmpeg" 2>/dev/null || echo "0")

echo "Linux x64 ffmpeg 大小: $LINUX_X64_SIZE bytes"
echo "Linux ARM64 ffmpeg 大小: $LINUX_ARM64_SIZE bytes"
echo "macOS ffmpeg 大小: $MAC_SIZE bytes"

# 如果 Linux 文件和 macOS 文件大小相同，说明是错误复制的
NEED_CLEANUP=false

if [ "$LINUX_X64_SIZE" -eq "$MAC_SIZE" ] && [ "$MAC_SIZE" -gt 0 ]; then
    echo "⚠️  Linux x64 文件是错误的 macOS 文件"
    NEED_CLEANUP=true
fi

if [ "$LINUX_ARM64_SIZE" -eq "$MAC_SIZE" ] && [ "$MAC_SIZE" -gt 0 ]; then
    echo "⚠️  Linux ARM64 文件是错误的 macOS 文件"
    NEED_CLEANUP=true
fi

# 检查 Windows 文件是否为空
WIN_X64_SIZE=$(stat -f%z "$FFMPEG_DIR/win-x64/ffmpeg.exe" 2>/dev/null || echo "0")
WIN_ARM64_SIZE=$(stat -f%z "$FFMPEG_DIR/win-arm64/ffmpeg.exe" 2>/dev/null || echo "0")

if [ "$WIN_X64_SIZE" -eq 0 ]; then
    echo "⚠️  Windows x64 文件是空文件"
    NEED_CLEANUP=true
fi

if [ "$WIN_ARM64_SIZE" -eq 0 ]; then
    echo "⚠️  Windows ARM64 文件是空文件"
    NEED_CLEANUP=true
fi

if [ "$NEED_CLEANUP" = false ]; then
    echo "✅ 所有文件看起来都正确，无需清理"
    exit 0
fi

echo ""
read -p "🤔 是否要清理这些错误的文件？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 取消清理操作"
    exit 0
fi

echo ""
echo "🗑️  开始清理..."

# 删除错误的 Linux 文件
if [ "$LINUX_X64_SIZE" -eq "$MAC_SIZE" ] && [ "$MAC_SIZE" -gt 0 ]; then
    echo "🗑️  删除错误的 Linux x64 文件..."
    rm -f "$FFMPEG_DIR/linux-x64/ffmpeg" "$FFMPEG_DIR/linux-x64/ffprobe"
fi

if [ "$LINUX_ARM64_SIZE" -eq "$MAC_SIZE" ] && [ "$MAC_SIZE" -gt 0 ]; then
    echo "🗑️  删除错误的 Linux ARM64 文件..."
    rm -f "$FFMPEG_DIR/linux-arm64/ffmpeg" "$FFMPEG_DIR/linux-arm64/ffprobe"
fi

# 删除空的 Windows 文件
if [ "$WIN_X64_SIZE" -eq 0 ]; then
    echo "🗑️  删除空的 Windows x64 文件..."
    rm -f "$FFMPEG_DIR/win-x64/ffmpeg.exe" "$FFMPEG_DIR/win-x64/ffprobe.exe"
fi

if [ "$WIN_ARM64_SIZE" -eq 0 ]; then
    echo "🗑️  删除空的 Windows ARM64 文件..."
    rm -f "$FFMPEG_DIR/win-arm64/ffmpeg.exe" "$FFMPEG_DIR/win-arm64/ffprobe.exe"
fi

echo ""
echo "✅ 清理完成！"

echo ""
echo "📥 现在需要手动下载以下文件："
echo ""

# 检查哪些文件需要下载
if [ ! -f "$FFMPEG_DIR/win-x64/ffmpeg.exe" ]; then
    echo "🪟 Windows FFmpeg:"
    echo "   下载: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
    echo "   解压后复制 bin/ffmpeg.exe 和 bin/ffprobe.exe 到:"
    echo "   - $FFMPEG_DIR/win-x64/"
    echo "   - $FFMPEG_DIR/win-arm64/"
    echo ""
fi

if [ ! -f "$FFMPEG_DIR/linux-x64/ffmpeg" ]; then
    echo "🐧 Linux x64 FFmpeg:"
    echo "   下载: https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz"
    echo "   解压后复制 ffmpeg 和 ffprobe 到: $FFMPEG_DIR/linux-x64/"
    echo "   记住设置执行权限: chmod +x $FFMPEG_DIR/linux-x64/ffmpeg $FFMPEG_DIR/linux-x64/ffprobe"
    echo ""
fi

if [ ! -f "$FFMPEG_DIR/linux-arm64/ffmpeg" ]; then
    echo "🐧 Linux ARM64 FFmpeg:"
    echo "   下载: https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz"
    echo "   解压后复制 ffmpeg 和 ffprobe 到: $FFMPEG_DIR/linux-arm64/"
    echo "   记住设置执行权限: chmod +x $FFMPEG_DIR/linux-arm64/ffmpeg $FFMPEG_DIR/linux-arm64/ffprobe"
    echo ""
fi

echo "📋 下载完成后运行以下命令验证:"
echo "   yarn test:ffmpeg"
echo ""
echo "📖 详细说明请查看: FFMPEG_DOWNLOAD_LIST.md"
