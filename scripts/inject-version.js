#!/usr/bin/env node

/**
 * 构建时版本注入脚本
 * 在构建过程中自动获取版本号并注入到应用程序中
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function getGitInfo() {
  try {
    const gitHash = execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();

    let gitTag = null;
    try {
      gitTag = execSync('git describe --tags --abbrev=0', { encoding: 'utf8' }).trim();
    } catch (e) {
      // 没有标签是正常的
    }

    return { gitHash, gitBranch, gitTag };
  } catch (error) {
    console.warn('⚠️ 无法获取Git信息:', error.message);
    return { gitHash: 'unknown', gitBranch: 'unknown', gitTag: null };
  }
}

function generateBuildInfo() {
  console.log('🔧 生成构建信息...');

  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const gitInfo = getGitInfo();
  const buildTime = new Date();

  // 简化的构建信息，主要保留版本号和基本信息
  const buildInfo = {
    version: packageJson.version,
    buildTime: buildTime.toISOString(),
    buildDateTime: buildTime.toLocaleString('zh-CN'),
    gitHash: gitInfo.gitHash
  };

  console.log('📊 构建信息:', {
    version: buildInfo.version,
    buildTime: buildInfo.buildDateTime,
    gitHash: buildInfo.gitHash
  });

  return buildInfo;
}

function injectVersionToElectron(buildInfo) {
  console.log('💉 注入版本信息到Electron主进程...');
  
  // 创建版本信息文件
  const versionFilePath = path.join(__dirname, '..', 'electron', 'build-info.json');
  fs.writeFileSync(versionFilePath, JSON.stringify(buildInfo, null, 2));
  
  // 更新main.js，添加版本信息获取功能
  const mainJsPath = path.join(__dirname, '..', 'electron', 'main.js');
  let mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
  
  // 检查是否已经添加了版本信息导入
  if (!mainJsContent.includes('build-info.json')) {
    // 在文件顶部添加版本信息导入
    const versionImport = `const buildInfo = require('./build-info.json');\n`;
    mainJsContent = versionImport + mainJsContent;
    
    // 在app.whenReady中添加版本信息日志
    const versionLog = `
  // 显示构建信息
  console.log('🚀 应用启动信息:');
  console.log('   版本:', buildInfo.version);
  console.log('   构建时间:', buildInfo.buildDateTime);
  console.log('   Git提交:', buildInfo.gitHash);
  console.log('   Git分支:', buildInfo.gitBranch);
  console.log('   平台:', buildInfo.platform, buildInfo.arch);
`;
    
    mainJsContent = mainJsContent.replace(
      'app.whenReady().then(() => {',
      `app.whenReady().then(() => {${versionLog}`
    );
    
    fs.writeFileSync(mainJsPath, mainJsContent);
    console.log('✅ 已更新main.js');
  }
  
  console.log('✅ 版本信息已注入到Electron');
}

function injectVersionToRenderer(buildInfo) {
  console.log('💉 注入版本信息到渲染进程...');
  
  // 创建前端版本信息文件
  const frontendVersionPath = path.join(__dirname, '..', 'src', 'build-info.json');
  fs.writeFileSync(frontendVersionPath, JSON.stringify(buildInfo, null, 2));
  
  // 创建TypeScript声明文件
  const versionTypesPath = path.join(__dirname, '..', 'src', 'build-info.d.ts');
  const typesContent = `export interface BuildInfo {
  version: string;
  name: string;
  description: string;
  author: string;
  buildTime: string;
  buildTimestamp: number;
  buildDate: string;
  buildDateTime: string;
  platform: string;
  arch: string;
  nodeVersion: string;
  electronVersion: string;
  gitHash: string;
  gitBranch: string;
  gitTag: string | null;
}

declare const buildInfo: BuildInfo;
export default buildInfo;
`;
  
  fs.writeFileSync(versionTypesPath, typesContent);
  
  console.log('✅ 版本信息已注入到渲染进程');
}

function updateLicenseService(buildInfo) {
  console.log('💉 更新许可证服务版本信息...');

  const licenseServicePath = path.join(__dirname, '..', 'electron', 'license-service.js');

  if (!fs.existsSync(licenseServicePath)) {
    console.warn('⚠️ 许可证服务文件不存在');
    return;
  }

  console.log('✅ 许可证服务已包含版本信息，无需更新');
}

function createVersionScript() {
  console.log('📝 创建版本获取脚本...');
  
  const versionScriptContent = `#!/usr/bin/env node

/**
 * 获取当前应用版本信息的工具脚本
 */

const fs = require('fs');
const path = require('path');

function getCurrentVersion() {
  try {
    const buildInfoPath = path.join(__dirname, '..', 'electron', 'build-info.json');
    if (fs.existsSync(buildInfoPath)) {
      const buildInfo = JSON.parse(fs.readFileSync(buildInfoPath, 'utf8'));
      return buildInfo;
    }
  } catch (error) {
    console.warn('⚠️ 无法读取构建信息:', error.message);
  }
  
  // 回退到package.json
  try {
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return {
      version: packageJson.version,
      name: packageJson.name,
      buildTime: 'unknown'
    };
  } catch (error) {
    console.error('❌ 无法获取版本信息:', error.message);
    return null;
  }
}

function main() {
  const versionInfo = getCurrentVersion();
  
  if (!versionInfo) {
    console.error('❌ 无法获取版本信息');
    process.exit(1);
  }
  
  console.log('📊 当前版本信息:');
  console.log('   应用名称:', versionInfo.name || 'unknown');
  console.log('   版本号:', versionInfo.version);
  console.log('   构建时间:', versionInfo.buildDateTime || versionInfo.buildTime || 'unknown');
  console.log('   Git提交:', versionInfo.gitHash || 'unknown');
  console.log('   Git分支:', versionInfo.gitBranch || 'unknown');
  
  return versionInfo;
}

if (require.main === module) {
  main();
}

module.exports = { getCurrentVersion };
`;
  
  const versionScriptPath = path.join(__dirname, 'get-version.js');
  fs.writeFileSync(versionScriptPath, versionScriptContent);
  
  console.log('✅ 版本获取脚本已创建');
}

function main() {
  console.log('🚀 开始构建时版本注入...');
  
  try {
    // 生成构建信息
    const buildInfo = generateBuildInfo();
    
    // 注入到Electron主进程
    injectVersionToElectron(buildInfo);
    
    // 注入到渲染进程
    injectVersionToRenderer(buildInfo);
    
    // 更新许可证服务
    updateLicenseService(buildInfo);
    
    // 创建版本获取脚本
    createVersionScript();
    
    console.log('\n🎉 版本注入完成！');
    console.log('📊 构建信息:');
    console.log('   版本:', buildInfo.version);
    console.log('   构建时间:', buildInfo.buildDateTime);
    console.log('   Git信息:', buildInfo.gitHash, buildInfo.gitBranch);
    
  } catch (error) {
    console.error('❌ 版本注入失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  generateBuildInfo,
  injectVersionToElectron,
  injectVersionToRenderer,
  updateLicenseService
};
