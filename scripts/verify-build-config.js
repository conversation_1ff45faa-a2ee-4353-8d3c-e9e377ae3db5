#!/usr/bin/env node

/**
 * 构建配置验证脚本
 * 验证所有构建命令是否正确配置
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPackageJson() {
  log('\n🔍 检查 package.json 构建配置...', 'bright');
  
  try {
    const packagePath = path.resolve(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const expectedScripts = [
      'build:windows-x64',
      'build:windows-arm64',
      'build:macos-x64',
      'build:macos-arm64',
      'build:linux-x64',
      'build:linux-arm64',
      'build:windows',
      'build:macos',
      'build:linux',
      'build:all'
    ];
    
    let allPresent = true;
    
    log('\n📋 检查构建脚本:', 'cyan');
    expectedScripts.forEach(script => {
      if (packageJson.scripts[script]) {
        log(`  ✅ ${script}`, 'green');
      } else {
        log(`  ❌ ${script} - 缺失`, 'red');
        allPresent = false;
      }
    });
    
    if (allPresent) {
      log('\n✅ 所有构建脚本配置正确', 'green');
    } else {
      log('\n❌ 部分构建脚本缺失', 'red');
      return false;
    }
    
    // 检查 electron-builder 配置
    log('\n📋 检查 electron-builder 配置:', 'cyan');
    const build = packageJson.build;
    
    if (build) {
      log('  ✅ build 配置存在', 'green');
      
      const platforms = ['mac', 'win', 'linux'];
      platforms.forEach(platform => {
        if (build[platform]) {
          log(`  ✅ ${platform} 配置存在`, 'green');
        } else {
          log(`  ⚠️  ${platform} 配置缺失`, 'yellow');
        }
      });
    } else {
      log('  ❌ build 配置缺失', 'red');
      return false;
    }
    
    return true;
  } catch (error) {
    log(`❌ 读取 package.json 失败: ${error.message}`, 'red');
    return false;
  }
}

function checkBuildFiles() {
  log('\n🔍 检查构建相关文件...', 'bright');
  
  const requiredFiles = [
    'BUILD.md',
    'scripts/build-platforms.js',
    'scripts/generate-build-version.js',
    'scripts/inject-version.js'
  ];
  
  let allPresent = true;
  
  requiredFiles.forEach(file => {
    const filePath = path.resolve(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      log(`  ✅ ${file}`, 'green');
    } else {
      log(`  ❌ ${file} - 缺失`, 'red');
      allPresent = false;
    }
  });
  
  return allPresent;
}

function checkBuildDirectories() {
  log('\n🔍 检查构建目录...', 'bright');
  
  const directories = [
    'build',
    'build/icons'
  ];
  
  let allPresent = true;
  
  directories.forEach(dir => {
    const dirPath = path.resolve(__dirname, '..', dir);
    if (fs.existsSync(dirPath)) {
      log(`  ✅ ${dir}/`, 'green');
    } else {
      log(`  ❌ ${dir}/ - 缺失`, 'red');
      allPresent = false;
    }
  });
  
  // 检查图标文件
  const iconFiles = [
    'build/icons/icon.ico',
    'build/icons/icon.icns',
    'build/icons/icon.png'
  ];
  
  iconFiles.forEach(icon => {
    const iconPath = path.resolve(__dirname, '..', icon);
    if (fs.existsSync(iconPath)) {
      log(`  ✅ ${icon}`, 'green');
    } else {
      log(`  ⚠️  ${icon} - 缺失`, 'yellow');
    }
  });
  
  return allPresent;
}

function showBuildCommands() {
  log('\n📋 可用的构建命令:', 'bright');
  
  const commands = [
    { cmd: 'yarn build:windows-x64', desc: 'Windows x64' },
    { cmd: 'yarn build:windows-arm64', desc: 'Windows ARM64' },
    { cmd: 'yarn build:macos-x64', desc: 'macOS Intel' },
    { cmd: 'yarn build:macos-arm64', desc: 'macOS Apple Silicon' },
    { cmd: 'yarn build:linux-x64', desc: 'Linux x64' },
    { cmd: 'yarn build:linux-arm64', desc: 'Linux ARM64' },
    { cmd: 'yarn build:windows', desc: '所有 Windows 版本' },
    { cmd: 'yarn build:macos', desc: '所有 macOS 版本' },
    { cmd: 'yarn build:linux', desc: '所有 Linux 版本' },
    { cmd: 'yarn build:all', desc: '所有平台' }
  ];
  
  commands.forEach(({ cmd, desc }) => {
    log(`  ${cmd.padEnd(25)} # ${desc}`, 'cyan');
  });
  
  log('\n🛠️  构建工具:', 'bright');
  log('  node scripts/build-platforms.js          # 交互式构建工具', 'cyan');
  log('  node scripts/build-platforms.js all      # 构建所有平台', 'cyan');
  log('  node scripts/build-platforms.js windows  # 构建 Windows 版本', 'cyan');
}

function main() {
  log('🔧 MEEA-VIOFO 构建配置验证', 'bright');
  
  const checks = [
    checkPackageJson(),
    checkBuildFiles(),
    checkBuildDirectories()
  ];
  
  const allPassed = checks.every(check => check);
  
  if (allPassed) {
    log('\n🎉 所有检查通过！构建配置正确', 'green');
    showBuildCommands();
  } else {
    log('\n❌ 部分检查失败，请修复后重试', 'red');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  checkPackageJson,
  checkBuildFiles,
  checkBuildDirectories
};
