#!/usr/bin/env node

/**
 * 构建版本生成脚本
 * 为每次构建生成唯一的版本号
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function generateBuildVersion() {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // 25
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 07
  const day = String(now.getDate()).padStart(2, '0'); // 18
  const hour = String(now.getHours()).padStart(2, '0'); // 18
  const minute = String(now.getMinutes()).padStart(2, '0'); // 10
  const second = String(now.getSeconds()).padStart(2, '0'); // 30
  
  return `${year}.${month}.${day}-${hour}${minute}${second}`;
}

function getGitShortHash() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    console.warn('⚠️ 无法获取Git哈希:', error.message);
    return 'unknown';
  }
}

function getPackageVersion() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  return packageJson.version;
}

function updatePackageVersion(newVersion) {
  console.log('🔄 更新package.json版本号...');

  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const oldVersion = packageJson.version;
  packageJson.version = newVersion;

  // 更新构建配置中的版本号
  if (!packageJson.build) {
    packageJson.build = {};
  }

  packageJson.build.buildVersion = newVersion;

  if (packageJson.build.win) {
    packageJson.build.win.fileVersion = newVersion;
    packageJson.build.win.productVersion = newVersion;
  }

  if (packageJson.build.mac) {
    packageJson.build.mac.bundleVersion = newVersion;
  }

  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

  console.log(`✅ 版本号已更新: ${oldVersion} → ${newVersion}`);

  return { oldVersion, newVersion };
}

function generateSimpleBuildInfo(baseVersion, buildVersion) {
  console.log('🔧 生成简化构建信息...');

  const buildTime = new Date();
  const gitHash = getGitShortHash();

  const buildInfo = {
    version: baseVersion,        // package.json中的基础版本
    buildVersion: buildVersion,  // 构建时生成的版本号
    buildTime: buildTime.toISOString(),
    buildDateTime: buildTime.toLocaleString('zh-CN'),
    gitHash: gitHash
  };

  // 写入构建信息文件
  const buildInfoPath = path.join(__dirname, '..', 'electron', 'build-info.json');
  fs.writeFileSync(buildInfoPath, JSON.stringify(buildInfo, null, 2));

  console.log('📊 构建信息:', {
    baseVersion: buildInfo.version,
    buildVersion: buildInfo.buildVersion,
    buildTime: buildInfo.buildDateTime,
    gitHash: buildInfo.gitHash
  });

  return buildInfo;
}

function parseVersion(versionArg) {
  // 支持多种版本格式
  if (versionArg.match(/^\d{2}\.\d{2}\.\d{2}-\d{6}$/)) {
    // 格式: 25.07.18-204354
    return versionArg;
  } else if (versionArg.match(/^\d+\.\d+\.\d+$/)) {
    // 格式: 1.0.0 -> 转换为时间戳格式
    const now = new Date();
    const timestamp = generateBuildVersion().split('-')[1];
    return `${versionArg.replace(/\./g, '.').slice(0, 8)}-${timestamp}`;
  } else {
    throw new Error(`无效的版本格式: ${versionArg}`);
  }
}

function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run') || args.includes('-d');
  const updatePackage = args.includes('--update-package') || args.includes('-u');
  const outputOnly = args.includes('--output-only') || args.includes('-o');

  // 查找版本参数
  let customVersion = null;
  const versionIndex = args.findIndex(arg => arg === '--version' || arg === '-v');
  if (versionIndex !== -1 && args[versionIndex + 1]) {
    try {
      customVersion = parseVersion(args[versionIndex + 1]);
    } catch (error) {
      console.error('❌', error.message);
      process.exit(1);
    }
  }

  try {
    // 确定版本号
    const buildVersion = customVersion || generateBuildVersion();
    const baseVersion = getPackageVersion();

    // 如果只是输出版本号，直接输出并退出
    if (outputOnly) {
      console.log(buildVersion);
      return buildVersion;
    }

    console.log('🚀 构建版本生成器');
    console.log('==================');

    console.log('📋 版本信息:');
    console.log('   基础版本 (package.json):', baseVersion);
    console.log('   构建版本:', buildVersion);

    if (dryRun) {
      console.log('🔍 预览模式 - 不会实际更新文件');
      console.log('📋 将要执行的操作:');
      if (updatePackage) {
        console.log('   1. 更新package.json版本号');
      }
      console.log('   2. 生成简化的构建信息文件');
      return buildVersion;
    }

    // 可选：更新package.json版本号
    if (updatePackage) {
      updatePackageVersion(buildVersion);
    }

    // 生成构建信息
    const buildInfo = generateSimpleBuildInfo(baseVersion, buildVersion);

    console.log('\n🎉 版本生成完成！');
    console.log('📊 最终版本信息:');
    console.log('   基础版本:', buildInfo.version);
    console.log('   构建版本:', buildInfo.buildVersion);
    console.log('   构建时间:', buildInfo.buildDateTime);
    console.log('   Git哈希:', buildInfo.gitHash);

    console.log('\n📋 后续步骤:');
    console.log('1. 运行构建命令 (yarn build)');
    console.log('2. 运行打包命令 (yarn dist:win/mac/linux)');

    return buildVersion;

  } catch (error) {
    console.error('❌ 版本生成失败:', error.message);
    process.exit(1);
  }
}

function showUsage() {
  console.log(`
📋 构建版本生成器使用说明:

用法: node scripts/generate-build-version.js [选项]

选项:
  --version, -v <版本号>     - 指定自定义版本号
  --update-package, -u       - 同时更新package.json中的版本号
  --dry-run, -d             - 预览模式，不实际更新文件
  --help, -h                - 显示帮助信息

版本号格式:
  25.07.18-204354          - 完整时间戳格式
  1.0.0                    - 语义化版本（会自动添加时间戳）

示例:
  node scripts/generate-build-version.js                    # 自动生成版本号
  node scripts/generate-build-version.js -v 25.07.18-210000 # 指定版本号
  node scripts/generate-build-version.js -v 1.2.3 -u        # 指定版本并更新package.json
  node scripts/generate-build-version.js -d                 # 预览模式
`);
}

if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
  } else {
    main();
  }
}

module.exports = {
  generateBuildVersion,
  updatePackageVersion,
  generateSimpleBuildInfo,
  getGitShortHash
};
