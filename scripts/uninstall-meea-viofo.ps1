# MEEA-VIOFO 完全卸载脚本 (PowerShell版本)
# 版本: 1.0
# 作者: PerccyKing

param(
    [switch]$Force,
    [switch]$Silent
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Write-ColorText {
    param(
        [string]$Text,
        [ConsoleColor]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Stop-MEEAProcesses {
    Write-ColorText "🔄 停止相关进程..." "Yellow"
    
    $processes = @("MEEA-VIOFO", "meea-viofo-all")
    $stopped = 0
    
    foreach ($processName in $processes) {
        $procs = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($procs) {
            $procs | Stop-Process -Force -ErrorAction SilentlyContinue
            $stopped += $procs.Count
            Write-ColorText "   已停止: $processName" "Gray"
        }
    }
    
    # 停止任何包含MEEA或viofo的进程
    Get-Process | Where-Object {
        $_.ProcessName -like "*MEEA*" -or $_.ProcessName -like "*viofo*"
    } | Stop-Process -Force -ErrorAction SilentlyContinue
    
    Write-ColorText "   ✅ 已停止 $stopped 个进程" "Green"
}

function Remove-ProgramFiles {
    Write-ColorText "🗂️ 删除程序文件..." "Yellow"
    
    $programPaths = @(
        "${env:ProgramFiles}\MEEA-VIOFO",
        "${env:ProgramFiles(x86)}\MEEA-VIOFO",
        "${env:ProgramFiles}\Video\MEEA-VIOFO",
        "${env:ProgramFiles(x86)}\Video\MEEA-VIOFO",
        "${env:ProgramFiles}\Video\MEEA-VIOFO-DEBUG",
        "${env:ProgramFiles(x86)}\Video\MEEA-VIOFO-DEBUG"
    )
    
    $removed = 0
    foreach ($path in $programPaths) {
        if (Test-Path $path) {
            try {
                Remove-Item $path -Recurse -Force -ErrorAction Stop
                Write-ColorText "   已删除: $path" "Gray"
                $removed++
            }
            catch {
                Write-ColorText "   ⚠️ 无法删除: $path - $($_.Exception.Message)" "Red"
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个程序目录" "Green"
}

function Remove-UserData {
    Write-ColorText "👤 删除用户数据..." "Yellow"
    
    $userDataPaths = @(
        "${env:APPDATA}\MEEA-VIOFO",
        "${env:APPDATA}\meea-viofo-all",
        "${env:APPDATA}\MEEA-VIOFO-DEBUG",
        "${env:LOCALAPPDATA}\MEEA-VIOFO",
        "${env:LOCALAPPDATA}\meea-viofo-all",
        "${env:LOCALAPPDATA}\MEEA-VIOFO-DEBUG"
    )
    
    $removed = 0
    foreach ($path in $userDataPaths) {
        if (Test-Path $path) {
            try {
                Remove-Item $path -Recurse -Force -ErrorAction Stop
                Write-ColorText "   已删除: $path" "Gray"
                $removed++
            }
            catch {
                Write-ColorText "   ⚠️ 无法删除: $path - $($_.Exception.Message)" "Red"
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个用户数据目录" "Green"
}

function Remove-RegistryEntries {
    Write-ColorText "📝 清理注册表..." "Yellow"
    
    $regPaths = @(
        "HKCU:\Software\MEEA-VIOFO",
        "HKCU:\Software\meea-viofo-all",
        "HKLM:\SOFTWARE\MEEA-VIOFO",
        "HKLM:\SOFTWARE\meea-viofo-all",
        "HKLM:\SOFTWARE\WOW6432Node\MEEA-VIOFO",
        "HKLM:\SOFTWARE\WOW6432Node\meea-viofo-all"
    )
    
    $removed = 0
    foreach ($regPath in $regPaths) {
        if (Test-Path $regPath) {
            try {
                Remove-Item $regPath -Recurse -Force -ErrorAction Stop
                Write-ColorText "   已删除: $regPath" "Gray"
                $removed++
            }
            catch {
                Write-ColorText "   ⚠️ 无法删除: $regPath - $($_.Exception.Message)" "Red"
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个注册表项" "Green"
}

function Remove-Shortcuts {
    Write-ColorText "🔗 清理快捷方式..." "Yellow"
    
    $shortcutPaths = @(
        "${env:APPDATA}\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO",
        "${env:ProgramData}\Microsoft\Windows\Start Menu\Programs\MEEA-VIOFO",
        "${env:USERPROFILE}\Desktop\MEEA-VIOFO.lnk",
        "${env:PUBLIC}\Desktop\MEEA-VIOFO.lnk"
    )
    
    $removed = 0
    foreach ($path in $shortcutPaths) {
        if (Test-Path $path) {
            try {
                Remove-Item $path -Recurse -Force -ErrorAction Stop
                Write-ColorText "   已删除: $path" "Gray"
                $removed++
            }
            catch {
                Write-ColorText "   ⚠️ 无法删除: $path - $($_.Exception.Message)" "Red"
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个快捷方式" "Green"
}

function Remove-TempFiles {
    Write-ColorText "🗑️ 清理临时文件..." "Yellow"
    
    $tempPaths = @(
        "${env:TEMP}\MEEA-VIOFO*",
        "${env:TEMP}\meea-viofo*",
        "${env:WINDIR}\Temp\MEEA-VIOFO*",
        "${env:WINDIR}\Temp\meea-viofo*"
    )
    
    $removed = 0
    foreach ($pattern in $tempPaths) {
        $files = Get-ChildItem -Path (Split-Path $pattern) -Filter (Split-Path $pattern -Leaf) -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            try {
                Remove-Item $file.FullName -Recurse -Force -ErrorAction Stop
                $removed++
            }
            catch {
                Write-ColorText "   ⚠️ 无法删除: $($file.FullName)" "Red"
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个临时文件" "Green"
}

function Remove-UninstallEntries {
    Write-ColorText "📋 清理卸载信息..." "Yellow"
    
    $uninstallPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
    )
    
    $removed = 0
    foreach ($basePath in $uninstallPaths) {
        if (Test-Path $basePath) {
            $subKeys = Get-ChildItem $basePath -ErrorAction SilentlyContinue
            foreach ($subKey in $subKeys) {
                $displayName = (Get-ItemProperty $subKey.PSPath -Name "DisplayName" -ErrorAction SilentlyContinue).DisplayName
                if ($displayName -and ($displayName -like "*MEEA-VIOFO*" -or $displayName -like "*meea-viofo*")) {
                    try {
                        Remove-Item $subKey.PSPath -Recurse -Force -ErrorAction Stop
                        Write-ColorText "   已删除卸载项: $displayName" "Gray"
                        $removed++
                    }
                    catch {
                        Write-ColorText "   ⚠️ 无法删除卸载项: $displayName" "Red"
                    }
                }
            }
        }
    }
    
    Write-ColorText "   ✅ 已删除 $removed 个卸载项" "Green"
}

# 主程序
Clear-Host
Write-ColorText "========================================" "Cyan"
Write-ColorText "    MEEA-VIOFO 完全卸载工具 (PowerShell)" "Cyan"
Write-ColorText "========================================" "Cyan"
Write-Host ""

# 检查管理员权限
if (-not (Test-Administrator)) {
    Write-ColorText "⚠️ 警告：需要管理员权限才能完全卸载" "Red"
    Write-ColorText "请右键点击PowerShell，选择'以管理员身份运行'，然后重新执行此脚本" "Yellow"
    Write-Host ""
    if (-not $Silent) {
        Read-Host "按回车键退出"
    }
    exit 1
}

Write-ColorText "✅ 管理员权限验证通过" "Green"
Write-Host ""

# 确认卸载
if (-not $Force -and -not $Silent) {
    Write-ColorText "🔍 此脚本将完全删除 MEEA-VIOFO 及其所有数据" "Yellow"
    Write-ColorText "   包括：程序文件、用户数据、配置文件、日志文件、注册表项" "Gray"
    Write-Host ""
    $confirm = Read-Host "确定要继续吗？(Y/N)"
    if ($confirm -ne "Y" -and $confirm -ne "y") {
        Write-ColorText "取消卸载操作" "Yellow"
        exit 0
    }
}

Write-Host ""
Write-ColorText "🚀 开始卸载 MEEA-VIOFO..." "Green"
Write-Host ""

# 执行卸载步骤
try {
    Stop-MEEAProcesses
    Remove-ProgramFiles
    Remove-UserData
    Remove-RegistryEntries
    Remove-Shortcuts
    Remove-TempFiles
    Remove-UninstallEntries
    
    Write-Host ""
    Write-ColorText "========================================" "Green"
    Write-ColorText "🎉 MEEA-VIOFO 卸载完成！" "Green"
    Write-ColorText "========================================" "Green"
    Write-Host ""
    
    Write-ColorText "✅ 已删除的内容：" "Green"
    Write-ColorText "   • 程序文件和目录" "Gray"
    Write-ColorText "   • 用户数据和配置文件" "Gray"
    Write-ColorText "   • 注册表项" "Gray"
    Write-ColorText "   • 开始菜单和桌面快捷方式" "Gray"
    Write-ColorText "   • 临时文件" "Gray"
    Write-ColorText "   • 卸载信息" "Gray"
    Write-Host ""
    
    Write-ColorText "💡 建议：" "Cyan"
    Write-ColorText "   • 重启计算机以确保完全清理" "Gray"
    Write-ColorText "   • 如需重新安装，请下载最新版本" "Gray"
    Write-Host ""
    
    Write-ColorText "📞 技术支持：<EMAIL>" "Cyan"
    Write-Host ""
    
    # 询问是否重启
    if (-not $Silent) {
        $restart = Read-Host "是否立即重启计算机？(Y/N)"
        if ($restart -eq "Y" -or $restart -eq "y") {
            Write-ColorText "正在重启计算机..." "Yellow"
            Restart-Computer -Force
        } else {
            Write-ColorText "请稍后手动重启计算机以完成清理" "Yellow"
        }
    }
}
catch {
    Write-ColorText "❌ 卸载过程中发生错误: $($_.Exception.Message)" "Red"
    Write-ColorText "请联系技术支持：<EMAIL>" "Yellow"
    exit 1
}

if (-not $Silent) {
    Write-Host ""
    Read-Host "按回车键退出"
}
