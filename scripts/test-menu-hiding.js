#!/usr/bin/env node

/**
 * 测试菜单栏隐藏功能
 * 验证所有平台上的 File 和 Edit 菜单是否被正确隐藏
 */

const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');

console.log('🧪 开始测试菜单栏隐藏功能...');
console.log(`📋 平台: ${process.platform}`);
console.log(`📋 架构: ${process.arch}`);

// 测试窗口创建和菜单隐藏
function testMenuHiding() {
  console.log('🔍 创建测试窗口...');
  
  const testWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: false,
    autoHideMenuBar: true,
    menuBarVisible: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  // 设置应用菜单为 null
  Menu.setApplicationMenu(null);
  
  // 确保菜单栏不可见
  testWindow.setMenuBarVisibility(false);

  console.log('✅ 测试窗口创建完成');
  console.log(`📋 菜单栏可见性: ${testWindow.isMenuBarVisible()}`);
  console.log(`📋 应用菜单: ${Menu.getApplicationMenu() ? '存在' : '已隐藏'}`);
  
  // 检查菜单栏状态
  const isMenuBarVisible = testWindow.isMenuBarVisible();
  const hasApplicationMenu = Menu.getApplicationMenu() !== null;
  
  if (!isMenuBarVisible && !hasApplicationMenu) {
    console.log('🎉 测试通过: 菜单栏已完全隐藏');
  } else {
    console.log('❌ 测试失败: 菜单栏仍然可见');
    console.log(`   - 菜单栏可见: ${isMenuBarVisible}`);
    console.log(`   - 应用菜单存在: ${hasApplicationMenu}`);
  }

  // 关闭测试窗口
  testWindow.close();
  
  // 退出应用
  setTimeout(() => {
    app.quit();
  }, 1000);
}

// 应用准备就绪后开始测试
app.whenReady().then(() => {
  testMenuHiding();
});

// 处理应用退出
app.on('window-all-closed', () => {
  console.log('🏁 测试完成，应用退出');
  app.quit();
});
