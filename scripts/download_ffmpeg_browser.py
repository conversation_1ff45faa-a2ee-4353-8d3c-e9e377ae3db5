#!/usr/bin/env python3
"""
FFmpeg 真实浏览器下载脚本
使用 Selenium 真正打开浏览器窗口，让用户看到下载过程
专门针对网络延迟和连接问题进行优化
"""

import os
import sys
import time
import random
import shutil
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"
DOWNLOADS_DIR = TEMP_DIR / "downloads"

def check_dependencies():
    """检查必要的依赖"""
    print("🔍 检查依赖...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        print("✅ Selenium 已安装")
        return True
    except ImportError as e:
        print(f"❌ Selenium 未安装: {e}")
        print("💡 安装方法:")
        print("   pip install selenium")
        print("   pip install webdriver-manager  # 可选，自动管理驱动")
        return False

def setup_browser():
    """设置浏览器，优化网络连接"""
    print("🚀 启动Chrome浏览器...")
    
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    
    chrome_options = Options()
    
    # 设置下载目录
    DOWNLOADS_DIR.mkdir(parents=True, exist_ok=True)
    prefs = {
        "download.default_directory": str(DOWNLOADS_DIR.absolute()),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
        "profile.default_content_settings.popups": 0
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    # 强制禁用代理和网络优化设置
    chrome_options.add_argument("--no-proxy-server")
    chrome_options.add_argument("--disable-proxy-certificate-handler")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    # 清除代理环境变量
    import os
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'http_proxy', 'https_proxy', 'ftp_proxy', 'no_proxy', 'all_proxy']
    for var in proxy_vars:
        if var in os.environ:
            print(f"🚫 清除代理环境变量: {var}")
            del os.environ[var]
    
    # 设置窗口大小（确保用户能看到）
    chrome_options.add_argument("--window-size=1200,800")
    chrome_options.add_argument("--window-position=100,100")
    
    # 不使用无头模式，让用户看到浏览器
    # chrome_options.add_argument("--headless")  # 注释掉这行
    
    try:
        # 尝试使用webdriver-manager
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("✅ 使用webdriver-manager启动Chrome")
        except ImportError:
            # 使用系统ChromeDriver
            driver = webdriver.Chrome(options=chrome_options)
            print("✅ 使用系统ChromeDriver启动Chrome")
        
        # 设置超时时间
        driver.set_page_load_timeout(60)
        driver.implicitly_wait(15)
        
        print(f"🌐 浏览器已启动，下载目录: {DOWNLOADS_DIR}")
        print("👀 您现在应该能看到Chrome浏览器窗口")
        
        return driver
        
    except Exception as e:
        print(f"❌ 启动浏览器失败: {e}")
        print("💡 请确保:")
        print("   1. 已安装Chrome浏览器")
        print("   2. 已安装ChromeDriver或webdriver-manager")
        print("   macOS安装方法:")
        print("     brew install --cask google-chrome")
        print("     brew install chromedriver")
        return None

def download_with_browser(driver, url, description):
    """使用浏览器下载文件"""
    print(f"\n📥 使用浏览器下载 {description}")
    print(f"🌐 URL: {url}")
    
    try:
        # 访问下载页面
        print("🔄 正在加载页面...")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        print("📄 页面已加载，请在浏览器中:")
        print("   1. 查看页面内容")
        print("   2. 找到FFmpeg下载链接")
        print("   3. 手动点击下载")
        print("   4. 等待下载完成")
        
        # 等待用户操作
        input("\n按回车键继续监控下载...")
        
        # 监控下载
        print("👀 监控下载进度...")
        max_wait_time = 600  # 10分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 检查下载文件
            zip_files = list(DOWNLOADS_DIR.glob("*.zip"))
            tar_files = list(DOWNLOADS_DIR.glob("*.tar.*"))
            downloading_files = list(DOWNLOADS_DIR.glob("*.crdownload"))
            
            if downloading_files:
                for dl_file in downloading_files:
                    try:
                        size_mb = dl_file.stat().st_size / 1024 / 1024
                        print(f"\r📊 正在下载: {dl_file.name} ({size_mb:.1f}MB)", end="", flush=True)
                    except:
                        print(f"\r📊 正在下载...", end="", flush=True)
            
            elif zip_files or tar_files:
                all_files = zip_files + tar_files
                largest_file = max(all_files, key=lambda f: f.stat().st_size)
                file_size_mb = largest_file.stat().st_size / 1024 / 1024
                elapsed_time = time.time() - start_time
                print(f"\n✅ 下载完成!")
                print(f"📁 文件: {largest_file.name}")
                print(f"📊 大小: {file_size_mb:.1f}MB")
                print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
                return largest_file
            
            time.sleep(2)
        
        print(f"\n⏰ 监控超时 (等待了{max_wait_time}秒)")
        return None
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 FFmpeg 真实浏览器下载工具")
    print("=" * 50)
    print("📝 这个工具会:")
    print("   1. 打开真实的Chrome浏览器窗口")
    print("   2. 访问FFmpeg下载页面")
    print("   3. 让您手动点击下载链接")
    print("   4. 监控下载进度")
    print()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    TEMP_DIR.mkdir(parents=True, exist_ok=True)
    DOWNLOADS_DIR.mkdir(parents=True, exist_ok=True)
    
    # 启动浏览器
    driver = setup_browser()
    if not driver:
        sys.exit(1)
    
    try:
        print("\n" + "=" * 50)
        print("🎯 开始下载流程")
        
        # 下载选项
        downloads = [
            {
                "name": "Windows FFmpeg",
                "url": "https://www.gyan.dev/ffmpeg/builds/",
                "description": "Windows版本 (gyan.dev)"
            },
            {
                "name": "Linux FFmpeg x64",
                "url": "https://johnvansickle.com/ffmpeg/releases/",
                "description": "Linux x64版本 (johnvansickle.com)"
            },
            {
                "name": "macOS FFmpeg",
                "url": "https://evermeet.cx/ffmpeg/",
                "description": "macOS版本 (evermeet.cx)"
            }
        ]
        
        print("\n📋 可用下载:")
        for i, download in enumerate(downloads, 1):
            print(f"   {i}. {download['name']} - {download['description']}")
        
        while True:
            try:
                choice = input(f"\n请选择要下载的版本 (1-{len(downloads)}, 0=退出): ").strip()
                
                if choice == "0":
                    break
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(downloads):
                    download_info = downloads[choice_idx]
                    result = download_with_browser(
                        driver, 
                        download_info["url"], 
                        download_info["name"]
                    )
                    
                    if result:
                        print(f"🎉 {download_info['name']} 下载成功!")
                        print(f"📁 文件位置: {result}")
                    else:
                        print(f"❌ {download_info['name']} 下载失败")
                else:
                    print("❌ 无效选择")
                    
            except ValueError:
                print("❌ 请输入数字")
            except KeyboardInterrupt:
                print("\n⚠️ 用户中断")
                break
    
    finally:
        print("\n🔚 关闭浏览器...")
        try:
            driver.quit()
            print("✅ 浏览器已关闭")
        except:
            print("⚠️ 浏览器关闭时出现问题")
    
    print("\n📊 下载完成!")
    print(f"📁 下载目录: {DOWNLOADS_DIR}")
    print("📝 下一步:")
    print("   1. 检查下载的文件")
    print("   2. 解压并复制到ffmpeg目录")
    print("   3. 运行: yarn test:ffmpeg")

if __name__ == "__main__":
    main()
