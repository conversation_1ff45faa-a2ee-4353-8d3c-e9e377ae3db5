#!/usr/bin/env python3
"""
FFmpeg 自动下载脚本
模拟真人浏览器行为下载所有平台的 FFmpeg 二进制文件
增强版：更真实的浏览器模拟，包括随机延迟、多User-Agent轮换等
"""

import os
import sys
import requests
import zipfile
import tarfile
import shutil
from pathlib import Path
from urllib.parse import urljoin, urlparse
import time
import re
import random
import json
from datetime import datetime

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
FFMPEG_DIR = PROJECT_ROOT / "ffmpeg"
TEMP_DIR = FFMPEG_DIR / ".temp"

# 多个真实的浏览器User-Agent，模拟不同用户
USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
]

# 真实的Accept-Language组合
ACCEPT_LANGUAGES = [
    'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'en-US,en;q=0.9',
    'zh-CN,zh;q=0.9,en;q=0.8',
    'en-GB,en-US;q=0.9,en;q=0.8',
    'ja,en-US;q=0.9,en;q=0.8',
    'de-DE,de;q=0.9,en;q=0.8'
]

def get_random_headers():
    """生成随机的浏览器请求头，模拟真人行为"""
    return {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': random.choice(ACCEPT_LANGUAGES),
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': f'"{random.choice(["macOS", "Windows", "Linux"])}"'
    }

def simulate_human_delay(min_seconds=1, max_seconds=3):
    """模拟人类操作的随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    print(f"⏳ 模拟人类操作延迟 {delay:.1f}s...")
    time.sleep(delay)

def create_session():
    """创建模拟真人浏览器的会话"""
    session = requests.Session()

    # 使用随机的浏览器头
    session.headers.update(get_random_headers())

    # 强制禁用所有代理
    session.proxies = {
        'http': '',
        'https': '',
        'ftp': '',
        'no_proxy': '*'
    }

    # 清除环境变量中的代理设置
    import os
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'http_proxy', 'https_proxy', 'ftp_proxy', 'no_proxy']
    for var in proxy_vars:
        if var in os.environ:
            print(f"🚫 清除代理环境变量: {var}")
            del os.environ[var]

    # 设置合理的超时时间
    session.timeout = 60

    # 启用cookie支持，模拟真实浏览器
    session.cookies.clear()

    # 设置连接池参数，模拟浏览器连接行为
    adapter = requests.adapters.HTTPAdapter(
        pool_connections=10,
        pool_maxsize=20,
        max_retries=3
    )
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    print(f"🌐 创建浏览器会话 (直连模式) - User-Agent: {session.headers.get('User-Agent', 'Unknown')[:50]}...")
    print("🚫 已禁用所有代理设置")

    return session

def visit_page_like_human(session, url, description="页面"):
    """像真人一样访问页面，包括延迟和错误处理"""
    print(f"🔍 正在访问 {description}: {url}")

    # 模拟人类打开页面前的思考时间
    simulate_human_delay(0.5, 2.0)

    try:
        # 第一次访问可能会有重定向，模拟真实浏览器行为
        response = session.get(url, allow_redirects=True, timeout=30)

        # 模拟页面加载时间
        simulate_human_delay(1.0, 2.5)

        response.raise_for_status()
        print(f"✅ 成功访问 {description} (状态码: {response.status_code})")

        # 如果有重定向，显示最终URL
        if response.url != url:
            print(f"🔄 重定向到: {response.url}")

        return response

    except requests.exceptions.RequestException as e:
        print(f"❌ 访问 {description} 失败: {e}")
        return None

def ensure_dir(path):
    """确保目录存在"""
    path.mkdir(parents=True, exist_ok=True)
    print(f"✅ 确保目录存在: {path}")

def download_file(session, url, output_path, description="文件"):
    """模拟真人下载文件，包括进度显示和人性化延迟"""
    print(f"📥 开始下载 {description}...")
    print(f"   URL: {url}")
    print(f"   保存到: {output_path}")

    # 模拟用户点击下载链接前的思考时间
    simulate_human_delay(1.0, 3.0)

    try:
        # 更新请求头，模拟下载请求
        download_headers = session.headers.copy()
        download_headers.update({
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': url.rsplit('/', 1)[0] + '/'  # 设置合理的Referer
        })

        response = session.get(url, stream=True, timeout=60, headers=download_headers)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        start_time = time.time()

        print(f"📦 文件大小: {total_size // 1024 // 1024 if total_size > 0 else '未知'}MB")

        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    # 模拟网络波动，偶尔暂停一下
                    if random.random() < 0.01:  # 1%的概率暂停
                        time.sleep(random.uniform(0.1, 0.5))

                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        elapsed_time = time.time() - start_time
                        speed = downloaded_size / elapsed_time / 1024 / 1024 if elapsed_time > 0 else 0
                        print(f"\r📊 进度: {progress:.1f}% ({downloaded_size // 1024 // 1024}MB/{total_size // 1024 // 1024}MB) 速度: {speed:.1f}MB/s", end='', flush=True)

        elapsed_time = time.time() - start_time
        print(f"\n✅ {description} 下载完成 (耗时: {elapsed_time:.1f}s)")

        # 模拟下载完成后的短暂停顿
        simulate_human_delay(0.5, 1.5)

        return True

    except Exception as e:
        print(f"\n❌ {description} 下载失败: {e}")
        # 模拟用户遇到错误时的停顿
        simulate_human_delay(2.0, 4.0)
        return False

def get_gyan_download_url(session):
    """像真人一样浏览 gyan.dev 获取最新 Windows FFmpeg 下载链接"""
    print("🔍 模拟真人浏览 gyan.dev 获取 Windows FFmpeg 下载链接...")

    try:
        # 像真人一样先访问主页
        main_page_response = visit_page_like_human(
            session,
            "https://www.gyan.dev/ffmpeg/builds/",
            "gyan.dev FFmpeg构建页面"
        )

        if not main_page_response:
            return None

        content = main_page_response.text

        # 模拟用户浏览页面寻找下载链接的时间
        print("👀 正在页面中寻找最新的下载链接...")
        simulate_human_delay(2.0, 4.0)

        # 查找 release essentials 下载链接（优先）
        patterns = [
            r'href="([^"]*ffmpeg-[^"]*-release-essentials\.zip)"',  # 首选：essentials版本
            r'href="([^"]*ffmpeg-[^"]*-release-full\.zip)"',       # 备选：完整版本
            r'href="([^"]*ffmpeg-[^"]*-release[^"]*\.zip)"'        # 最后：任何release版本
        ]

        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, content)
            if matches:
                download_url = matches[0]
                if not download_url.startswith('http'):
                    download_url = urljoin("https://www.gyan.dev/ffmpeg/builds/", download_url)

                version_type = ["essentials", "full", "release"][i]
                print(f"✅ 找到 Windows FFmpeg {version_type} 下载链接: {download_url}")

                # 模拟用户找到链接后的确认时间
                simulate_human_delay(1.0, 2.0)
                return download_url

        print("❌ 未找到 Windows FFmpeg 下载链接")
        print("🔍 页面内容预览:")
        # 显示页面的部分内容用于调试
        lines = content.split('\n')[:20]
        for line in lines:
            if 'ffmpeg' in line.lower():
                print(f"   {line.strip()[:100]}")

        return None

    except Exception as e:
        print(f"❌ 获取 Windows FFmpeg 下载链接失败: {e}")
        return None

def download_windows_ffmpeg(session):
    """下载 Windows FFmpeg"""
    print("\n🪟 下载 Windows FFmpeg...")
    
    # 获取最新下载链接
    download_url = get_gyan_download_url(session)
    if not download_url:
        print("❌ 无法获取 Windows FFmpeg 下载链接")
        return False
    
    # 下载文件
    temp_file = TEMP_DIR / "ffmpeg-windows.zip"
    if not download_file(session, download_url, temp_file, "Windows FFmpeg"):
        return False
    
    # 解压文件
    print("📦 解压 Windows FFmpeg...")
    try:
        with zipfile.ZipFile(temp_file, 'r') as zip_ref:
            zip_ref.extractall(TEMP_DIR)
        
        # 查找解压后的目录
        extracted_dirs = [d for d in TEMP_DIR.iterdir() if d.is_dir() and 'ffmpeg' in d.name.lower()]
        if not extracted_dirs:
            print("❌ 未找到解压后的 FFmpeg 目录")
            return False
        
        ffmpeg_dir = extracted_dirs[0]
        bin_dir = ffmpeg_dir / "bin"
        
        if not bin_dir.exists():
            print(f"❌ 未找到 bin 目录: {bin_dir}")
            return False
        
        # 复制文件到目标目录
        platforms = ["win-x64", "win-arm64"]
        for platform in platforms:
            platform_dir = FFMPEG_DIR / platform
            ensure_dir(platform_dir)
            
            # 复制 ffmpeg.exe 和 ffprobe.exe
            for exe_name in ["ffmpeg.exe", "ffprobe.exe"]:
                src_file = bin_dir / exe_name
                dst_file = platform_dir / exe_name
                
                if src_file.exists():
                    shutil.copy2(src_file, dst_file)
                    print(f"✅ 复制 {exe_name} 到 {platform}")
                else:
                    print(f"❌ 未找到 {exe_name}")
                    return False
        
        print("✅ Windows FFmpeg 设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 解压 Windows FFmpeg 失败: {e}")
        return False

def download_linux_ffmpeg(session, arch="amd64"):
    """模拟真人下载 Linux FFmpeg"""
    arch_name = "x64" if arch == "amd64" else "arm64"
    print(f"\n🐧 模拟真人下载 Linux {arch_name} FFmpeg...")

    # 构建下载 URL
    url = f"https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-{arch}-static.tar.xz"

    # 模拟用户先访问网站了解情况
    print(f"🌐 先访问 johnvansickle.com 了解 Linux FFmpeg...")
    main_response = visit_page_like_human(
        session,
        "https://johnvansickle.com/ffmpeg/",
        "johnvansickle FFmpeg页面"
    )

    if main_response:
        print("✅ 了解了网站信息，准备下载...")
    else:
        print("⚠️ 无法访问主页，直接尝试下载...")

    # 下载文件
    temp_file = TEMP_DIR / f"ffmpeg-linux-{arch}.tar.xz"
    if not download_file(session, url, temp_file, f"Linux {arch_name} FFmpeg"):
        return False
    
    # 解压文件
    print(f"📦 解压 Linux {arch_name} FFmpeg...")
    try:
        with tarfile.open(temp_file, 'r:xz') as tar_ref:
            tar_ref.extractall(TEMP_DIR)
        
        # 查找解压后的目录
        extracted_dirs = [d for d in TEMP_DIR.iterdir() 
                         if d.is_dir() and f'{arch}-static' in d.name]
        if not extracted_dirs:
            print(f"❌ 未找到解压后的 Linux {arch_name} 目录")
            return False
        
        ffmpeg_dir = extracted_dirs[0]
        
        # 复制文件到目标目录
        platform_dir = FFMPEG_DIR / f"linux-{arch_name}"
        ensure_dir(platform_dir)
        
        for exe_name in ["ffmpeg", "ffprobe"]:
            src_file = ffmpeg_dir / exe_name
            dst_file = platform_dir / exe_name
            
            if src_file.exists():
                shutil.copy2(src_file, dst_file)
                # 设置执行权限
                os.chmod(dst_file, 0o755)
                print(f"✅ 复制 {exe_name} 到 linux-{arch_name}")
            else:
                print(f"❌ 未找到 {exe_name}")
                return False
        
        print(f"✅ Linux {arch_name} FFmpeg 设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 解压 Linux {arch_name} FFmpeg 失败: {e}")
        return False

def setup_macos_ffmpeg():
    """设置 macOS FFmpeg（从系统复制）"""
    print("\n🍎 设置 macOS FFmpeg...")
    
    try:
        # 检查系统是否有 ffmpeg
        import subprocess
        
        ffmpeg_path = subprocess.check_output(['which', 'ffmpeg'], text=True).strip()
        ffprobe_path = subprocess.check_output(['which', 'ffprobe'], text=True).strip()
        
        print(f"✅ 找到系统 FFmpeg: {ffmpeg_path}")
        print(f"✅ 找到系统 FFprobe: {ffprobe_path}")
        
        # 复制到项目目录
        platforms = ["mac-x64", "mac-arm64"]
        for platform in platforms:
            platform_dir = FFMPEG_DIR / platform
            ensure_dir(platform_dir)
            
            # 复制文件
            shutil.copy2(ffmpeg_path, platform_dir / "ffmpeg")
            shutil.copy2(ffprobe_path, platform_dir / "ffprobe")
            
            # 设置执行权限
            os.chmod(platform_dir / "ffmpeg", 0o755)
            os.chmod(platform_dir / "ffprobe", 0o755)
            
            print(f"✅ 复制到 {platform}")
        
        print("✅ macOS FFmpeg 设置完成")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ 系统中未找到 FFmpeg，请先安装:")
        print("   brew install ffmpeg")
        return False
    except Exception as e:
        print(f"❌ 设置 macOS FFmpeg 失败: {e}")
        return False

def cleanup_temp():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    if TEMP_DIR.exists():
        shutil.rmtree(TEMP_DIR)
        print("✅ 临时文件清理完成")

def verify_setup():
    """验证设置结果"""
    print("\n🔍 验证设置结果...")
    
    platforms = {
        "win-x64": ["ffmpeg.exe", "ffprobe.exe"],
        "win-arm64": ["ffmpeg.exe", "ffprobe.exe"],
        "mac-x64": ["ffmpeg", "ffprobe"],
        "mac-arm64": ["ffmpeg", "ffprobe"],
        "linux-x64": ["ffmpeg", "ffprobe"],
        "linux-arm64": ["ffmpeg", "ffprobe"]
    }
    
    all_good = True
    
    for platform, files in platforms.items():
        print(f"\n📋 检查 {platform}:")
        platform_dir = FFMPEG_DIR / platform
        
        for file_name in files:
            file_path = platform_dir / file_name
            if file_path.exists():
                size_mb = file_path.stat().st_size / 1024 / 1024
                print(f"  ✅ {file_name} ({size_mb:.1f}MB)")
            else:
                print(f"  ❌ {file_name} (缺失)")
                all_good = False
    
    return all_good

def main():
    """主函数"""
    print("🚀 FFmpeg 自动下载脚本")
    print("=" * 50)
    
    # 创建目录结构
    ensure_dir(FFMPEG_DIR)
    ensure_dir(TEMP_DIR)
    
    platforms = ["win-x64", "win-arm64", "mac-x64", "mac-arm64", "linux-x64", "linux-arm64"]
    for platform in platforms:
        ensure_dir(FFMPEG_DIR / platform)
    
    # 创建会话
    session = create_session()
    
    success_count = 0
    total_count = 4  # Windows, macOS, Linux x64, Linux ARM64
    
    try:
        # 1. 设置 macOS FFmpeg
        if setup_macos_ffmpeg():
            success_count += 1
        
        # 2. 下载 Windows FFmpeg
        if download_windows_ffmpeg(session):
            success_count += 1
        
        # 3. 下载 Linux x64 FFmpeg
        if download_linux_ffmpeg(session, "amd64"):
            success_count += 1
        
        # 4. 下载 Linux ARM64 FFmpeg
        if download_linux_ffmpeg(session, "arm64"):
            success_count += 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
        cleanup_temp()
        sys.exit(1)
    
    # 清理临时文件
    cleanup_temp()
    
    # 验证结果
    all_good = verify_setup()
    
    print("\n" + "=" * 50)
    print(f"📊 下载结果: {success_count}/{total_count} 个平台成功")
    
    if all_good:
        print("🎉 所有平台的 FFmpeg 设置完成！")
        print("\n📝 下一步:")
        print("  1. 运行: yarn test:ffmpeg")
        print("  2. 运行: yarn version:generate")
        print("  3. 运行: yarn dist")
        print("  4. 提交到仓库: git add ffmpeg/ && git commit && git push")
    else:
        print("❌ 部分平台设置失败，请检查上述错误")
        sys.exit(1)

if __name__ == "__main__":
    main()
