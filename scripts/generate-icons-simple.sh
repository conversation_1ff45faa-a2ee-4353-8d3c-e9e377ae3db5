#!/bin/bash

# 简单的图标生成脚本
# 使用系统工具从 SVG 生成各种尺寸的图标

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎨 生成 MEEA-VIOFO 图标${NC}"

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SVG_PATH="$PROJECT_ROOT/assets/logo-new.svg"
BUILD_ICONS_DIR="$PROJECT_ROOT/build/icons"
ASSETS_DIR="$PROJECT_ROOT/assets"
PUBLIC_DIR="$PROJECT_ROOT/public"

# 检查 SVG 文件
if [ ! -f "$SVG_PATH" ]; then
    echo -e "${RED}❌ SVG 文件不存在: $SVG_PATH${NC}"
    exit 1
fi

# 创建目录
mkdir -p "$BUILD_ICONS_DIR"
mkdir -p "$PUBLIC_DIR"

# 检查转换工具
if command -v rsvg-convert >/dev/null 2>&1; then
    CONVERTER="rsvg-convert"
    echo -e "${GREEN}✅ 使用 rsvg-convert${NC}"
elif command -v inkscape >/dev/null 2>&1; then
    CONVERTER="inkscape"
    echo -e "${GREEN}✅ 使用 Inkscape${NC}"
else
    echo -e "${RED}❌ 需要安装 rsvg-convert 或 Inkscape${NC}"
    echo "macOS: brew install librsvg"
    echo "Ubuntu: sudo apt install librsvg2-bin"
    exit 1
fi

# SVG 转 PNG 函数
svg_to_png() {
    local size=$1
    local output_path=$2
    
    case $CONVERTER in
        "rsvg-convert")
            rsvg-convert -w $size -h $size "$SVG_PATH" -o "$output_path"
            ;;
        "inkscape")
            inkscape --export-width=$size --export-height=$size --export-filename="$output_path" "$SVG_PATH" >/dev/null 2>&1
            ;;
    esac
    
    if [ -f "$output_path" ]; then
        echo -e "${GREEN}✅ ${size}x${size}: $(basename "$output_path")${NC}"
    fi
}

echo -e "${BLUE}📐 生成图标文件...${NC}"

# 生成主要尺寸
svg_to_png 16 "$BUILD_ICONS_DIR/icon-16.png"
svg_to_png 32 "$BUILD_ICONS_DIR/icon-32.png"
svg_to_png 48 "$BUILD_ICONS_DIR/icon-48.png"
svg_to_png 64 "$BUILD_ICONS_DIR/icon-64.png"
svg_to_png 128 "$BUILD_ICONS_DIR/icon-128.png"
svg_to_png 256 "$BUILD_ICONS_DIR/icon-256.png"
svg_to_png 512 "$BUILD_ICONS_DIR/icon-512.png"
svg_to_png 1024 "$BUILD_ICONS_DIR/icon-1024.png"

# 生成标准文件
svg_to_png 256 "$BUILD_ICONS_DIR/icon.png"
svg_to_png 512 "$BUILD_ICONS_DIR/<EMAIL>"

# Web 图标
svg_to_png 192 "$PUBLIC_DIR/icon-192.png"
svg_to_png 512 "$PUBLIC_DIR/icon-512.png"

# Logo 文件
svg_to_png 512 "$ASSETS_DIR/logo.png"
svg_to_png 256 "$PUBLIC_DIR/logo.png"

# 复制 SVG
cp "$SVG_PATH" "$ASSETS_DIR/logo.svg"
cp "$SVG_PATH" "$PUBLIC_DIR/logo.svg"

# 生成 ICO (如果有 ImageMagick)
if command -v convert >/dev/null 2>&1; then
    echo -e "${BLUE}🪟 生成 ICO 文件...${NC}"
    convert \
        "$BUILD_ICONS_DIR/icon-16.png" \
        "$BUILD_ICONS_DIR/icon-32.png" \
        "$BUILD_ICONS_DIR/icon-48.png" \
        "$BUILD_ICONS_DIR/icon-64.png" \
        "$BUILD_ICONS_DIR/icon-128.png" \
        "$BUILD_ICONS_DIR/icon-256.png" \
        "$BUILD_ICONS_DIR/icon.ico"
    
    cp "$BUILD_ICONS_DIR/icon.ico" "$ASSETS_DIR/icon.ico"
    echo -e "${GREEN}✅ icon.ico${NC}"
fi

# 生成 ICNS (仅 macOS)
if [[ "$OSTYPE" == "darwin"* ]] && command -v iconutil >/dev/null 2>&1; then
    echo -e "${BLUE}🍎 生成 ICNS 文件...${NC}"
    ICONSET_DIR="$BUILD_ICONS_DIR/icon.iconset"
    mkdir -p "$ICONSET_DIR"
    
    # 复制文件到 iconset
    cp "$BUILD_ICONS_DIR/icon-16.png" "$ICONSET_DIR/icon_16x16.png"
    cp "$BUILD_ICONS_DIR/icon-32.png" "$ICONSET_DIR/<EMAIL>"
    cp "$BUILD_ICONS_DIR/icon-32.png" "$ICONSET_DIR/icon_32x32.png"
    cp "$BUILD_ICONS_DIR/icon-64.png" "$ICONSET_DIR/<EMAIL>"
    cp "$BUILD_ICONS_DIR/icon-128.png" "$ICONSET_DIR/icon_128x128.png"
    cp "$BUILD_ICONS_DIR/icon-256.png" "$ICONSET_DIR/<EMAIL>"
    cp "$BUILD_ICONS_DIR/icon-256.png" "$ICONSET_DIR/icon_256x256.png"
    cp "$BUILD_ICONS_DIR/icon-512.png" "$ICONSET_DIR/<EMAIL>"
    cp "$BUILD_ICONS_DIR/icon-512.png" "$ICONSET_DIR/icon_512x512.png"
    cp "$BUILD_ICONS_DIR/icon-1024.png" "$ICONSET_DIR/<EMAIL>"
    
    # 生成 ICNS
    iconutil -c icns "$ICONSET_DIR" -o "$BUILD_ICONS_DIR/icon.icns"
    rm -rf "$ICONSET_DIR"
    
    echo -e "${GREEN}✅ icon.icns${NC}"
fi

echo -e "${GREEN}🎉 图标生成完成!${NC}"
echo -e "📁 图标位置: $BUILD_ICONS_DIR"

# 显示生成的文件
echo -e "${BLUE}📋 生成的文件:${NC}"
ls -la "$BUILD_ICONS_DIR"/icon.* 2>/dev/null | awk '{print "   ✓ " $9 " (" $5 " bytes)"}'
