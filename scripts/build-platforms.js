#!/usr/bin/env node

/**
 * 多平台构建脚本
 * 支持单独构建不同平台和架构的版本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 支持的平台和架构
const platforms = {
  'windows-x64': {
    command: 'yarn build:windows-x64',
    description: 'Windows x64',
    icon: '🪟'
  },
  'windows-arm64': {
    command: 'yarn build:windows-arm64',
    description: 'Windows ARM64',
    icon: '🪟'
  },
  'windows-both': {
    command: 'yarn build:windows-x64-arm64',
    description: 'Windows (x64 + ARM64)',
    icon: '🪟'
  },
  'macos-x64': {
    command: 'yarn build:macos-x64',
    description: 'macOS Intel (x64)',
    icon: '🍎'
  },
  'macos-arm64': {
    command: 'yarn build:macos-arm64',
    description: 'macOS Apple Silicon (ARM64)',
    icon: '🍎'
  },
  'linux-x64': {
    command: 'yarn build:linux-x64',
    description: 'Linux x64',
    icon: '🐧'
  },
  'linux-arm64': {
    command: 'yarn build:linux-arm64',
    description: 'Linux ARM64',
    icon: '🐧'
  }
};

// 预定义的构建组合
const buildSets = {
  'all': Object.keys(platforms),
  'windows': ['windows-x64', 'windows-arm64'],
  'macos': ['macos-x64', 'macos-arm64'],
  'linux': ['linux-x64', 'linux-arm64'],
  'x64': ['windows-x64', 'macos-x64', 'linux-x64'],
  'arm64': ['windows-arm64', 'macos-arm64', 'linux-arm64']
};

function showHelp() {
  log('\n📦 MEEA-VIOFO 多平台构建工具\n', 'bright');
  
  log('用法:', 'yellow');
  log('  node scripts/build-platforms.js [平台/组合]\n');
  
  log('单独平台:', 'cyan');
  Object.entries(platforms).forEach(([key, config]) => {
    log(`  ${config.icon} ${key.padEnd(15)} - ${config.description}`);
  });
  
  log('\n构建组合:', 'cyan');
  Object.entries(buildSets).forEach(([key, platforms]) => {
    const icons = platforms.map(p => platforms[p]?.icon || '📦').join('');
    log(`  ${icons} ${key.padEnd(15)} - ${platforms.join(', ')}`);
  });
  
  log('\n示例:', 'green');
  log('  node scripts/build-platforms.js windows-x64     # 构建 Windows x64');
  log('  node scripts/build-platforms.js macos          # 构建所有 macOS 版本');
  log('  node scripts/build-platforms.js all            # 构建所有平台');
  log('');
}

function executeCommand(command, description) {
  log(`\n🚀 开始构建: ${description}`, 'bright');
  log(`📝 执行命令: ${command}`, 'blue');
  
  const startTime = Date.now();
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    log(`✅ 构建完成: ${description} (耗时: ${duration}s)`, 'green');
    return true;
  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    log(`❌ 构建失败: ${description} (耗时: ${duration}s)`, 'red');
    log(`错误信息: ${error.message}`, 'red');
    return false;
  }
}

function buildPlatforms(targetPlatforms) {
  const startTime = Date.now();
  const results = [];
  
  log(`\n🎯 开始构建 ${targetPlatforms.length} 个平台`, 'bright');
  
  for (const platformKey of targetPlatforms) {
    const platform = platforms[platformKey];
    if (!platform) {
      log(`⚠️  未知平台: ${platformKey}`, 'yellow');
      continue;
    }
    
    const success = executeCommand(platform.command, platform.description);
    results.push({
      platform: platformKey,
      description: platform.description,
      success
    });
  }
  
  // 显示构建总结
  const totalTime = ((Date.now() - startTime) / 1000 / 60).toFixed(1);
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;
  
  log('\n📊 构建总结', 'bright');
  log(`⏱️  总耗时: ${totalTime} 分钟`);
  log(`✅ 成功: ${successful} 个`);
  log(`❌ 失败: ${failed} 个`);
  
  if (successful > 0) {
    log('\n✅ 成功构建:', 'green');
    results.filter(r => r.success).forEach(r => {
      const icon = platforms[r.platform].icon;
      log(`  ${icon} ${r.description}`);
    });
  }
  
  if (failed > 0) {
    log('\n❌ 构建失败:', 'red');
    results.filter(r => !r.success).forEach(r => {
      const icon = platforms[r.platform].icon;
      log(`  ${icon} ${r.description}`);
    });
  }
  
  // 显示输出目录信息
  const distPath = path.resolve(__dirname, '..', 'dist');
  if (fs.existsSync(distPath)) {
    log('\n📁 构建产物位置:', 'cyan');
    log(`  ${distPath}`);
    
    try {
      const files = fs.readdirSync(distPath)
        .filter(file => file.includes('MEEA-VIOFO') && (file.endsWith('.dmg') || file.endsWith('.exe') || file.endsWith('.AppImage')))
        .sort();
      
      if (files.length > 0) {
        log('\n📦 生成的安装包:', 'cyan');
        files.forEach(file => {
          const stats = fs.statSync(path.join(distPath, file));
          const size = (stats.size / 1024 / 1024).toFixed(1);
          log(`  📄 ${file} (${size} MB)`);
        });
      }
    } catch (error) {
      log(`⚠️  无法读取构建产物: ${error.message}`, 'yellow');
    }
  }
  
  return failed === 0;
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  const target = args[0].toLowerCase();
  
  // 检查是否是预定义的构建组合
  if (buildSets[target]) {
    buildPlatforms(buildSets[target]);
    return;
  }
  
  // 检查是否是单个平台
  if (platforms[target]) {
    buildPlatforms([target]);
    return;
  }
  
  // 未知目标
  log(`❌ 未知的构建目标: ${target}`, 'red');
  log('使用 --help 查看可用选项', 'yellow');
  process.exit(1);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  platforms,
  buildSets,
  buildPlatforms
};
