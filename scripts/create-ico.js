#!/usr/bin/env node

/**
 * 创建 Windows ICO 文件的 Node.js 脚本
 * 使用纯 JavaScript 实现，无需外部依赖
 */

const fs = require('fs');
const path = require('path');

// ICO 文件头结构
function createIcoHeader(imageCount) {
  const buffer = Buffer.alloc(6);
  buffer.writeUInt16LE(0, 0);      // Reserved (must be 0)
  buffer.writeUInt16LE(1, 2);      // Type (1 = ICO)
  buffer.writeUInt16LE(imageCount, 4); // Number of images
  return buffer;
}

// ICO 目录条目结构
function createIcoDirectoryEntry(width, height, colorCount, reserved, planes, bitCount, bytesInRes, imageOffset) {
  const buffer = Buffer.alloc(16);
  buffer.writeUInt8(width === 256 ? 0 : width, 0);    // Width (0 = 256)
  buffer.writeUInt8(height === 256 ? 0 : height, 1);  // Height (0 = 256)
  buffer.writeUInt8(colorCount, 2);                   // Color count
  buffer.writeUInt8(reserved, 3);                     // Reserved
  buffer.writeUInt16LE(planes, 4);                    // Color planes
  buffer.writeUInt16LE(bitCount, 6);                  // Bits per pixel
  buffer.writeUInt32LE(bytesInRes, 8);                // Bytes in resource
  buffer.writeUInt32LE(imageOffset, 12);              // Image offset
  return buffer;
}

// 创建基于 MEEA VIOFO 设计的 BMP 数据（用于 ICO）
function createBmpData(width, height) {
  const bytesPerPixel = 4; // RGBA
  const rowSize = Math.ceil((width * bytesPerPixel) / 4) * 4; // 4字节对齐
  const pixelDataSize = rowSize * height;
  const maskSize = Math.ceil(width / 8) * height; // 1位掩码
  const totalSize = 40 + pixelDataSize + maskSize; // BMP头 + 像素数据 + 掩码

  const buffer = Buffer.alloc(totalSize);
  let offset = 0;

  // BMP 信息头 (40 字节)
  buffer.writeUInt32LE(40, offset); offset += 4;           // 头大小
  buffer.writeInt32LE(width, offset); offset += 4;         // 宽度
  buffer.writeInt32LE(height * 2, offset); offset += 4;    // 高度 * 2 (ICO格式要求)
  buffer.writeUInt16LE(1, offset); offset += 2;            // 平面数
  buffer.writeUInt16LE(32, offset); offset += 2;           // 位深度
  buffer.writeUInt32LE(0, offset); offset += 4;            // 压缩方式
  buffer.writeUInt32LE(pixelDataSize, offset); offset += 4; // 图像大小
  buffer.writeInt32LE(0, offset); offset += 4;             // X分辨率
  buffer.writeInt32LE(0, offset); offset += 4;             // Y分辨率
  buffer.writeUInt32LE(0, offset); offset += 4;            // 颜色数
  buffer.writeUInt32LE(0, offset); offset += 4;            // 重要颜色数

  // 像素数据 (从下到上)
  for (let y = height - 1; y >= 0; y--) {
    for (let x = 0; x < width; x++) {
      // 创建基于 MEEA VIOFO 设计的图标
      const centerX = width / 2;
      const centerY = height / 2;
      const outerRadius = Math.min(width, height) * 0.47;
      const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

      // 背景圆形
      if (distance <= outerRadius) {
        // 背景色 #2D3748
        buffer[offset++] = 0x48; // B
        buffer[offset++] = 0x37; // G
        buffer[offset++] = 0x2D; // R
        buffer[offset++] = 0xFF; // A
      } else {
        // 透明
        buffer[offset++] = 0; // B
        buffer[offset++] = 0; // G
        buffer[offset++] = 0; // R
        buffer[offset++] = 0; // A
        continue;
      }

      // 视频播放器矩形
      const rectLeft = centerX - width * 0.27;
      const rectRight = centerX + width * 0.27;
      const rectTop = centerY - height * 0.19;
      const rectBottom = centerY + height * 0.19;

      if (x >= rectLeft && x <= rectRight && y >= rectTop && y <= rectBottom) {
        // 检查是否在矩形边框上
        const borderWidth = Math.max(1, width * 0.008);
        if (x <= rectLeft + borderWidth || x >= rectRight - borderWidth ||
            y <= rectTop + borderWidth || y >= rectBottom - borderWidth) {
          // 边框色 #4FD1C7
          buffer[offset - 4] = 0xC7; // B
          buffer[offset - 3] = 0xD1; // G
          buffer[offset - 2] = 0x4F; // R
          buffer[offset - 1] = 0xFF; // A
        } else {
          // 内部色 #1A202C
          buffer[offset - 4] = 0x2C; // B
          buffer[offset - 3] = 0x20; // G
          buffer[offset - 2] = 0x1A; // R
          buffer[offset - 1] = 0xFF; // A
        }
      }

      // 播放按钮三角形
      const playLeft = centerX - width * 0.08;
      const playRight = centerX + width * 0.08;
      const playTop = centerY - height * 0.08;
      const playBottom = centerY + height * 0.08;

      if (x >= playLeft && x <= playRight && y >= playTop && y <= playBottom) {
        // 简化的三角形检测
        const relX = (x - playLeft) / (playRight - playLeft);
        const relY = (y - playTop) / (playBottom - playTop);

        if (relX <= 0.8 && relY >= 0.2 && relY <= 0.8 &&
            relX >= (relY - 0.2) * 1.33 && relX >= (0.8 - relY) * 1.33) {
          // 播放按钮色 #4FD1C7
          buffer[offset - 4] = 0xC7; // B
          buffer[offset - 3] = 0xD1; // G
          buffer[offset - 2] = 0x4F; // R
          buffer[offset - 1] = 0xFF; // A
        }
      }
    }
    // 行填充
    while (offset % 4 !== 0) {
      buffer[offset++] = 0;
    }
  }

  // AND 掩码 (全部设为0，使用Alpha通道)
  for (let i = 0; i < maskSize; i++) {
    buffer[offset++] = 0;
  }

  return buffer;
}

// 创建 ICO 文件
function createIcoFile(outputPath) {
  const sizes = [16, 32, 48, 256];
  const images = [];
  
  // 为每个尺寸创建图像数据
  for (const size of sizes) {
    const imageData = createBmpData(size, size);
    images.push({
      width: size,
      height: size,
      data: imageData
    });
  }
  
  // 计算偏移量
  let currentOffset = 6 + (images.length * 16); // 头部 + 目录条目
  
  // 创建文件头
  const header = createIcoHeader(images.length);
  
  // 创建目录条目
  const directoryEntries = [];
  for (const image of images) {
    const entry = createIcoDirectoryEntry(
      image.width,
      image.height,
      0,    // colorCount
      0,    // reserved
      1,    // planes
      32,   // bitCount
      image.data.length,
      currentOffset
    );
    directoryEntries.push(entry);
    currentOffset += image.data.length;
  }
  
  // 合并所有数据
  const buffers = [header, ...directoryEntries, ...images.map(img => img.data)];
  const finalBuffer = Buffer.concat(buffers);
  
  // 写入文件
  fs.writeFileSync(outputPath, finalBuffer);
  console.log(`✅ 创建 ICO 文件: ${outputPath} (${finalBuffer.length} 字节)`);
}

// 主函数
function main() {
  const iconsDir = path.join(__dirname, '..', 'build', 'icons');
  const icoPath = path.join(iconsDir, 'icon.ico');
  
  // 确保目录存在
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }
  
  try {
    createIcoFile(icoPath);
    console.log('🎉 ICO 文件创建成功！');
  } catch (error) {
    console.error('❌ 创建 ICO 文件失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { createIcoFile };
