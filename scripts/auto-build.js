#!/usr/bin/env node

/**
 * 自动构建脚本 - 自动更新版本号并构建
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function getCurrentDateTime() {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // 25
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 07
  const day = String(now.getDate()).padStart(2, '0'); // 18
  const hour = String(now.getHours()).padStart(2, '0'); // 18
  const minute = String(now.getMinutes()).padStart(2, '0'); // 10
  const second = String(now.getSeconds()).padStart(2, '0'); // 30

  return `${year}.${month}.${day}-${hour}${minute}${second}`;
}

function updateVersion() {
  console.log('🔄 自动更新版本号...');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const oldVersion = packageJson.version;
  const newVersion = getCurrentDateTime();
  
  packageJson.version = newVersion;
  
  // 更新构建配置
  if (!packageJson.build) {
    packageJson.build = {};
  }
  
  packageJson.build.buildVersion = newVersion;
  
  if (packageJson.build.win) {
    packageJson.build.win.fileVersion = newVersion;
    packageJson.build.win.productVersion = newVersion;
  }
  
  if (packageJson.build.mac) {
    packageJson.build.mac.bundleVersion = newVersion;
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  
  console.log(`✅ 版本号已更新: ${oldVersion} → ${newVersion}`);
  
  return { oldVersion, newVersion };
}

function runBuild(target = 'win') {
  console.log(`🔨 开始构建 ${target} 版本...`);
  
  try {
    const buildCommand = `yarn dist:${target}`;
    console.log(`📋 执行命令: ${buildCommand}`);
    
    execSync(buildCommand, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log(`✅ ${target} 构建完成！`);
    return true;
  } catch (error) {
    console.error(`❌ ${target} 构建失败:`, error.message);
    return false;
  }
}

function showBuildResults(version) {
  console.log('\n📊 构建结果检查...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distDir)) {
    console.error('❌ dist目录不存在');
    return;
  }
  
  const files = fs.readdirSync(distDir);
  const buildFiles = files.filter(file => 
    file.includes(version) && (file.endsWith('.exe') || file.endsWith('.zip') || file.endsWith('.dmg'))
  );
  
  if (buildFiles.length > 0) {
    console.log('✅ 找到构建文件:');
    buildFiles.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
      console.log(`   📦 ${file} (${sizeMB} MB)`);
    });
  } else {
    console.warn('⚠️ 未找到包含新版本号的构建文件');
    console.log('📁 dist目录内容:');
    files.forEach(file => {
      console.log(`   📄 ${file}`);
    });
  }
}

function main() {
  const args = process.argv.slice(2);
  const target = args[0] || 'win';
  const autoVersion = args.includes('--auto-version') || args.includes('-v');
  
  console.log('🚀 自动构建工具');
  console.log('==================');
  
  if (!['win', 'mac', 'linux', 'all'].includes(target)) {
    console.error('❌ 无效的构建目标:', target);
    console.log('💡 支持的目标: win, mac, linux, all');
    process.exit(1);
  }
  
  try {
    let version;
    
    if (autoVersion) {
      // 自动更新版本号
      const versionInfo = updateVersion();
      version = versionInfo.newVersion;
    } else {
      // 使用当前版本号
      const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
      version = packageJson.version;
      console.log('📋 使用当前版本号:', version);
    }
    
    // 执行构建
    const success = runBuild(target);
    
    if (success) {
      // 显示构建结果
      showBuildResults(version);
      
      console.log('\n🎉 构建完成！');
      console.log('📋 后续步骤:');
      console.log('1. 检查生成的安装包文件');
      console.log('2. 测试安装和运行');
      console.log('3. 验证版本号是否正确显示');
    } else {
      console.error('\n❌ 构建失败！');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 构建过程出错:', error.message);
    process.exit(1);
  }
}

function showUsage() {
  console.log(`
📋 自动构建工具使用说明:

用法: node scripts/auto-build.js [目标] [选项]

目标:
  win     - 构建Windows版本 (默认)
  mac     - 构建macOS版本
  linux   - 构建Linux版本
  all     - 构建所有平台版本

选项:
  --auto-version, -v  - 自动更新版本号

示例:
  node scripts/auto-build.js win -v          # 自动更新版本并构建Windows版本
  node scripts/auto-build.js mac             # 使用当前版本构建macOS版本
  node scripts/auto-build.js all --auto-version  # 自动更新版本并构建所有平台
`);
}

if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
  } else {
    main();
  }
}

module.exports = {
  updateVersion,
  runBuild,
  showBuildResults,
  getCurrentDateTime
};
