/**
 * 日志工具类
 * 用于控制开发环境下的日志输出
 */

// 日志级别
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

// 当前日志级别配置
const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production' 
  ? LogLevel.ERROR 
  : LogLevel.INFO; // 开发环境下只显示INFO及以上级别的日志

/**
 * 检查是否应该输出指定级别的日志
 */
function shouldLog(level: LogLevel): boolean {
  return level <= CURRENT_LOG_LEVEL;
}

/**
 * 日志工具类
 */
export class Logger {
  private context: string;

  constructor(context: string) {
    this.context = context;
  }

  /**
   * 错误日志 - 总是输出
   */
  error(...args: any[]): void {
    if (shouldLog(LogLevel.ERROR)) {
      console.error(`[${this.context}]`, ...args);
    }
  }

  /**
   * 警告日志
   */
  warn(...args: any[]): void {
    if (shouldLog(LogLevel.WARN)) {
      console.warn(`[${this.context}]`, ...args);
    }
  }

  /**
   * 信息日志
   */
  info(...args: any[]): void {
    if (shouldLog(LogLevel.INFO)) {
      console.log(`[${this.context}]`, ...args);
    }
  }

  /**
   * 调试日志 - 开发环境下默认不输出
   */
  debug(...args: any[]): void {
    if (shouldLog(LogLevel.DEBUG)) {
      console.log(`[DEBUG][${this.context}]`, ...args);
    }
  }

  /**
   * 详细日志 - 开发环境下默认不输出
   */
  verbose(...args: any[]): void {
    if (shouldLog(LogLevel.VERBOSE)) {
      console.log(`[VERBOSE][${this.context}]`, ...args);
    }
  }
}

/**
 * 创建日志实例
 */
export function createLogger(context: string): Logger {
  return new Logger(context);
}

/**
 * 默认日志实例
 */
export const logger = createLogger('App');
