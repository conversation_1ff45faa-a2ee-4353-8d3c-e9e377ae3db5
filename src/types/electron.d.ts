export type CameraAngle = 'F' | 'R' | 'I' | 'unknown';

export interface VideoFile {
  id: string;
  name: string;
  path: string;
  relativePath: string;
  size: number;
  timestamp: string;
  directory: string;
  gpsData?: GPSTrack;
}

export interface FileInfo {
  size: number;
  created: string;
  modified: string;
  accessed: string;
}

export interface GPSPoint {
  latitude: number;
  longitude: number;
  altitude?: number;
  speed?: number;
  heading?: number;
  timestamp?: string;
  accuracy?: number;
}

export interface GPSStatistics {
  totalDistance: number;      // 总距离（米）
  duration: number;           // 持续时间（秒）
  averageSpeed: number;       // 平均速度（km/h）
  maxSpeed: number;           // 最大速度（km/h）
  minSpeed: number;           // 最小速度（km/h）
  speedFromGPS: number;       // 从GPS记录计算的平均速度
  speedFromDistance: number;  // 从距离计算的平均速度
  validSpeedPoints: number;   // 有效速度点数量
  totalPoints: number;        // 总点数
}

export interface GPSTrack {
  points: GPSPoint[];
  totalDistance?: number;     // 总距离（米）
  duration?: number;          // 持续时间（秒）
  averageSpeed?: number;      // 平均速度（km/h）
  maxSpeed?: number;          // 最大速度（km/h）
  minSpeed?: number;          // 最小速度（km/h）
  startTime?: string;         // 开始时间
  endTime?: string;           // 结束时间
  pointCount?: number;        // GPS点数量
  statistics?: GPSStatistics; // 详细统计信息
}

export interface ThumbnailInfo {
  time: number;
  path: string;
  url: string;
  cacheKey?: string;
}

export interface ThumbnailOptions {
  interval?: number; // 间隔秒数，默认10
  maxThumbnails?: number; // 最大缩略图数量，默认100
  width?: number; // 宽度，默认160
  height?: number; // 高度，默认90
  quality?: number; // 质量，默认80
}

export interface CacheStats {
  fileCount: number;
  totalSize: number;
  cacheDir: string;
}

export interface LicenseResult {
  success: boolean;
  message?: string;
}

export interface LicenseValidationResult {
  success: boolean;
  isValid: boolean;
  message?: string;
}

export interface LicenseInfo {
  machineId: string;
  issuedAt: Date;
  verificationCode: string;
  version: string;
  issuer: string;
}

export interface LicenseInfoResult {
  success: boolean;
  info: LicenseInfo | null;
  message?: string;
}

export interface MachineIdResult {
  success: boolean;
  machineId: string | null;
  message?: string;
}

declare global {
  interface Window {
    electronAPI: {
      selectFolder: () => Promise<string | null>;
      scanVideoFiles: (folderPath: string) => Promise<VideoFile[]>;
      getFileInfo: (filePath: string) => Promise<FileInfo | null>;
      getVideoUrl: (filePath: string) => Promise<string | null>;
      extractGPSData: (filePath: string) => Promise<GPSTrack | null>;
      getVersion: () => Promise<string>;
      minimize: () => Promise<void>;
      maximize: () => Promise<void>;
      close: () => Promise<void>;
      toggleFullscreen: () => Promise<boolean>;
      isFullscreen: () => Promise<boolean>;
      showNotification: (title: string, body: string) => Promise<void>;

      // 视频缩略图相关API
      generateThumbnails: (videoPath: string, options?: ThumbnailOptions) => Promise<ThumbnailInfo[]>;
      generateThumbnail: (videoPath: string, timestamp: number, options?: ThumbnailOptions) => Promise<ThumbnailInfo | null>;
      getVideoDuration: (videoPath: string) => Promise<number>;
      preloadThumbnails: (videoPath: string, options?: ThumbnailOptions) => Promise<boolean>;
      getCacheStats: () => Promise<CacheStats>;
      cleanupCache: (maxAge?: number) => Promise<boolean>;

      // 配置管理相关API
      loadConfig: () => Promise<any>;
      saveConfig: (config: any) => Promise<boolean>;
      getLastVideoFolder: () => Promise<string | null>;
      setLastVideoFolder: (folderPath: string | null) => Promise<boolean>;
      getAmapApiKey: () => Promise<string | null>;
      setAmapApiKey: (apiKey: string | null) => Promise<boolean>;

      // 系统相关API
      openExternal: (url: string) => Promise<boolean>;

      // 许可证相关API
      license: {
        request: (verificationCode: string) => Promise<LicenseResult>;
        validate: () => Promise<LicenseValidationResult>;
        getInfo: () => Promise<LicenseInfoResult>;
        clear: () => Promise<LicenseResult>;
        getMachineId: () => Promise<MachineIdResult>;
      };
    };
  }
}
