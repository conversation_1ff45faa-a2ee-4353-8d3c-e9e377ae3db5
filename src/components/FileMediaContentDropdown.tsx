import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Button,
  Image,
  Badge,
  IconButton,
  Tooltip,
  Collapsible,
  Grid
} from '@chakra-ui/react';
import {
  FiChevronDown,
  FiChevronRight,
  FiImage,
  FiVideo,
  FiTrash2,
  FiExternalLink,
  FiPlay,
  FiFolder
} from 'react-icons/fi';
import { MediaScreenshot, MediaClip, MediaGroup } from '../hooks/useFileMediaContent';

interface FileMediaContentDropdownProps {
  videoPath: string;
  screenshots: MediaScreenshot[];
  clips: MediaClip[];
  groups: MediaGroup[];
  onRemoveScreenshot: (screenshotId: string) => void;
  onRemoveClip: (clipId: string) => void;
  onRemoveGroup: (groupId: string) => void;
  onOpenScreenshot: (imagePath: string) => void;
  onPlayClip: (clipPath: string) => void;
  onOpenInFolder: (filePath: string) => void;
}

const FileMediaContentDropdown: React.FC<FileMediaContentDropdownProps> = ({
  videoPath,
  screenshots,
  clips,
  groups,
  onRemoveScreenshot,
  onRemoveClip,
  onRemoveGroup,
  onOpenScreenshot,
  onPlayClip,
  onOpenInFolder
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}m${secs}s`;
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  // 获取未分组的截图和剪辑
  const ungroupedScreenshots = screenshots.filter(s => !s.groupId);
  const ungroupedClips = clips.filter(c => !c.groupId);

  const totalItems = screenshots.length + clips.length;

  if (totalItems === 0) {
    return null;
  }

  return (
    <Box>
      {/* 展开/收起按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        leftIcon={isExpanded ? <FiChevronDown /> : <FiChevronRight />}
        rightIcon={
          <HStack gap={1}>
            {screenshots.length > 0 && (
              <Badge colorPalette="blue" size="sm">
                <FiImage style={{ marginRight: '2px' }} />
                {screenshots.length}
              </Badge>
            )}
            {clips.length > 0 && (
              <Badge colorPalette="green" size="sm">
                <FiVideo style={{ marginRight: '2px' }} />
                {clips.length}
              </Badge>
            )}
          </HStack>
        }
        w="100%"
        justifyContent="flex-start"
        fontSize="xs"
        color="gray.600"
        _hover={{ bg: 'gray.100' }}
      >
        媒体内容
      </Button>

      {/* 内容区域 */}
      <Collapsible.Root open={isExpanded}>
        <Collapsible.Content>
          <Box ml={4} mt={2} p={3} bg="gray.50" borderRadius="md" border="1px solid" borderColor="gray.200">
            <VStack gap={3} align="stretch">
              {/* 分组内容 */}
              {groups.map(group => {
                const groupScreenshots = screenshots.filter(s => s.groupId === group.id);
                const groupClips = clips.filter(c => c.groupId === group.id);
                const isGroupExpanded = expandedGroups.has(group.id);

                return (
                  <Box key={group.id} border="1px solid" borderColor="gray.300" borderRadius="md" bg="white">
                    {/* 分组标题 */}
                    <HStack
                      p={2}
                      cursor="pointer"
                      onClick={() => toggleGroup(group.id)}
                      _hover={{ bg: 'gray.50' }}
                      borderRadius="md"
                    >
                      {isGroupExpanded ? <FiChevronDown /> : <FiChevronRight />}
                      <Text fontSize="sm" fontWeight="medium" flex={1}>
                        {group.name}
                      </Text>
                      <HStack gap={1}>
                        <Badge colorPalette={group.type === 'screenshot' ? 'blue' : 'green'} size="xs">
                          {group.type === 'screenshot' ? '截图' : '剪辑'}
                        </Badge>
                        <Badge colorPalette="gray" size="xs">
                          {groupScreenshots.length + groupClips.length}
                        </Badge>
                        <Tooltip label="删除分组">
                          <IconButton
                            size="xs"
                            variant="ghost"
                            colorPalette="red"
                            onClick={(e) => {
                              e.stopPropagation();
                              onRemoveGroup(group.id);
                            }}
                            aria-label="删除分组"
                          >
                            <FiTrash2 />
                          </IconButton>
                        </Tooltip>
                      </HStack>
                    </HStack>

                    {/* 分组内容 */}
                    {isGroupExpanded && (
                      <Box p={2} borderTop="1px solid" borderColor="gray.200">
                        <VStack gap={2} align="stretch">
                          {/* 分组截图 */}
                          {groupScreenshots.length > 0 && (
                            <Box>
                              <Text fontSize="xs" color="gray.600" mb={1}>截图</Text>
                              <Grid templateColumns="repeat(auto-fill, minmax(80px, 1fr))" gap={2}>
                                {groupScreenshots.map(screenshot => (
                                  <Box key={screenshot.id} position="relative">
                                    <Image
                                      src={`file://${screenshot.imagePath}`}
                                      alt={`截图 ${formatTime(screenshot.timestamp)}`}
                                      w="100%"
                                      h="45px"
                                      objectFit="cover"
                                      borderRadius="md"
                                      cursor="pointer"
                                      onClick={() => onOpenScreenshot(screenshot.imagePath)}
                                    />
                                    <Badge
                                      position="absolute"
                                      top={1}
                                      right={1}
                                      fontSize="xs"
                                      colorPalette="blackAlpha"
                                    >
                                      {formatTime(screenshot.timestamp)}
                                    </Badge>
                                  </Box>
                                ))}
                              </Grid>
                            </Box>
                          )}

                          {/* 分组剪辑 */}
                          {groupClips.length > 0 && (
                            <Box>
                              <Text fontSize="xs" color="gray.600" mb={1}>剪辑</Text>
                              <VStack gap={1} align="stretch">
                                {groupClips.map(clip => (
                                  <HStack key={clip.id} p={2} bg="gray.50" borderRadius="md" fontSize="xs">
                                    <FiVideo />
                                    <Text flex={1} noOfLines={1}>
                                      {formatTime(clip.startTime)}-{formatTime(clip.endTime)}
                                    </Text>
                                    <Badge size="xs">{formatDuration(clip.duration)}</Badge>
                                    <HStack gap={1}>
                                      <Tooltip label="播放">
                                        <IconButton
                                          size="xs"
                                          variant="ghost"
                                          onClick={() => onPlayClip(clip.outputPath)}
                                          aria-label="播放"
                                        >
                                          <FiPlay />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip label="在文件夹中显示">
                                        <IconButton
                                          size="xs"
                                          variant="ghost"
                                          onClick={() => onOpenInFolder(clip.outputPath)}
                                          aria-label="在文件夹中显示"
                                        >
                                          <FiFolder />
                                        </IconButton>
                                      </Tooltip>
                                    </HStack>
                                  </HStack>
                                ))}
                              </VStack>
                            </Box>
                          )}
                        </VStack>
                      </Box>
                    )}
                  </Box>
                );
              })}

              {/* 未分组的截图 */}
              {ungroupedScreenshots.length > 0 && (
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.700">
                    <FiImage style={{ display: 'inline', marginRight: '4px' }} />
                    截图 ({ungroupedScreenshots.length})
                  </Text>
                  <Grid templateColumns="repeat(auto-fill, minmax(100px, 1fr))" gap={2}>
                    {ungroupedScreenshots.map(screenshot => (
                      <Box key={screenshot.id} position="relative" bg="white" borderRadius="md" overflow="hidden">
                        <Image
                          src={`file://${screenshot.imagePath}`}
                          alt={`截图 ${formatTime(screenshot.timestamp)}`}
                          w="100%"
                          h="60px"
                          objectFit="cover"
                          cursor="pointer"
                          onClick={() => onOpenScreenshot(screenshot.imagePath)}
                        />
                        <Badge
                          position="absolute"
                          top={1}
                          right={1}
                          fontSize="xs"
                          colorPalette="blackAlpha"
                        >
                          {formatTime(screenshot.timestamp)}
                        </Badge>
                        <HStack position="absolute" bottom={1} left={1} right={1} justify="space-between">
                          <Tooltip label="在文件夹中显示">
                            <IconButton
                              size="xs"
                              variant="solid"
                              colorPalette="blue"
                              onClick={(e) => {
                                e.stopPropagation();
                                onOpenInFolder(screenshot.imagePath);
                              }}
                              aria-label="在文件夹中显示"
                            >
                              <FiExternalLink />
                            </IconButton>
                          </Tooltip>
                          <Tooltip label="删除">
                            <IconButton
                              size="xs"
                              variant="solid"
                              colorPalette="red"
                              onClick={(e) => {
                                e.stopPropagation();
                                onRemoveScreenshot(screenshot.id);
                              }}
                              aria-label="删除"
                            >
                              <FiTrash2 />
                            </IconButton>
                          </Tooltip>
                        </HStack>
                      </Box>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* 未分组的剪辑 */}
              {ungroupedClips.length > 0 && (
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.700">
                    <FiVideo style={{ display: 'inline', marginRight: '4px' }} />
                    剪辑 ({ungroupedClips.length})
                  </Text>
                  <VStack gap={2} align="stretch">
                    {ungroupedClips.map(clip => (
                      <HStack key={clip.id} p={3} bg="white" borderRadius="md" border="1px solid" borderColor="gray.200">
                        <FiVideo />
                        <VStack align="start" flex={1} gap={0}>
                          <Text fontSize="sm" fontWeight="medium">
                            {formatTime(clip.startTime)} - {formatTime(clip.endTime)}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {formatDate(clip.createdAt)} • {formatDuration(clip.duration)}
                          </Text>
                        </VStack>
                        <HStack gap={1}>
                          <Tooltip label="播放">
                            <IconButton
                              size="sm"
                              variant="ghost"
                              colorPalette="blue"
                              onClick={() => onPlayClip(clip.outputPath)}
                              aria-label="播放"
                            >
                              <FiPlay />
                            </IconButton>
                          </Tooltip>
                          <Tooltip label="在文件夹中显示">
                            <IconButton
                              size="sm"
                              variant="ghost"
                              colorPalette="gray"
                              onClick={() => onOpenInFolder(clip.outputPath)}
                              aria-label="在文件夹中显示"
                            >
                              <FiFolder />
                            </IconButton>
                          </Tooltip>
                          <Tooltip label="删除">
                            <IconButton
                              size="sm"
                              variant="ghost"
                              colorPalette="red"
                              onClick={() => onRemoveClip(clip.id)}
                              aria-label="删除"
                            >
                              <FiTrash2 />
                            </IconButton>
                          </Tooltip>
                        </HStack>
                      </HStack>
                    ))}
                  </VStack>
                </Box>
              )}
            </VStack>
          </Box>
        </Collapsible.Content>
      </Collapsible.Root>
    </Box>
  );
};

export default FileMediaContentDropdown;
