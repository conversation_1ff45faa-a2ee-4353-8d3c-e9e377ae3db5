import { Box, Text, Button, VStack } from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { GPSPoint, GPSTrack } from '../types/electron';
import Compass from './Compass';
// 图标选项 - 您可以选择喜欢的样式
import {
  FiSettings,    // 齿轮图标 (推荐)
} from 'react-icons/fi';

// 地图API类型定义
declare global {
  interface Window {
    AMap: any;
    AMapLoader: any;
  }
}

interface MapComponentProps {
  width?: string;
  height?: string;
  center?: [number, number]; // [经度, 纬度]
  zoom?: number;
  markers?: Array<{
    position: [number, number];
    title?: string;
  }>;
  gpsTrack?: GPSTrack; // GPS轨迹数据
  currentPosition?: GPSPoint; // 当前位置
  apiKey?: string | null; // 高德地图App Key
  onConfigureApiKey?: () => void; // 配置App Key的回调
}

const MapComponent = ({
  width = '100%',
  height = '400px',
  center = [116.397428, 39.90923], // 默认北京天安门
  zoom = 13,
  markers = [],
  gpsTrack,
  currentPosition,
  apiKey,
  onConfigureApiKey
}: MapComponentProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const polylineRef = useRef<any>(null);
  const currentMarkerRef = useRef<any>(null);
  const [mapKey, setMapKey] = useState(0); // 用于强制重新加载地图

  // 重置地图状态
  const resetMapState = () => {
    setIsMapLoaded(false);
    setLoadError(null);
    if (mapInstanceRef.current) {
      mapInstanceRef.current.destroy();
      mapInstanceRef.current = null;
    }
    if (polylineRef.current) {
      polylineRef.current = null;
    }
    if (currentMarkerRef.current) {
      currentMarkerRef.current = null;
    }
  };

  // 监听apiKey变化，重新加载地图
  useEffect(() => {
    resetMapState();
    setMapKey(prev => prev + 1); // 强制重新渲染
  }, [apiKey]);

  // 加载地图API
  useEffect(() => {
    const loadMapAPI = async () => {
      try {
        // 检查是否有App Key
        if (!apiKey) {
          setLoadError('地图功能已禁用');
          return;
        }

        // 检查是否已经加载了地图API
        if (window.AMap) {
          initMap();
          return;
        }

        // 动态加载地图API
        const script = document.createElement('script');
        script.src = `https://webapi.amap.com/maps?v=2.0&key=${apiKey}&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar`;
        script.async = true;

        script.onload = () => {
          if (window.AMap) {
            initMap();
          } else {
            setLoadError('地图API加载失败');
          }
        };

        script.onerror = () => {
          setLoadError('地图API加载失败');
        };

        document.head.appendChild(script);
      } catch (error) {
        console.error('加载地图API时出错:', error);
        setLoadError('地图API加载失败');
      }
    };

    loadMapAPI();

    // 清理函数
    return () => {
      if (polylineRef.current) {
        polylineRef.current = null;
      }
      if (currentMarkerRef.current) {
        currentMarkerRef.current = null;
      }
      if (mapInstanceRef.current) {
        mapInstanceRef.current.destroy();
        mapInstanceRef.current = null;
      }
    };
  }, [apiKey, mapKey]);

  // 初始化地图
  const initMap = () => {
    if (!mapRef.current || !window.AMap) return;

    try {
      const map = new window.AMap.Map(mapRef.current, {
        zoom: zoom,
        center: center,
        viewMode: '2D', // 使用2D视图，更稳定
        mapStyle: 'amap://styles/normal', // 地图样式
        features: ['bg', 'road', 'building'], // 简化显示要素
        resizeEnable: true, // 是否监控地图容器尺寸变化
        rotateEnable: false, // 禁用旋转
        pitchEnable: false, // 禁用倾斜
        zoomEnable: true, // 允许缩放
        dragEnable: true, // 允许拖拽
      });

      // 只添加比例尺控件，避免复杂控件导致的错误
      try {
        map.addControl(new window.AMap.Scale({
          position: 'LB' // 左下角
        }));
      } catch (controlError) {
        console.warn('添加地图控件失败:', controlError);
      }

      mapInstanceRef.current = map;
      setIsMapLoaded(true);
      setLoadError(null);


    } catch (error) {
      console.error('初始化地图时出错:', error);
      setLoadError('地图初始化失败');
    }
  };

  // 更新地图中心点
  useEffect(() => {
    if (mapInstanceRef.current && isMapLoaded) {
      mapInstanceRef.current.setCenter(center);
    }
  }, [center, isMapLoaded]);

  // 更新缩放级别
  useEffect(() => {
    if (mapInstanceRef.current && isMapLoaded) {
      mapInstanceRef.current.setZoom(zoom);
    }
  }, [zoom, isMapLoaded]);

  // 更新标记点
  useEffect(() => {
    if (!mapInstanceRef.current || !isMapLoaded || !window.AMap) return;

    // 清除现有标记（但保留轨迹和当前位置标记）
    mapInstanceRef.current.getAllOverlays('marker').forEach((marker: any) => {
      if (marker !== currentMarkerRef.current) {
        mapInstanceRef.current.remove(marker);
      }
    });

    // 添加新标记
    markers.forEach(marker => {
      const mapMarker = new window.AMap.Marker({
        position: marker.position,
        title: marker.title || '',
        anchor: 'bottom-center'
      });

      mapInstanceRef.current.add(mapMarker);
    });
  }, [markers, isMapLoaded]);

  // 更新GPS轨迹
  useEffect(() => {
    if (!mapInstanceRef.current || !isMapLoaded || !window.AMap || !gpsTrack) return;

    // 清除现有轨迹
    if (polylineRef.current) {
      mapInstanceRef.current.remove(polylineRef.current);
      polylineRef.current = null;
    }

    // 如果有GPS轨迹数据，绘制轨迹
    if (gpsTrack.points && gpsTrack.points.length > 1) {
      const path = gpsTrack.points.map(point => [point.longitude, point.latitude]);

      const polyline = new window.AMap.Polyline({
        path: path,
        strokeColor: '#FF0000', // 轨迹颜色 - 红色
        strokeWeight: 4, // 轨迹宽度
        strokeOpacity: 0.8, // 轨迹透明度
        strokeStyle: 'solid', // 轨迹样式
        lineJoin: 'round', // 线段连接样式
        lineCap: 'round' // 线段端点样式
      });

      mapInstanceRef.current.add(polyline);
      polylineRef.current = polyline;

      // 自动调整地图视野以显示完整轨迹
      try {
        mapInstanceRef.current.setFitView([polyline], false, [20, 20, 20, 20]);
      } catch (error) {
        console.warn('自动调整地图视野失败:', error);
      }


    }
  }, [gpsTrack, isMapLoaded]);

  // 更新当前位置标记
  useEffect(() => {
    if (!mapInstanceRef.current || !isMapLoaded || !window.AMap) return;

    // 清除现有当前位置标记
    if (currentMarkerRef.current) {
      mapInstanceRef.current.remove(currentMarkerRef.current);
      currentMarkerRef.current = null;
    }

    // 如果有当前位置，添加特殊标记
    if (currentPosition) {
      const currentMarker = new window.AMap.Marker({
        position: [currentPosition.longitude, currentPosition.latitude],
        title: '当前位置',
        anchor: 'center',
        icon: new window.AMap.Icon({
          size: new window.AMap.Size(20, 20),
          image: 'data:image/svg+xml;base64,' + btoa(`
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
              <circle cx="10" cy="10" r="8" fill="#FF4444" stroke="#FFFFFF" stroke-width="2"/>
              <circle cx="10" cy="10" r="3" fill="#FFFFFF"/>
            </svg>
          `),
          imageSize: new window.AMap.Size(20, 20)
        })
      });

      mapInstanceRef.current.add(currentMarker);
      currentMarkerRef.current = currentMarker;
    }
  }, [currentPosition, isMapLoaded]);

  return (
    <Box
      width={width}
      height={height}
      position="relative"
      borderRadius="md"
      overflow="hidden"
    >
      {loadError ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="100%"
          bg="gray.50"
          p={6}
        >
          {loadError === '地图功能已禁用' ? (
            <VStack gap={4} textAlign="center">
              <Text fontSize="4xl">🗺️</Text>
              <VStack gap={2}>
                <Text fontSize="lg" fontWeight="medium" color="gray.700">
                  地图功能已禁用
                </Text>
                <Text fontSize="sm" color="gray.600" maxWidth="400px">
                  配置高德地图App Key以启用地图和GPS轨迹功能
                </Text>
              </VStack>
              {onConfigureApiKey && (
                <Button
                  colorScheme="blue"
                  size="sm"
                  onClick={onConfigureApiKey}
                >
                  启用地图功能
                </Button>
              )}
            </VStack>
          ) : (
            <VStack gap={3} textAlign="center">
              <Text fontSize="3xl">⚠️</Text>
              <VStack gap={1}>
                <Text fontSize="md" fontWeight="medium" color="red.600">
                  地图加载失败
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {loadError}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  请检查网络连接和App Key配置
                </Text>
              </VStack>
              {onConfigureApiKey && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onConfigureApiKey}
                >
                  重新配置
                </Button>
              )}
            </VStack>
          )}
        </Box>
      ) : !isMapLoaded ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="100%"
          bg="gray.100"
          color="gray.600"
          fontSize="sm"
        >
          地图加载中...
        </Box>
      ) : null}
      
      <div
        ref={mapRef}
        style={{
          width: '100%',
          height: '100%',
          visibility: isMapLoaded ? 'visible' : 'hidden'
        }}
      />

      {/* 罗盘覆盖层 - 更柔和的设计 */}
      {isMapLoaded && currentPosition && currentPosition.heading !== undefined && (
        <Box
          position="absolute"
          top="16px"
          right="16px"
          zIndex={999}
          width="90px"
          height="90px"
          bg="rgba(255, 255, 255, 0.9)"
          borderRadius="12px"
          display="flex"
          alignItems="center"
          justifyContent="center"
          boxShadow="0 2px 8px rgba(0,0,0,0.1)"
          backdropFilter="blur(4px)"
          border="1px solid rgba(0,0,0,0.05)"
        >
          <Compass
            heading={currentPosition.heading}
            size={80}
          />
        </Box>
      )}

      {/* 地图设置按钮 - 当地图加载成功或禁用时显示 */}
      {(isMapLoaded || loadError === '地图功能已禁用') && onConfigureApiKey && (
        <Box
          position="absolute"
          top={4}
          left={4}
          zIndex={1000}
        >
          <Button
            size="sm"
            variant="outline"
            bg="white"
            shadow="md"
            onClick={onConfigureApiKey}
            title="重新配置App Key"
          >
            {/* 选项1: 齿轮图标 (推荐) */}
            <FiSettings size={16} />

            {/* 选项2: 工具图标 */}
            {/* <FiTool size={16} /> */}

            {/* 选项3: 钥匙图标 */}
            {/* <FiKey size={16} /> */}

            {/* 选项4: 地图图标 */}
            {/* <FiMap size={16} /> */}

            {/* 选项5: 滑块图标 */}
            {/* <FiSliders size={16} /> */}

            {/* 选项6: 原始emoji (当前使用) */}
            {/* ⚙️ */}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default MapComponent;
