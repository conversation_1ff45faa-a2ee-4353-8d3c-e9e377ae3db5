import React, { useState, useCallback } from 'react';
import {
  Dialog,
  Portal,
  Button,
  Text,
  VStack,
  HStack,
  Box,
  Image
} from '@chakra-ui/react';
import { FiSave, FiX } from 'react-icons/fi';

interface ScreenshotSaveDialogProps {
  open: boolean;
  onOpenChange: (details: { open: boolean }) => void;
  screenshotData?: {
    imagePath?: string;
    base64Data?: string;
    timestamp: number;
    videoName?: string;
    resolution?: { width: number; height: number };
    isMultiVideo?: boolean;
    compositeScreenshot?: {
      videoCount: number;
      timestamp: number;
      imagePath?: string;
      base64Data?: string;
      thumbnailPath?: string;
      resolution: { width: number; height: number };
    };
  };
  onSave: () => Promise<void>;
}

const ScreenshotSaveDialog: React.FC<ScreenshotSaveDialogProps> = ({
  open,
  onOpenChange,
  screenshotData,
  onSave
}) => {
  const [isSaving, setIsSaving] = useState(false);


  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 打开独立预览窗口
  const handleOpenPreview = useCallback(async () => {
    if (!screenshotData) return;

    try {
      // 优先使用base64数据，如果没有则使用文件路径
      const base64Data = screenshotData.isMultiVideo
        ? screenshotData.compositeScreenshot?.base64Data
        : screenshotData.base64Data;

      const imagePath = screenshotData.isMultiVideo
        ? screenshotData.compositeScreenshot?.imagePath
        : screenshotData.imagePath;

      const imageInfo = {
        width: screenshotData.isMultiVideo
          ? screenshotData.compositeScreenshot?.resolution?.width
          : screenshotData.resolution?.width,
        height: screenshotData.isMultiVideo
          ? screenshotData.compositeScreenshot?.resolution?.height
          : screenshotData.resolution?.height,
        videoName: screenshotData.isMultiVideo
          ? `多视频合成 (${screenshotData.compositeScreenshot?.videoCount}个视频)`
          : screenshotData.videoName,
        timestamp: screenshotData.timestamp
      };

      // 如果有base64数据，需要先保存为临时文件
      if (base64Data) {
        // 调用主进程创建临时文件并打开预览
        const result = await window.electronAPI.openImagePreviewFromBase64?.(base64Data, imageInfo);
        if (!result?.success) {
          console.error('打开base64图片预览失败:', result?.error);
        }
      } else if (imagePath) {
        await window.electronAPI.openImagePreview(imagePath, imageInfo);
      }
    } catch (error) {
      console.error('打开图片预览失败:', error);
    }
  }, [screenshotData]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave();
      // 注意：不在这里关闭对话框，让父组件处理
    } catch (error) {
      console.error('保存截图失败:', error);
    } finally {
      setIsSaving(false);
    }
  };



  if (!screenshotData) return null;

  return (
    <>
      <Dialog.Root open={open} onOpenChange={onOpenChange} size="lg">
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>保存截图</Dialog.Title>
            </Dialog.Header>
            <Dialog.CloseTrigger>
              <FiX />
            </Dialog.CloseTrigger>
            
            <Dialog.Body>
              <VStack gap={4} align="stretch">
                {/* 截图预览 */}
                <Box>
                  <Text fontWeight="medium" mb={2}>截图预览</Text>
                  <Box
                    borderRadius="md"
                    overflow="hidden"
                    border="1px solid"
                    borderColor="gray.200"
                    maxH="400px"
                    cursor="pointer"
                    onClick={handleOpenPreview}
                    _hover={{ borderColor: 'blue.300' }}
                    transition="border-color 0.2s"
                  >
                    <Image
                      src={screenshotData.isMultiVideo
                        ? (screenshotData.compositeScreenshot?.base64Data || `file://${screenshotData.compositeScreenshot?.imagePath}`)
                        : (screenshotData.base64Data || `file://${screenshotData.imagePath}`)
                      }
                      alt="截图预览"
                      maxW="100%"
                      maxH="400px"
                      objectFit="contain"
                    />
                  </Box>
                </Box>

                {/* 移除截图信息显示 - 根据用户要求不显示类型、时间点、分辨率 */}
              </VStack>
            </Dialog.Body>

            <Dialog.Footer>
              <HStack gap={2}>
                <Button
                  variant="outline"
                  onClick={() => onOpenChange({ open: false })}
                >
                  取消
                </Button>
                <Button
                  colorPalette="blue"
                  onClick={handleSave}
                  loading={isSaving}
                >
                  <FiSave style={{ marginRight: '8px' }} />
                  保存截图
                </Button>
              </HStack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>


    </>
  );
};

export default ScreenshotSaveDialog;
