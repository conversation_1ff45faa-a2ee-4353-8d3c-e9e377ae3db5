import { Box, Text } from '@chakra-ui/react';
import { ReactElement } from 'react';

interface MetricCardProps {
  icon?: ReactElement;
  label: string;
  value: string;
  unit?: string;
  color?: string;
  bgColor?: string;
  size?: 'sm' | 'md' | 'lg';
}

const MetricCard = ({ 
  icon, 
  label, 
  value, 
  unit, 
  color = 'gray.700', 
  bgColor = 'gray.50',
  size = 'md'
}: MetricCardProps) => {
  const sizeConfig = {
    sm: {
      padding: 2,
      labelSize: '10px',
      valueSize: '11px',
      iconSize: 3
    },
    md: {
      padding: 3,
      labelSize: 'xs',
      valueSize: 'sm',
      iconSize: 4
    },
    lg: {
      padding: 4,
      labelSize: 'sm',
      valueSize: 'md',
      iconSize: 5
    }
  };

  const config = sizeConfig[size];

  return (
    <Box
      p={config.padding}
      bg={bgColor}
      borderRadius="md"
      textAlign="center"
      position="relative"
      border="1px solid"
      borderColor="gray.200"
      _hover={{
        borderColor: 'gray.300',
        transform: 'translateY(-1px)',
        boxShadow: 'sm'
      }}
      transition="all 0.2s"
    >
      {icon && (
        <Box
          position="absolute"
          top={1}
          right={1}
          opacity={0.3}
          fontSize={config.iconSize}
        >
          {icon}
        </Box>
      )}
      
      <Text 
        fontSize={config.labelSize} 
        color="gray.500" 
        fontWeight="medium"
        mb={1}
        textTransform="uppercase"
        letterSpacing="wide"
      >
        {label}
      </Text>
      
      <Box display="flex" alignItems="baseline" justifyContent="center" gap={1}>
        <Text 
          fontSize={config.valueSize} 
          fontFamily="mono" 
          fontWeight="bold" 
          color={color}
          lineHeight="1"
        >
          {value}
        </Text>
        {unit && (
          <Text 
            fontSize="10px" 
            color="gray.500" 
            fontWeight="medium"
          >
            {unit}
          </Text>
        )}
      </Box>
    </Box>
  );
};

export default MetricCard;
