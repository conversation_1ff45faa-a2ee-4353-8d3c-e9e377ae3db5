import {
  Box,
  Text,
  Grid
} from '@chakra-ui/react';
import { useState, useRef, useEffect, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { VideoFile, CameraAngle } from '../types/electron';
import { useKeyboardFrameControl } from '../hooks/useKeyboardFrameControl';
import { toaster } from '../ui/toaster';
import VideoPlayerControls from './VideoPlayerControls';
import { createLogger } from '../utils/logger';

// 创建日志实例
const logger = createLogger('MultiVideoPlayer');

export interface MultiVideoPlayerRef {
  seek: (time: number) => void;
  volumeChange: (volume: number) => void;
  toggleMute: () => void;
  skipTime: (seconds: number) => void;
  play: () => void;
  pause: () => void;
  togglePlay: () => void;
  toggleFullscreen: () => void;
  // 缩放控制
  toggleZoom?: () => void;
  setScale?: (scale: number) => void;
  resetZoom?: () => void;
  // 视频显示控制
  toggleVideoVisibility?: (index: number) => void;
  returnToMultiVideoLayout: () => void;
}

interface MultiVideoPlayerProps {
  videoFiles: VideoFile[];
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onClose?: () => void;
  onSingleVideoPlay?: (file: VideoFile) => void; // 单独播放回调
  // 控制函数
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlay?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number) => void;
  onToggleMute?: () => void;
  onSkipTime?: (seconds: number) => void;
  // 状态
  isPlaying?: boolean;
  masterTime?: number;
  masterDuration?: number;
  volume?: number;
  isMuted?: boolean;
  // 单视频模式标识
  isSingleVideoMode?: boolean;
  // 返回多视频模式
  showReturnToMultiVideo?: boolean;
  onReturnToMultiVideo?: () => void;
  // 主视频独占模式状态变化回调
  onMainVideoFocusChange?: (isFocused: boolean) => void;
  // 显示视频数量变化回调
  onVisibleVideoCountChange?: (count: number) => void;
  // 缩放状态变化回调
  onZoomStateChange?: (scale: number, showZoomControls: boolean) => void;
  // 视频可见性状态和变化回调
  videoVisibility?: boolean[];
  onVideoVisibilityChange?: (videoVisibility: boolean[]) => void;
  // 剪辑进度相关
  isClipping?: boolean;
  clipProgress?: number;
}

interface VideoState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
}



// 视频布局信息
interface VideoLayout {
  gridTemplateColumns: string;
  gridTemplateRows: string;
  positions: Array<{
    gridColumn?: string;
    gridRow?: string;
    aspectRatio?: string;
  }>;
}



// 简化的视频布局状态（移除复杂的高度计算）

const MultiVideoPlayer = forwardRef<MultiVideoPlayerRef, MultiVideoPlayerProps>(({
  videoFiles,
  onTimeUpdate,

  onSingleVideoPlay,
  onPlay,
  onPause,
  onTogglePlay,
  onSeek,
  onVolumeChange,
  onToggleMute,
  onSkipTime,
  isPlaying: externalIsPlaying,
  masterTime: externalMasterTime,
  masterDuration: externalMasterDuration,
  volume: externalVolume,
  isMuted: externalIsMuted,
  isSingleVideoMode = false,
  showReturnToMultiVideo = false,
  onReturnToMultiVideo,
  onMainVideoFocusChange,
  onVisibleVideoCountChange,
  onZoomStateChange,
  videoVisibility: externalVideoVisibility,
  onVideoVisibilityChange,
  isClipping = false,
  clipProgress = 0
}: MultiVideoPlayerProps, ref) => {


  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const fullscreenContainerRef = useRef<HTMLDivElement>(null);

  const [videoUrls, setVideoUrls] = useState<(string | null | undefined)[]>([]);
  const [videoStates, setVideoStates] = useState<VideoState[]>([]);
  const [showControls, setShowControls] = useState(true);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 主视频独占模式状态
  const [isMainVideoFocused, setIsMainVideoFocused] = useState(false);
  const [focusedVideoIndex, setFocusedVideoIndex] = useState<number | null>(null);
  const [videoOrder, setVideoOrder] = useState<VideoFile[]>([]); // 视频显示顺序
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);

  // 视频显示/隐藏状态 - 使用外部传入的状态，如果没有则使用默认值
  const videoVisibility = externalVideoVisibility || new Array(videoFiles.length).fill(true);



  const [internalIsFullscreen, setInternalIsFullscreen] = useState(false); // 内部全屏状态

  // 视频缩放相关状态
  const [scale, setScale] = useState(1);
  const [translateX, setTranslateX] = useState(0);
  const [translateY, setTranslateY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [lastTranslate, setLastTranslate] = useState({ x: 0, y: 0 });
  const [showZoomControls, setShowZoomControls] = useState(false);

  // 剪辑模式相关状态
  const [isClipMode, setIsClipMode] = useState(false);
  const [clipStartTime, setClipStartTime] = useState(0);
  const [clipEndTime, setClipEndTime] = useState(0);

  // 使用内部全屏状态
  const isFullscreen = internalIsFullscreen;
  const masterTime = externalMasterTime ?? 0;
  const masterDuration = externalMasterDuration ?? 0;
  const isPlaying = externalIsPlaying ?? videoStates.some(state => state.isPlaying);
  const currentVolume = externalVolume ?? volume;
  const currentIsMuted = externalIsMuted ?? isMuted;



  // 计算可见视频数量，但不过滤视频文件数组
  const visibleVideoCount = useMemo(() =>
    videoVisibility.filter(Boolean).length,
    [videoVisibility]
  );

  // 通知父组件显示视频数量变化
  useEffect(() => {
    try {
      if (onVisibleVideoCountChange && typeof onVisibleVideoCountChange === 'function') {
        onVisibleVideoCountChange(visibleVideoCount);
      }
    } catch (error) {
      console.error('Error calling onVisibleVideoCountChange:', error);
    }
  }, [visibleVideoCount, onVisibleVideoCountChange]);

  // 获取摄像头信息
  const getCameraInfo = useCallback((filename: string): { angle: CameraAngle; label: string; bgColor: string } => {
    const lastChar = filename.charAt(filename.lastIndexOf('.') - 1).toUpperCase();
    switch (lastChar) {
      case 'F': return {
        angle: 'F',
        label: '前',
        bgColor: '#0d9488' // 深青色 - 前置摄像头
      };
      case 'R': return {
        angle: 'R',
        label: '后',
        bgColor: '#ea580c' // 橙色 - 后置摄像头
      };
      case 'I': return {
        angle: 'I',
        label: '内',
        bgColor: '#6b7280' // 灰色 - 内置摄像头
      };
      default: return {
        angle: 'unknown',
        label: '',
        bgColor: '#6b7280'
      };
    }
  }, []);

  // 简化的视频布局 - 让CSS自然适应视频高度
  const getVideoLayout = useCallback((count: number): VideoLayout => {
    if (count === 0) {
      return {
        gridTemplateColumns: '1fr',
        gridTemplateRows: 'auto',
        positions: []
      };
    } else if (count === 1) {
      return {
        gridTemplateColumns: '1fr',
        gridTemplateRows: 'auto',
        positions: [{}]
      };
    } else if (count === 2) {
      // 两个视频：次视频在左上角，主视频占据下方整行
      return {
        gridTemplateColumns: '1fr 1fr',
        gridTemplateRows: 'auto auto', // 让行高自动适应内容
        positions: [
          { gridColumn: '1 / -1', gridRow: '2' }, // 主视频 - 下方整行 (displayFiles[0])
          { gridColumn: '1', gridRow: '1' }  // 次视频 - 左上角 (displayFiles[1])
        ]
      };
    } else if (count === 3) {
      // 三视频布局：上面两个次视频，下面一个主视频
      return {
        gridTemplateColumns: '1fr 1fr',
        gridTemplateRows: 'auto auto', // 让行高自动适应内容
        positions: [
          { gridColumn: '1', gridRow: '1' }, // 后 - 左上
          { gridColumn: '2', gridRow: '1' }, // 内 - 右上
          { gridColumn: '1 / -1', gridRow: '2' } // 前 - 下面整行
        ]
      };
    } else {
      // 4个或更多视频，使用2x2网格
      return {
        gridTemplateColumns: '1fr 1fr',
        gridTemplateRows: '1fr 1fr',
        positions: [
          { gridColumn: '1', gridRow: '1' },
          { gridColumn: '2', gridRow: '1' },
          { gridColumn: '1', gridRow: '2' },
          { gridColumn: '2', gridRow: '2' }
        ]
      };
    }
  }, []);

  // 根据摄像头角度排序视频文件（基于所有视频，不过滤可见性）
  const sortedVideoFiles = useMemo(() => {
    const sorted = [...videoFiles];

    if (visibleVideoCount === 2) {
      // 两个可见视频：按照前(F)、后(R)、内(I)的优先顺序排列
      const angleOrder: Record<CameraAngle, number> = { 'F': 0, 'R': 1, 'I': 2, 'unknown': 3 };

      sorted.sort((a, b) => {
        const angleA = getCameraInfo(a.name).angle;
        const angleB = getCameraInfo(b.name).angle;
        return angleOrder[angleA] - angleOrder[angleB];
      });
    } else {
      // 三个或更多可见视频：按照后(R)、内(I)、前(F)的顺序排列（原有逻辑）
      const angleOrder: Record<CameraAngle, number> = { 'R': 0, 'I': 1, 'F': 2, 'unknown': 3 };

      sorted.sort((a, b) => {
        const angleA = getCameraInfo(a.name).angle;
        const angleB = getCameraInfo(b.name).angle;
        return angleOrder[angleA] - angleOrder[angleB];
      });
    }

    return sorted;
  }, [videoFiles, visibleVideoCount, getCameraInfo]);

  // 使用自定义顺序或默认排序
  const displayFiles = videoOrder.length > 0 ? videoOrder : sortedVideoFiles;

  // 移除复杂的高度计算，让CSS自然适应视频尺寸

  // 移除复杂的尺寸监听逻辑，让CSS自然处理响应式布局

  // 控制栏自动隐藏逻辑
  const showControlsTemporarily = useCallback(() => {
    setShowControls(true);

    // 清除之前的定时器
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    // 在全屏模式下，3秒后自动隐藏控制栏
    if (isFullscreen) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }
  }, [isFullscreen]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  const layout = getVideoLayout(visibleVideoCount);



  // 处理视频点击交互
  const handleVideoClick = useCallback((file: VideoFile, visibleIndex: number) => {
    // 如果处于缩放状态，禁用点击事件（只允许拖动）
    if (scale > 1) {
      return;
    }

    // 获取可见视频列表
    const visibleFiles = displayFiles.filter((f, i) => {
      const originalIndex = videoFiles.findIndex(vf => vf.id === f.id);
      return videoVisibility[originalIndex];
    });

    // 单视频模式下，点击视频切换播放/暂停状态
    if (visibleFiles.length === 1) {
      // 直接调用播放/暂停逻辑，避免函数定义顺序问题
      if (isPlaying) {
        onPause?.();
      } else {
        onPlay?.();
      }
      return;
    }

    // 如果已经在主视频独占模式，点击视频切换播放/暂停
    if (isMainVideoFocused) {
      if (isPlaying) {
        onPause?.();
      } else {
        onPlay?.();
      }
      return;
    }

    // 找到被点击视频在displayFiles中的实际索引
    const clickedFileDisplayIndex = displayFiles.findIndex(f => f.id === file.id);

    if (visibleFiles.length === 3) {
      if (visibleIndex === 0 || visibleIndex === 1) {
        // 点击左上或右上的次视频：与主视频交换位置
        const newOrder = [...displayFiles];
        // 找到主视频在displayFiles中的索引（可见视频中的第3个，即visibleIndex=2）
        const visibleMainFile = visibleFiles[2];
        const mainVideoDisplayIndex = displayFiles.findIndex(f => f.id === visibleMainFile.id);

        [newOrder[clickedFileDisplayIndex], newOrder[mainVideoDisplayIndex]] = [newOrder[mainVideoDisplayIndex], newOrder[clickedFileDisplayIndex]];
        setVideoOrder(newOrder);
      } else if (visibleIndex === 2) {
        // 点击主视频：进入主视频独占模式
        setIsMainVideoFocused(true);
        setFocusedVideoIndex(clickedFileDisplayIndex);
        onMainVideoFocusChange?.(true);
      }
    } else if (visibleFiles.length === 2) {
      if (visibleIndex === 1) {
        // 点击次视频（左上角）：与主视频交换位置
        const newOrder = [...displayFiles];
        // 找到主视频在displayFiles中的索引（可见视频中的第1个，即visibleIndex=0）
        const visibleMainFile = visibleFiles[0];
        const mainVideoDisplayIndex = displayFiles.findIndex(f => f.id === visibleMainFile.id);

        [newOrder[clickedFileDisplayIndex], newOrder[mainVideoDisplayIndex]] = [newOrder[mainVideoDisplayIndex], newOrder[clickedFileDisplayIndex]];
        setVideoOrder(newOrder);
      } else if (visibleIndex === 0) {
        // 点击主视频（下方）：进入主视频独占模式
        setIsMainVideoFocused(true);
        setFocusedVideoIndex(clickedFileDisplayIndex);
        onMainVideoFocusChange?.(true);
      }
    } else if (visibleFiles.length === 4) {
      // 4个视频时，点击任意视频都进入独占模式
      setIsMainVideoFocused(true);
      setFocusedVideoIndex(clickedFileDisplayIndex);
      onMainVideoFocusChange?.(true);
    }
  }, [displayFiles, videoFiles, videoVisibility, isPlaying, onPlay, onPause, isMainVideoFocused, scale, onMainVideoFocusChange]);

  // 返回多视频布局
  const handleReturnToMultiVideoLayout = useCallback(() => {
    setIsMainVideoFocused(false);
    setFocusedVideoIndex(null);
    onMainVideoFocusChange?.(false);

    // 延迟一点时间确保布局更新完成后再同步播放状态
    setTimeout(() => {
      // 根据当前的播放状态同步所有视频
      if (isPlaying) {
        // 同步播放所有视频
        videoRefs.current.forEach((ref) => {
          if (ref && !ref.paused) return;
          ref?.play().catch(console.error);
        });
      } else {
        // 同步暂停所有视频
        videoRefs.current.forEach((ref) => {
          if (ref && ref.paused) return;
          ref?.pause();
        });
      }
    }, 100);
  }, [onMainVideoFocusChange, isPlaying]);

  // 切换视频显示状态
  const toggleVideoVisibility = useCallback((originalIndex: number) => {
    const newVisibility = [...videoVisibility];
    newVisibility[originalIndex] = !newVisibility[originalIndex];

    // 如果隐藏的是当前聚焦的视频，退出主视频独占模式
    if (!newVisibility[originalIndex] && focusedVideoIndex === originalIndex) {
      setIsMainVideoFocused(false);
      setFocusedVideoIndex(null);
      onMainVideoFocusChange?.(false);
    }

    // 立即同步到父组件

    onVideoVisibilityChange?.(newVisibility);
  }, [videoVisibility, focusedVideoIndex, onMainVideoFocusChange, onVideoVisibilityChange]);

  // 移除不再需要的useEffect，因为现在直接使用外部传入的状态

  // 初始化视频顺序 - 只在真正的视频文件列表变化时触发
  useEffect(() => {
    if (videoFiles.length > 0) {
      // 计算初始排序（不依赖可见性）
      const initialSorted = [...videoFiles].sort((a, b) => {
        const aInfo = getCameraInfo(a.name);
        const bInfo = getCameraInfo(b.name);

        // 使用默认排序：后(R) > 内(I) > 前(F)
        const angleOrder: Record<CameraAngle, number> = { 'R': 0, 'I': 1, 'F': 2, 'unknown': 3 };
        const aOrder = angleOrder[aInfo.angle] || 999;
        const bOrder = angleOrder[bInfo.angle] || 999;

        return aOrder - bOrder;
      });

      // 当视频文件列表变化时，重置视频顺序
      setVideoOrder(initialSorted);

      // 通过回调函数通知父组件重置播放状态到开始位置
      onSeek?.(0);
      onPause?.();

      // 同步所有视频到开始位置
      setTimeout(() => {
        videoRefs.current.forEach((video) => {
          if (video) {
            video.currentTime = 0;
            video.pause();
          }
        });
      }, 100); // 延迟执行，确保视频元素已加载
    }
  }, [videoFiles, getCameraInfo, onSeek, onPause]); // 只依赖于原始视频文件列表，不依赖排序结果

  // 更新视频顺序 - 当可见性变化时只更新顺序，不重置播放状态
  useEffect(() => {
    if (videoFiles.length > 0) {
      setVideoOrder(sortedVideoFiles);
    }
  }, [sortedVideoFiles, videoFiles.length]);

  // 初始化视频状态 - 只在原始文件列表变化时重新初始化
  useEffect(() => {
    const initialStates = videoFiles.map(() => ({
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      isLoading: false,
      hasError: false,
      errorMessage: ''
    }));
    setVideoStates(initialStates);
    setVideoUrls(new Array(videoFiles.length).fill(undefined));

    // 重置剪辑状态
    setIsClipMode(false);
    setClipStartTime(0);
    setClipEndTime(0); // 将在视频加载完成后设置为视频总长度

  }, [videoFiles]);

  // 获取视频URL - 只在原始文件列表变化时重新加载
  useEffect(() => {
    const loadVideoUrls = async () => {
      const urls = await Promise.all(
        videoFiles.map(async (file) => {
          try {
            return await window.electronAPI.getVideoUrl(file.path);
          } catch (error) {
            console.error('获取视频URL失败:', error);
            return undefined;
          }
        })
      );
      setVideoUrls(urls);
    };

    if (videoFiles.length > 0) {
      loadVideoUrls();
    }
  }, [videoFiles]);

  // 同步播放所有视频
  const syncPlay = useCallback((notifyParent = true) => {
    videoRefs.current.forEach((video, index) => {
      if (video && !videoStates[index]?.hasError) {
        video.play().catch(console.error);
      }
    });
    setVideoStates(prev => prev.map(state => ({ ...state, isPlaying: true })));
    if (notifyParent) {
      // 使用 setTimeout 避免在渲染过程中调用父组件回调
      setTimeout(() => onPlay?.(), 0);
    }
  }, [videoStates, onPlay]);

  // 同步暂停所有视频
  const syncPause = useCallback((notifyParent = true) => {
    videoRefs.current.forEach((video) => {
      if (video) {
        video.pause();
      }
    });
    setVideoStates(prev => prev.map(state => ({ ...state, isPlaying: false })));
    if (notifyParent) {
      // 使用 setTimeout 避免在渲染过程中调用父组件回调
      setTimeout(() => onPause?.(), 0);
    }
  }, [onPause]);

  // 同步跳转所有视频
  const syncSeek = useCallback((time: number) => {
    videoRefs.current.forEach((video) => {
      if (video) {
        video.currentTime = time;
      }
    });
    // 使用 setTimeout 避免在渲染过程中调用父组件回调
    setTimeout(() => {
      onTimeUpdate?.(time, masterDuration);
    }, 0);
  }, [masterDuration, onTimeUpdate]);

  // 当视频加载完成后设置剪辑结束时间
  useEffect(() => {
    if (masterDuration > 0 && clipEndTime === 0) {
      setClipEndTime(masterDuration);
    }
  }, [masterDuration, clipEndTime]);

  // 使用syncSeek函数
  const handleSeek = useCallback((time: number) => {
    syncSeek(time);
    onSeek?.(time);
  }, [syncSeek, onSeek]);

  // 同步音量控制
  const handleVolumeChange = useCallback((newVolume: number) => {
    videoRefs.current.forEach((video) => {
      if (video) {
        video.volume = newVolume;
      }
    });
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
    onVolumeChange?.(newVolume);
  }, [onVolumeChange]);

  // 同步静音控制
  const handleToggleMute = useCallback(() => {
    const newMuted = !currentIsMuted;
    videoRefs.current.forEach((video) => {
      if (video) {
        video.muted = newMuted;
      }
    });
    setIsMuted(newMuted);
    onToggleMute?.();
  }, [currentIsMuted, onToggleMute]);

  // 快进快退
  const handleSkipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(masterDuration, masterTime + seconds));
    syncSeek(newTime);
    onSkipTime?.(seconds);
  }, [masterTime, masterDuration, syncSeek, onSkipTime]);

  // 切换播放/暂停
  const togglePlay = useCallback(() => {
    // 使用当前的isPlaying状态来决定下一步操作
    if (isPlaying) {
      syncPause();
    } else {
      syncPlay();
    }
    // 通知外部状态变化
    onTogglePlay?.();
  }, [isPlaying, syncPlay, syncPause, onTogglePlay]);

  // 剪辑模式相关处理函数
  const handleToggleClipMode = useCallback(() => {
    setIsClipMode(prev => {
      if (!prev) {
        // 进入剪辑模式时，设置默认的剪辑范围为整个视频
        setClipStartTime(0);
        setClipEndTime(masterDuration);
      }
      return !prev;
    });
  }, [masterDuration]);

  const handleClipTimeChange = useCallback((startTime: number, endTime: number) => {
    setClipStartTime(startTime);
    setClipEndTime(endTime);
  }, []);

  const handleSaveClip = useCallback(async () => {
    if (videoFiles.length === 0) return;

    // 只使用显示的视频进行剪辑
    const visibleVideos = videoFiles.filter((_, index) => videoVisibility[index]);

    if (visibleVideos.length === 0) {
      toaster.create({
        description: '没有显示的视频可以剪辑',
        status: 'error',
        duration: 2000,
      });
      return;
    }

    try {
      // 验证时间设置
      if (clipStartTime >= clipEndTime) {
        toaster.create({
          description: '结束时间必须大于开始时间',
          status: 'error',
          duration: 2000,
        });
        return;
      }

      if (clipStartTime < 0 || clipEndTime > masterDuration) {
        toaster.create({
          description: '时间范围超出视频长度',
          status: 'error',
          duration: 2000,
        });
        return;
      }

      // 使用多视频合成剪辑功能
      const result = await (window as any).electronAPI?.clipMultiVideo?.({
        videoFiles: visibleVideos, // 只传递显示的视频
        startTime: clipStartTime,
        endTime: clipEndTime,
        showSaveDialog: true // 显示保存对话框
      });

      if (result?.success) {


        // 显示成功提示
        toaster.create({
          description: `${visibleVideos.length}个视频剪辑已保存 (${(clipEndTime - clipStartTime).toFixed(1)}秒)`,
          status: 'success',
          duration: 3000,
        });

        // 退出剪辑模式
        setIsClipMode(false);
      } else if (result?.cancelled) {
        // 用户取消了保存操作

      } else {
        throw new Error(result?.error || '多视频剪辑失败');
      }
    } catch (error) {
      console.error('多视频剪辑失败:', error);
      // 显示错误提示
      toaster.create({
        description: '多视频剪辑失败',
        status: 'error',
        duration: 2000,
      });
    }
  }, [videoFiles, videoVisibility, clipStartTime, clipEndTime, masterDuration]);

  const handleCaptureFrame = useCallback(async () => {
    if (videoFiles.length === 0) return;

    // 只使用显示的视频进行截图
    const visibleVideos = videoFiles.filter((_, index) => videoVisibility[index]);

    if (visibleVideos.length === 0) {
      throw new Error('没有显示的视频可以截图');
    }

    try {


      // 获取base64预览数据
      const result = await (window as any).electronAPI?.captureMultiVideoFrame?.({
        videoFiles: visibleVideos, // 只传递显示的视频
        timestamp: masterTime,
        returnBase64: true // 返回base64数据而不是创建临时文件
      });

      if (result?.success) {

        // 返回合成截图数据
        return {
          isMultiVideo: true,
          compositeScreenshot: {
            videoCount: visibleVideos.length, // 使用显示的视频数量
            timestamp: masterTime,
            base64Data: result.base64,
            resolution: result.resolution
          },
          timestamp: masterTime
        };
      } else {
        const errorMsg = result?.error || '多视频截图预览失败，未知错误';
        console.error('多视频截图预览失败，错误信息:', errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('多视频截图异常:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        videoFiles: videoFiles.map(f => ({ name: f.name, path: f.path })),
        masterTime: masterTime
      });
      return null;
    }
  }, [videoFiles, videoVisibility, masterTime]);

  // 视频播放器区域全屏切换
  const toggleFullscreen = useCallback(async () => {
    try {
      const fullscreenContainer = fullscreenContainerRef.current;

      if (!fullscreenContainer) {
        return;
      }

      if (!isFullscreen) {
        if (fullscreenContainer.requestFullscreen) {
          await fullscreenContainer.requestFullscreen();
        } else if ((fullscreenContainer as any).webkitRequestFullscreen) {
          await (fullscreenContainer as any).webkitRequestFullscreen();
        } else if ((fullscreenContainer as any).mozRequestFullScreen) {
          await (fullscreenContainer as any).mozRequestFullScreen();
        } else if ((fullscreenContainer as any).msRequestFullscreen) {
          await (fullscreenContainer as any).msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
      }
    } catch (error) {
      console.error('Video player fullscreen toggle failed:', error);
    }
  }, [isFullscreen]);

  // 切换缩放控制显示
  const toggleZoom = useCallback(() => {
    setShowZoomControls(prev => !prev);
  }, []);

  // 重置缩放
  const resetZoom = useCallback(() => {
    setScale(1);
    setTranslateX(0);
    setTranslateY(0);
  }, []);

  // 缩放控制
  const handleScaleChange = useCallback((newScale: number) => {
    setScale(newScale);
    // 如果缩放回到1，重置平移
    if (newScale === 1) {
      setTranslateX(0);
      setTranslateY(0);
    }
  }, []);

  // 监听缩放状态变化并通知父组件
  useEffect(() => {
    onZoomStateChange?.(scale, showZoomControls);
  }, [scale, showZoomControls, onZoomStateChange]);

  // 暴露控制方法给外部
  useImperativeHandle(ref, () => ({
    seek: handleSeek,
    volumeChange: handleVolumeChange,
    toggleMute: handleToggleMute,
    skipTime: handleSkipTime,
    play: syncPlay,
    pause: syncPause,
    togglePlay,
    toggleFullscreen,
    // 缩放控制
    toggleZoom,
    setScale: handleScaleChange,
    resetZoom,
    // 视频显示控制
    toggleVideoVisibility,
    returnToMultiVideoLayout: handleReturnToMultiVideoLayout
  }), [handleSeek, handleVolumeChange, handleToggleMute, handleSkipTime, syncPlay, syncPause, togglePlay, toggleFullscreen, toggleZoom, handleScaleChange, resetZoom, toggleVideoVisibility, handleReturnToMultiVideoLayout]);





  // 鼠标拖拽处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (scale <= 1) return; // 只有放大时才允许拖拽

    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    setLastTranslate({ x: translateX, y: translateY });
    e.preventDefault();
  }, [scale, translateX, translateY]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || scale <= 1) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    setTranslateX(lastTranslate.x + deltaX);
    setTranslateY(lastTranslate.y + deltaY);
  }, [isDragging, dragStart, lastTranslate, scale]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 滚轮缩放
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!showZoomControls) return;

    // 使用stopPropagation而不是preventDefault来避免被动监听器问题
    e.stopPropagation();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newScale = Math.max(0.5, Math.min(5, scale + delta));
    handleScaleChange(newScale);
  }, [scale, showZoomControls, handleScaleChange]);

  // 处理视频事件
  const handleVideoEvent = useCallback((index: number, eventType: string, event?: any) => {
    const video = videoRefs.current[index];
    if (!video) return;

    // 先更新状态
    setVideoStates(prev => {
      const newStates = [...prev];

      switch (eventType) {
        case 'loadedmetadata':
          newStates[index] = {
            ...newStates[index],
            duration: video.duration,
            isLoading: false
          };


          break;
        case 'timeupdate':
          newStates[index] = {
            ...newStates[index],
            currentTime: video.currentTime
          };
          break;
        case 'play':
          newStates[index] = { ...newStates[index], isPlaying: true };
          break;
        case 'pause':
          newStates[index] = { ...newStates[index], isPlaying: false };
          break;
        case 'loadstart':
          newStates[index] = {
            ...newStates[index],
            isLoading: true,
            hasError: false,
            errorMessage: ''
          };
          break;
        case 'error':
          newStates[index] = {
            ...newStates[index],
            isLoading: false,
            hasError: true,
            errorMessage: `视频加载失败: ${event?.target?.error?.message || '未知错误'}`
          };
          break;
      }

      return newStates;
    });

    // 然后在下一个事件循环中调用回调函数，避免在渲染过程中触发父组件更新
    if (index === 0 && (eventType === 'loadedmetadata' || eventType === 'timeupdate')) {
      setTimeout(() => {
        onTimeUpdate?.(video.currentTime || 0, video.duration);
      }, 0);
    }
  }, [onTimeUpdate]);

  // 自动获得焦点以便接收键盘事件
  useEffect(() => {
    // 组件挂载后自动获得焦点
    if (containerRef.current) {
      containerRef.current.focus();
    }
  }, []);

  // 当视频文件变化时重新获得焦点
  useEffect(() => {
    if (videoFiles.length > 0 && containerRef.current) {
      // 延迟一点时间确保组件完全渲染
      const timer = setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.focus();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [videoFiles]);

  // 帧级精度控制已移到App.tsx中全局处理
  // useKeyboardFrameControl({
  //   onSeek: syncSeek,
  //   getCurrentTime: () => masterTime,
  //   getDuration: () => masterDuration,
  //   frameRate: 30,
  //   enabled: videoFiles.length > 0, // 只有当有视频时才启用
  //   containerRef // 传递容器引用，确保只在容器获得焦点时生效
  // });

  // 其他键盘快捷键处理（除了左右箭头）
  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    // 检查是否在输入框中，如果是则不处理快捷键
    const target = e.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return;
    }

    // 只有在多视频模式下才处理键盘事件，避免与默认播放器冲突
    if (!containerRef.current || videoFiles.length === 0) return;

    // 检查焦点是否在容器内（空格键已由全局处理，这里不需要特殊处理）
    const isFocusInContainer = containerRef.current?.contains(document.activeElement);

    if (!isFocusInContainer) {
      return;
    }

    switch (e.code) {
      // 移除空格键处理，由App.tsx全局处理
      // case 'Space':
      //   e.preventDefault();
      //   togglePlay();
      //   break;
      case 'ArrowUp':
        e.preventDefault();
        const newVolumeUp = Math.min(1, currentVolume + 0.1);
        handleVolumeChange(newVolumeUp);
        break;
      case 'ArrowDown':
        e.preventDefault();
        const newVolumeDown = Math.max(0, currentVolume - 0.1);
        handleVolumeChange(newVolumeDown);
        break;
      case 'KeyM':
        e.preventDefault();
        handleToggleMute();
        break;
      case 'KeyF':
        e.preventDefault();
        toggleFullscreen();
        break;
    }
  }, [togglePlay, currentVolume, handleVolumeChange, handleToggleMute, toggleFullscreen, videoFiles.length]);

  // 临时禁用MultiVideoPlayer的键盘监听器，避免与App.tsx中的全局监听器冲突
  // 这可能是导致粘贴功能不工作的原因之一
  /*
  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  */

  // 全屏状态监听
  useEffect(() => {
    const handleFullscreenChange = () => {
      // 检查当前全屏元素是否是我们的全屏容器
      const fullscreenElement = document.fullscreenElement;
      const isCurrentlyFullscreen = fullscreenElement === fullscreenContainerRef.current;
      setInternalIsFullscreen(isCurrentlyFullscreen);
    };

    // 监听 Web API 全屏变化
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 响应外部播放状态变化
  useEffect(() => {
    if (externalIsPlaying !== undefined && videoRefs.current.length > 0) {
      // 检查视频是否已经加载完成
      const hasLoadedVideos = videoRefs.current.some(video => video && video.readyState >= 3);

      if (hasLoadedVideos) {
        const currentlyPlaying = videoStates.some(state => state.isPlaying);
        if (externalIsPlaying && !currentlyPlaying) {
          syncPlay(false); // 不通知父组件，避免循环调用
        } else if (!externalIsPlaying && currentlyPlaying) {
          syncPause(false); // 不通知父组件，避免循环调用
        }
      } else {
        // 如果视频还没加载完成，延迟一点时间再尝试
        const timer = setTimeout(() => {
          const currentlyPlaying = videoStates.some(state => state.isPlaying);
          if (externalIsPlaying && !currentlyPlaying) {
            syncPlay(false);
          } else if (!externalIsPlaying && currentlyPlaying) {
            syncPause(false);
          }
        }, 300);

        return () => clearTimeout(timer);
      }
    }
  }, [externalIsPlaying, videoStates, syncPlay, syncPause]);

  // 响应外部时间变化
  useEffect(() => {
    if (externalMasterTime !== undefined && videoRefs.current.length > 0) {
      const currentVideoTime = videoRefs.current[0]?.currentTime || 0;
      // 只有当时间差异超过0.5秒时才进行跳转，避免频繁跳转
      if (Math.abs(currentVideoTime - externalMasterTime) > 0.5) {
        syncSeek(externalMasterTime);
      }
    }
  }, [externalMasterTime, syncSeek]);

  // 键盘快捷键现在由统一的VideoPlayerControls处理

  // 根据视频数量确定布局
  // const getLayout = () => {
  //   const count = videoFiles.length;
  //   if (count === 1) {
  //     return { gridTemplate: '1fr', areas: ['"video1"'] };
  //   } else if (count === 2) {
  //     return { gridTemplate: '1fr 1fr', areas: ['"video1 video2"'] };
  //   } else {
  //     return { gridTemplate: '1fr 1fr / 1fr', areas: ['"video1 video2"', '"video3 video3"'] };
  //   }
  // };

  // const layout = getLayout(); // 暂时注释掉未使用的变量

  // 移除复杂的高度计算，让容器自适应内容高度

  return (
    <Box
      ref={fullscreenContainerRef}
      display="flex"
      flexDirection="column"
      height={isFullscreen ? "100vh" : "100%"}
      width="100%"
      overflow="hidden"
    >
      {/* 视频容器 */}
      <Box
        ref={containerRef}
        position="relative"
        width="100%"
        flex={1}
        bg="black"
        borderRadius={isFullscreen ? "0" : "md"}
        overflow="hidden"
        tabIndex={0}
        outline="none"
        display="flex"
        flexDirection="column"
        onMouseEnter={() => showControlsTemporarily()}
        onMouseLeave={() => {
          if (!isFullscreen) {
            setShowControls(false);
          }
        }}
        onMouseMove={() => {
          if (isFullscreen) {
            showControlsTemporarily();
          }
        }}
        onClick={() => {
          // 点击容器时获得焦点，以便接收键盘事件
          if (containerRef.current) {
            containerRef.current.focus();
          }
        }}
      >



        {/* 视频网格 - 自适应高度 */}
        <Grid
          templateColumns={isMainVideoFocused ? "1fr" : layout.gridTemplateColumns}
          templateRows={isMainVideoFocused ? "1fr" : layout.gridTemplateRows}
          width="100%"
          flex={1}
          minHeight={
            displayFiles.length === 0 ? "400px" :
            displayFiles.length === 1 || isMainVideoFocused ? "300px" :
            "250px"
          } // 根据视频数量设置合适的最小高度
          gap={1}
          alignItems="stretch" // 让内容填充可用空间
          overflow="hidden"
          cursor={scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
          transform={`scale(${scale}) translate(${translateX / scale}px, ${translateY / scale}px)`}
          transformOrigin="center center"
          transition={isDragging ? 'none' : 'transform 0.1s ease-out'}
        >
        {displayFiles.length === 0 ? (
          // 空状态显示
          <Box
            gridColumn="1"
            gridRow="1"
            display="flex"
            alignItems="center"
            justifyContent="center"
            height="100%"
            color="gray.400"
            fontSize="lg"
          >
            请选择视频文件
          </Box>
        ) : (
          displayFiles.map((file, displayIndex) => {
          // 找到该文件在原始文件列表中的索引
          const originalIndex = videoFiles.findIndex(f => f.id === file.id);
          const videoState = videoStates[originalIndex];
          const videoUrl = videoUrls[originalIndex];
          const cameraInfo = getCameraInfo(file.name);

          // 检查视频是否可见
          const isVideoVisible = videoVisibility[originalIndex];

          // 在主视频独占模式下，隐藏其他视频但保留DOM元素以便同步播放状态
          const isHiddenByFocus = isMainVideoFocused && focusedVideoIndex !== null && displayIndex !== focusedVideoIndex;

          // 最终是否隐藏：被用户隐藏 或 被主视频独占模式隐藏
          const isHidden = !isVideoVisible || isHiddenByFocus;

          // 计算在可见视频中的位置索引
          const visibleVideoIndex = displayFiles
            .slice(0, displayIndex)
            .filter((f, i) => videoVisibility[videoFiles.findIndex(vf => vf.id === f.id)])
            .length;

          const position = isMainVideoFocused ? { gridColumn: '1', gridRow: '1' } : (layout.positions[visibleVideoIndex] || {});

          return (
            <Box
              key={file.id}
              bg="black"
              gridColumn={position.gridColumn}
              gridRow={position.gridRow}
              cursor={isHidden ? "default" : (scale > 1 ? (isDragging ? "grabbing" : "grab") : "pointer")}
              onClick={isHidden ? undefined : () => handleVideoClick(file, visibleVideoIndex)}
              _hover={{
                transform: isHidden || scale > 1 ? "none" : "scale(1.02)",
                transition: "transform 0.2s ease",
                zIndex: isHidden ? -1 : 10
              }}
              // 移除高度限制，让视频自然适应容器
              height="100%"
              width="100%"
              overflow="hidden"
              alignItems="center"
              justifyContent="center"
              // 合并显示和位置逻辑
              display={!isVideoVisible ? "none" : "flex"}
              position={isHiddenByFocus ? "absolute" : "relative"}
              visibility={isHiddenByFocus ? "hidden" : "visible"}
              transform={isHiddenByFocus ? "translateX(-9999px)" : "none"}
            >
              {/* 视频元素 */}
              {videoUrl && (
                <video
                  ref={(el) => {
                    videoRefs.current[originalIndex] = el;
                  }}
                  src={videoUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain' // 保持视频比例，完整显示视频内容
                  }}
                  onLoadedMetadata={(e) => handleVideoEvent(originalIndex, 'loadedmetadata', e)}
                  onTimeUpdate={(e) => handleVideoEvent(originalIndex, 'timeupdate', e)}
                  onPlay={(e) => handleVideoEvent(originalIndex, 'play', e)}
                  onPause={(e) => handleVideoEvent(originalIndex, 'pause', e)}
                  onLoadStart={(e) => handleVideoEvent(originalIndex, 'loadstart', e)}
                  onError={(e) => handleVideoEvent(originalIndex, 'error', e)}
                />
              )}

              {/* 视频标签 - 颜色圆点 */}
              <Box
                position="absolute"
                top={3}
                left={3}
                width="12px"
                height="12px"
                bg={cameraInfo.bgColor}
                borderRadius="50%"
                boxShadow="0 0 0 2px rgba(255,255,255,0.8)"
                zIndex={5}
              />



              {/* 交互提示 */}
              <Box
                position="absolute"
                top="50%"
                left="50%"
                transform="translate(-50%, -50%)"
                opacity={0}
                transition="opacity 0.2s ease"
                _hover={{ opacity: 1 }}
                bg="rgba(0,0,0,0.7)"
                color="white"
                px={3}
                py={2}
                borderRadius="md"
                fontSize="sm"
                fontWeight="medium"
                pointerEvents="none"
                textAlign="center"
              >
                {isSingleVideoMode || isMainVideoFocused ? (
                  "点击播放/暂停"
                ) : displayFiles.length === 3 ? (
                  displayIndex === 2 ? "点击独占播放" : "点击与主视频交换"
                ) : displayFiles.length === 2 ? (
                  displayIndex === 0 ? "点击独占播放" : "点击与主视频交换"
                ) : displayFiles.length === 4 ? (
                  "点击独占播放"
                ) : (
                  "点击播放/暂停"
                )}
              </Box>

              {/* 加载状态 */}
              {videoState?.isLoading && (
                <Box
                  position="absolute"
                  top="50%"
                  left="50%"
                  transform="translate(-50%, -50%)"
                  color="white"
                  textAlign="center"
                >
                  <Text fontSize="sm">加载中...</Text>
                </Box>
              )}

              {/* 错误状态 */}
              {videoState?.hasError && (
                <Box
                  position="absolute"
                  top="50%"
                  left="50%"
                  transform="translate(-50%, -50%)"
                  color="red.400"
                  textAlign="center"
                  p={2}
                >
                  <Text fontSize="sm">播放失败</Text>
                  <Text fontSize="xs" mt={1}>{videoState.errorMessage}</Text>
                </Box>
              )}
            </Box>
          );
        }))}
      </Grid>

      {/* 剪辑进度遮罩 */}
      {isClipping && (
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          bg="rgba(0, 0, 0, 0.8)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          zIndex={50}
          flexDirection="column"
        >
          <Box
            bg="white"
            borderRadius="lg"
            p={6}
            textAlign="center"
            minW="300px"
            boxShadow="xl"
          >
            <Text fontSize="lg" fontWeight="bold" mb={4} color="gray.800">
              正在剪辑视频...
            </Text>

            {/* 进度条 */}
            <Box
              w="100%"
              h="8px"
              bg="gray.200"
              borderRadius="full"
              overflow="hidden"
              mb={3}
            >
              <Box
                h="100%"
                bg="blue.500"
                borderRadius="full"
                width={`${clipProgress}%`}
                transition="width 0.3s ease"
              />
            </Box>

            {/* 进度百分比 */}
            <Text fontSize="sm" color="gray.600">
              {Math.round(clipProgress)}%
            </Text>
          </Box>
        </Box>
      )}

      {/* 全屏模式下的悬浮控制栏 */}
      {isFullscreen && (
        <Box
          position="absolute"
          bottom={4}
          left="50%"
          transform="translateX(-50%)"
          zIndex={30}
          opacity={showControls ? 1 : 0}
          transition="opacity 0.3s ease"
          pointerEvents={showControls ? "auto" : "none"}
        >
          <VideoPlayerControls
            isPlaying={isPlaying}
            currentTime={masterTime}
            duration={masterDuration}
            volume={currentVolume}
            isMuted={currentIsMuted}
            isFullscreen={isFullscreen}
            onTogglePlay={togglePlay}
            onSeek={syncSeek}
            onVolumeChange={handleVolumeChange}
            onToggleMute={handleToggleMute}
            onToggleFullscreen={toggleFullscreen}
            onSkipTime={handleSkipTime}
            onToggleZoom={toggleZoom}
            scale={scale}
            showZoomControls={showZoomControls}
            onScaleChange={handleScaleChange}
            onResetZoom={resetZoom}
            videoCount={visibleVideoCount} // 显示实际显示的视频数量
            isMultiVideo={true}
            multiVideoExtensions={{
              showProgressBar: true,
              showTimeDisplay: true,
              showVolumeSlider: false,
              customButtons: undefined
            }}
            showVolumeControl={true}
            showSkipButtons={true}
            showFullscreenButton={true}
            compact={true}
            multiVideoFiles={videoFiles}
            videoVisibility={videoVisibility}
            onToggleVideoVisibility={toggleVideoVisibility}
          />
        </Box>
      )}

      </Box>
    </Box>
  );
});

MultiVideoPlayer.displayName = 'MultiVideoPlayer';

export default MultiVideoPlayer;
