import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Box, Text, VStack, HStack } from '@chakra-ui/react';

export interface VideoClipTimelineProps {
  duration: number;
  currentTime: number;
  startTime: number;
  endTime: number;
  onStartTimeChange: (time: number) => void;
  onEndTimeChange: (time: number) => void;
  onSeek?: (time: number) => void;
}

const VideoClipTimeline: React.FC<VideoClipTimelineProps> = ({
  duration,
  currentTime,
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  onSeek
}) => {
  const timelineRef = useRef<HTMLDivElement>(null);
  const [isDraggingStart, setIsDraggingStart] = useState(false);
  const [isDraggingEnd, setIsDraggingEnd] = useState(false);
  const [isDraggingCurrent, setIsDraggingCurrent] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [hoverTime, setHoverTime] = useState(0);

  // 格式化时间显示
  const formatTime = useCallback((time: number): string => {
    const mins = Math.floor(time / 60);
    const secs = Math.floor(time % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // 计算时间对应的位置百分比
  const getPositionPercent = useCallback((time: number) => {
    return duration > 0 ? (time / duration) * 100 : 0;
  }, [duration]);

  // 根据鼠标位置计算时间
  const getTimeFromPosition = useCallback((clientX: number) => {
    if (!timelineRef.current) return 0;
    const rect = timelineRef.current.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    return percent * duration;
  }, [duration]);

  // 开始时间拖拽处理
  const handleStartDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingStart(true);
    // 开始拖动时立即跳转到开始时间位置
    onSeek?.(startTime);
  }, [startTime, onSeek]);

  // 结束时间拖拽处理
  const handleEndDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingEnd(true);
    // 开始拖动时立即跳转到结束时间位置
    onSeek?.(endTime);
  }, [endTime, onSeek]);

  // 当前时间拖拽处理
  const handleCurrentDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingCurrent(true);
  }, []);

  // 时间轴点击处理
  const handleTimelineClick = useCallback((e: React.MouseEvent) => {
    if (isDraggingStart || isDraggingEnd || isDraggingCurrent) return;
    const newTime = getTimeFromPosition(e.clientX);
    onSeek?.(newTime);
  }, [isDraggingStart, isDraggingEnd, isDraggingCurrent, getTimeFromPosition, onSeek]);

  // 鼠标移动处理
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const newTime = getTimeFromPosition(e.clientX);
    setHoverTime(newTime);
  }, [getTimeFromPosition]);

  // 全局鼠标移动处理
  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    const newTime = getTimeFromPosition(e.clientX);

    if (isDraggingStart) {
      const clampedTime = Math.max(0, Math.min(endTime - 0.1, newTime));
      onStartTimeChange(clampedTime);
      // 拖动开始时间时，同步视频播放进度
      onSeek?.(clampedTime);
    } else if (isDraggingEnd) {
      const clampedTime = Math.max(startTime + 0.1, Math.min(duration, newTime));
      onEndTimeChange(clampedTime);
      // 拖动结束时间时，同步视频播放进度
      onSeek?.(clampedTime);
    } else if (isDraggingCurrent) {
      const clampedTime = Math.max(0, Math.min(duration, newTime));
      onSeek?.(clampedTime);
    }
  }, [isDraggingStart, isDraggingEnd, isDraggingCurrent, getTimeFromPosition, startTime, endTime, duration, onStartTimeChange, onEndTimeChange, onSeek]);

  // 全局鼠标释放处理
  const handleGlobalMouseUp = useCallback(() => {
    setIsDraggingStart(false);
    setIsDraggingEnd(false);
    setIsDraggingCurrent(false);
  }, []);

  // 添加/移除全局事件监听器
  useEffect(() => {
    if (isDraggingStart || isDraggingEnd || isDraggingCurrent) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDraggingStart, isDraggingEnd, isDraggingCurrent, handleGlobalMouseMove, handleGlobalMouseUp]);

  return (
    <VStack gap={3} align="stretch">
      {/* 时间显示 */}
      <HStack justify="space-between" fontSize="sm" color="gray.600">
        <Text>开始: {formatTime(startTime)}</Text>
        <Text>长度: {formatTime(endTime - startTime)}</Text>
        <Text>结束: {formatTime(endTime)}</Text>
      </HStack>

      {/* 时间轴容器 */}
      <Box
        ref={timelineRef}
        position="relative"
        height="60px"
        bg="gray.100"
        borderRadius="md"
        cursor="pointer"
        onClick={handleTimelineClick}
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        userSelect="none"
      >
        {/* 背景时间轴 */}
        <Box
          position="absolute"
          top="50%"
          left="0"
          right="0"
          height="4px"
          bg="gray.300"
          borderRadius="2px"
          transform="translateY(-50%)"
        />

        {/* 选中区域 */}
        <Box
          position="absolute"
          top="50%"
          left={`${getPositionPercent(startTime)}%`}
          width={`${getPositionPercent(endTime - startTime)}%`}
          height="8px"
          bg="blue.400"
          borderRadius="4px"
          transform="translateY(-50%)"
          boxShadow="0 2px 4px rgba(0,0,0,0.2)"
        />

        {/* 当前播放位置 */}
        <Box
          position="absolute"
          top="0"
          bottom="0"
          left={`${getPositionPercent(currentTime)}%`}
          width="2px"
          bg="red.500"
          transform="translateX(-50%)"
          zIndex={10}
        >
          {/* 当前时间拖拽手柄 */}
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            width="12px"
            height="12px"
            bg="red.500"
            borderRadius="50%"
            border="2px solid white"
            cursor="grab"
            onMouseDown={handleCurrentDragStart}
            _active={{ cursor: 'grabbing' }}
            boxShadow="0 2px 4px rgba(0,0,0,0.3)"
          />
        </Box>

        {/* 开始时间拖拽手柄 */}
        <Box
          position="absolute"
          top="50%"
          left={`${getPositionPercent(startTime)}%`}
          transform="translate(-50%, -50%)"
          width="16px"
          height="16px"
          bg="blue.500"
          borderRadius="50%"
          border="2px solid white"
          cursor="grab"
          onMouseDown={handleStartDragStart}
          _active={{ cursor: 'grabbing' }}
          boxShadow="0 2px 4px rgba(0,0,0,0.3)"
          zIndex={20}
        />

        {/* 结束时间拖拽手柄 */}
        <Box
          position="absolute"
          top="50%"
          left={`${getPositionPercent(endTime)}%`}
          transform="translate(-50%, -50%)"
          width="16px"
          height="16px"
          bg="blue.500"
          borderRadius="50%"
          border="2px solid white"
          cursor="grab"
          onMouseDown={handleEndDragStart}
          _active={{ cursor: 'grabbing' }}
          boxShadow="0 2px 4px rgba(0,0,0,0.3)"
          zIndex={20}
        />

        {/* 悬停时间提示 */}
        {isHovering && !isDraggingStart && !isDraggingEnd && !isDraggingCurrent && (
          <Box
            position="absolute"
            bottom="100%"
            left={`${getPositionPercent(hoverTime)}%`}
            transform="translateX(-50%)"
            mb={2}
            bg="blackAlpha.800"
            color="white"
            px={2}
            py={1}
            borderRadius="md"
            fontSize="xs"
            whiteSpace="nowrap"
            zIndex={30}
          >
            {formatTime(hoverTime)}
          </Box>
        )}
      </Box>

      {/* 时间刻度 */}
      <Box position="relative" height="20px">
        {Array.from({ length: 11 }, (_, i) => {
          const time = (duration / 10) * i;
          return (
            <Box
              key={i}
              position="absolute"
              left={`${(i / 10) * 100}%`}
              transform="translateX(-50%)"
              textAlign="center"
            >
              <Box
                width="1px"
                height="8px"
                bg="gray.400"
                mx="auto"
                mb={1}
              />
              <Text fontSize="xs" color="gray.500">
                {formatTime(time)}
              </Text>
            </Box>
          );
        })}
      </Box>
    </VStack>
  );
};

export default VideoClipTimeline;
