import { Box, Text } from '@chakra-ui/react';
import { useMemo } from 'react';

interface CompassProps {
  heading?: number; // 方向角度，0-360度，0度为正北
  size?: number; // 罗盘大小，默认120px
}

const Compass = ({ heading, size = 120 }: CompassProps) => {
  // 计算指针的旋转角度
  const rotation = useMemo(() => {
    if (heading === undefined || isNaN(heading)) return 0;
    return heading;
  }, [heading]);



  // 生成刻度标记
  const generateTicks = () => {
    const ticks = [];
    const radius = size / 2 - 12;
    const centerX = size / 2;
    const centerY = size / 2;

    // 主要方向标记 (N, E, S, W) - 更柔和的颜色
    const mainDirections = [
      { angle: 0, label: 'N', color: '#e53e3e' }, // 北方用柔和的红色
      { angle: 90, label: 'E', color: '#4a5568' }, // 东方用深灰色
      { angle: 180, label: 'S', color: '#4a5568' }, // 南方用深灰色
      { angle: 270, label: 'W', color: '#4a5568' } // 西方用深灰色
    ];

    // 始终显示四个主要方向标记
    mainDirections.forEach(({ angle, label, color }) => {
      const radian = (angle - 90) * Math.PI / 180; // -90度调整，使0度指向上方
      const x = centerX + radius * Math.cos(radian);
      const y = centerY + radius * Math.sin(radian);

      ticks.push(
        <Text
          key={`main-${angle}`}
          position="absolute"
          left={`${x - 6}px`}
          top={`${y - 7}px`}
          fontSize={size >= 80 ? "xs" : "10px"}
          fontWeight="bold"
          color={color}
          textAlign="center"
          width="12px"
          lineHeight="1"
        >
          {label}
        </Text>
      );
    });

    // 在较大尺寸时显示次要方向标记
    if (size >= 80) {
      const subDirections = [45, 135, 225, 315];
      subDirections.forEach((angle) => {
        const radian = (angle - 90) * Math.PI / 180;
        const x = centerX + (radius - 3) * Math.cos(radian);
        const y = centerY + (radius - 3) * Math.sin(radian);

        ticks.push(
          <Box
            key={`sub-${angle}`}
            position="absolute"
            left={`${x - 1}px`}
            top={`${y - 1}px`}
            width="2px"
            height="2px"
            bg="gray.400"
            borderRadius="50%"
          />
        );
      });
    }

    // 度数刻度（每90度一个小刻度，小尺寸时减少刻度）
    const tickInterval = size >= 80 ? 30 : 90;
    for (let i = 0; i < 360; i += tickInterval) {
      if (i % 90 !== 0 || size < 80) { // 跳过主要方向（大尺寸时）
        const radian = (i - 90) * Math.PI / 180;
        const x = centerX + (radius - 5) * Math.cos(radian);
        const y = centerY + (radius - 5) * Math.sin(radian);

        ticks.push(
          <Box
            key={`tick-${i}`}
            position="absolute"
            left={`${x - 0.5}px`}
            top={`${y - 0.5}px`}
            width="1px"
            height="1px"
            bg="gray.300"
          />
        );
      }
    }

    return ticks;
  };

  return (
    <Box
      position="relative"
      width={`${size}px`}
      height={`${size}px`}
      mx="auto"
      my={0}
    >
      {/* 罗盘外圈 - 更柔和的设计 */}
      <Box
        position="absolute"
        top="0"
        left="0"
        width={`${size}px`}
        height={`${size}px`}
        borderRadius="50%"
        border="1px solid"
        borderColor="gray.200"
        bg="rgba(255, 255, 255, 0.95)"
        boxShadow="0 1px 3px rgba(0,0,0,0.08)"
        backdropFilter="blur(4px)"
      />

      {/* 内圈 - 更淡的背景 */}
      <Box
        position="absolute"
        top="6px"
        left="6px"
        width={`${size - 12}px`}
        height={`${size - 12}px`}
        borderRadius="50%"
        border="1px solid"
        borderColor="gray.100"
        bg="rgba(248, 250, 252, 0.8)"
      />

      {/* 刻度和方向标记 */}
      {generateTicks()}

      {/* 中心点 - 更柔和的设计 */}
      <Box
        position="absolute"
        top="50%"
        left="50%"
        transform="translate(-50%, -50%)"
        width="4px"
        height="4px"
        borderRadius="50%"
        bg="white"
        zIndex={3}
        border="1px solid #e53e3e"
        boxShadow="0 1px 2px rgba(0,0,0,0.1)"
      />

      {/* 指针 - 更柔和的颜色和阴影 */}
      {heading !== undefined && !isNaN(heading) && (
        <Box
          position="absolute"
          top="50%"
          left="50%"
          width="2px"
          height={`${size / 2 - 15}px`}
          bg="#e53e3e"
          transformOrigin="bottom center"
          transform={`translate(-50%, -100%) rotate(${rotation}deg)`}
          transition="transform 0.3s ease"
          zIndex={2}
          borderRadius="1px"
          boxShadow="0 1px 2px rgba(0,0,0,0.1)"
          _before={{
            content: '""',
            position: 'absolute',
            top: '-3px',
            left: '-1.5px',
            width: '5px',
            height: '5px',
            bg: '#e53e3e',
            clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
          }}
        />
      )}

      {/* 度数显示 - 更柔和的背景 */}
      <Box
        position="absolute"
        bottom="2px"
        right="2px"
        textAlign="center"
        bg="rgba(0, 0, 0, 0.5)"
        borderRadius="3px"
        px={1.5}
        py={0.5}
        backdropFilter="blur(2px)"
        border="1px solid rgba(255, 255, 255, 0.1)"
      >
        <Text fontSize="9px" color="white" fontWeight="medium" lineHeight="1">
          {heading !== undefined && !isNaN(heading) ? `${heading.toFixed(0)}°` : '--°'}
        </Text>
      </Box>
    </Box>
  );
};

export default Compass;
