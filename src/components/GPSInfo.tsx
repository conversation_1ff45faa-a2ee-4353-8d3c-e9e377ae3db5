import { Box, Text, Stack, Badge } from '@chakra-ui/react';
import { useMemo } from 'react';
import { GPSPoint } from '../types/electron';
import Compass from './Compass';

interface GPSInfoProps {
  currentGPSPoint?: GPSPoint;
  isExtractingGPS?: boolean;
}

const GPSInfo = ({ currentGPSPoint, isExtractingGPS }: GPSInfoProps) => {
  // 格式化GPS坐标
  const formatCoordinate = (value: number | undefined, type: 'lat' | 'lng'): string => {
    if (value === undefined) return '--';

    const direction = type === 'lat'
      ? (value >= 0 ? 'N' : 'S')
      : (value >= 0 ? 'E' : 'W');

    const absValue = Math.abs(value);
    const degrees = Math.floor(absValue);
    const minutes = (absValue - degrees) * 60;

    return `${degrees}°${minutes.toFixed(4)}'${direction}`;
  };

  // 格式化速度
  const formatSpeed = (speed: number | undefined): string => {
    if (speed === undefined) return '--';
    return `${speed.toFixed(1)} km/h`;
  };



  // 格式化方向
  const formatHeading = (bearing: number | undefined): string => {
    if (bearing === undefined) return '--';

    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(bearing / 45) % 8;
    return `${bearing.toFixed(1)}° (${directions[index]})`;
  };



  // 判断GPS数据是否有效
  const hasValidGPS = useMemo(() => {
    return currentGPSPoint &&
           typeof currentGPSPoint.latitude === 'number' &&
           typeof currentGPSPoint.longitude === 'number' &&
           !isNaN(currentGPSPoint.latitude) &&
           !isNaN(currentGPSPoint.longitude);
  }, [currentGPSPoint]);

  // 处理加载状态
  if (isExtractingGPS) {
    return (
      <Box
        p={4}
        height="100%"
      >
        <Text fontSize="md" fontWeight="semibold" color="gray.700" mb={3}>
          GPS 信息
        </Text>
        <Box textAlign="center" py={8}>
          <Text color="gray.500">正在提取GPS数据...</Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      p={4}
      height="100%"
      overflow="auto"
    >
      <Text
        fontSize="md"
        fontWeight="semibold"
        color="gray.700"
        mb={3}
      >
        GPS 信息
      </Text>

      {/* GPS状态 */}
      <Box mb={3}>
        <Badge
          colorScheme={hasValidGPS ? 'green' : 'red'}
          variant="subtle"
          fontSize="xs"
        >
          {hasValidGPS ? 'GPS 有效' : 'GPS 无效'}
        </Badge>
      </Box>

      {/* GPS详细信息 */}
      {hasValidGPS ? (
        <Stack gap={3}>
          {/* 坐标信息 */}
          <Box>
            <Text fontSize="xs" color="gray.500" mb={2}>位置坐标</Text>
            <Stack gap={1}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Text fontSize="xs" color="gray.600">纬度:</Text>
                <Text
                  fontSize="xs"
                  fontFamily="mono"
                  textAlign="right"
                  maxWidth="65%"
                  wordBreak="break-all"
                >
                  {formatCoordinate(currentGPSPoint?.latitude, 'lat')}
                </Text>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Text fontSize="xs" color="gray.600">经度:</Text>
                <Text
                  fontSize="xs"
                  fontFamily="mono"
                  textAlign="right"
                  maxWidth="65%"
                  wordBreak="break-all"
                >
                  {formatCoordinate(currentGPSPoint?.longitude, 'lng')}
                </Text>
              </Box>
            </Stack>
          </Box>

          {/* 运动信息 */}
          <Box>
            <Text fontSize="xs" color="gray.500" mb={2}>运动状态</Text>
            <Stack gap={1}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Text fontSize="xs" color="gray.600">速度:</Text>
                <Text
                  fontSize="xs"
                  fontFamily="mono"
                  fontWeight="semibold"
                  color="blue.600"
                >
                  {formatSpeed(currentGPSPoint?.speed)}
                </Text>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Text fontSize="xs" color="gray.600">方向:</Text>
                <Text
                  fontSize="xs"
                  fontFamily="mono"
                  textAlign="right"
                  maxWidth="65%"
                  wordBreak="break-all"
                >
                  {formatHeading(currentGPSPoint?.heading)}
                </Text>
              </Box>
            </Stack>
          </Box>

          {/* 罗盘显示 */}
          <Box>
            <Text fontSize="xs" color="gray.500" mb={2} textAlign="center">
              车辆方向
            </Text>
            <Box display="flex" justifyContent="center">
              <Compass
                heading={currentGPSPoint?.heading}
                size={90}
              />
            </Box>
          </Box>

        </Stack>
      ) : (
        <Box mt={3} p={3} bg="gray.50" borderRadius="md">
          <Text fontSize="xs" color="gray.500" textAlign="center">
            当前视频文件无GPS数据
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default GPSInfo;
