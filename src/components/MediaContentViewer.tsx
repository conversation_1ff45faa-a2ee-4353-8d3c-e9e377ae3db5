import React, { useState } from 'react';
import {
  Dialog,
  Portal,
  Text,
  VStack,
  HStack,
  Button,
  Image,
  Badge,
  IconButton,
  Tooltip,
  Grid,
  Box,
  Tabs
} from '@chakra-ui/react';
import {
  FiX,
  FiImage,
  FiVideo,
  FiTrash2,
  FiExternalLink,
  FiPlay,
  FiFolder,
  FiChevronDown,
  FiChevronRight
} from 'react-icons/fi';
import { MediaScreenshot, MediaClip, MediaGroup } from '../hooks/useFileMediaContent';

interface MediaContentViewerProps {
  open: boolean;
  onOpenChange: (details: { open: boolean }) => void;
  videoPath: string;
  videoName: string;
  screenshots: MediaScreenshot[];
  clips: MediaClip[];
  groups: MediaGroup[];
  onRemoveScreenshot: (screenshotId: string) => void;
  onRemoveClip: (clipId: string) => void;
  onRemoveGroup: (groupId: string) => void;
  onOpenScreenshot: (imagePath: string) => void;
  onPlayClip: (clipPath: string) => void;
  onOpenInFolder: (filePath: string) => void;
}

const MediaContentViewer: React.FC<MediaContentViewerProps> = ({
  open,
  onOpenChange,
  videoPath,
  videoName,
  screenshots,
  clips,
  groups,
  onRemoveScreenshot,
  onRemoveClip,
  onRemoveGroup,
  onOpenScreenshot,
  onPlayClip,
  onOpenInFolder
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}m${secs}s`;
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  // 获取未分组的截图和剪辑
  const ungroupedScreenshots = screenshots.filter(s => !s.groupId);
  const ungroupedClips = clips.filter(c => !c.groupId);

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange} size="xl">
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content maxH="80vh" overflowY="auto">
            <Dialog.Header>
              <Dialog.Title>
                <HStack>
                  <FiFolder />
                  <Text>媒体内容 - {videoName}</Text>
                  <Badge colorPalette="blue" size="sm">
                    {screenshots.length} 截图
                  </Badge>
                  <Badge colorPalette="green" size="sm">
                    {clips.length} 剪辑
                  </Badge>
                </HStack>
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.CloseTrigger>
              <FiX />
            </Dialog.CloseTrigger>

            <Dialog.Body>
              <Tabs.Root defaultValue="all" variant="enclosed">
                <Tabs.List>
                  <Tabs.Trigger value="all">
                    <HStack>
                      <FiFolder />
                      <Text>全部</Text>
                    </HStack>
                  </Tabs.Trigger>
                  <Tabs.Trigger value="screenshots">
                    <HStack>
                      <FiImage />
                      <Text>截图 ({screenshots.length})</Text>
                    </HStack>
                  </Tabs.Trigger>
                  <Tabs.Trigger value="clips">
                    <HStack>
                      <FiVideo />
                      <Text>剪辑 ({clips.length})</Text>
                    </HStack>
                  </Tabs.Trigger>
                </Tabs.List>

                <Tabs.Content value="all">
                  <VStack gap={4} align="stretch">
                    {/* 分组内容 */}
                    {groups.map(group => {
                      const groupScreenshots = screenshots.filter(s => s.groupId === group.id);
                      const groupClips = clips.filter(c => c.groupId === group.id);
                      const isGroupExpanded = expandedGroups.has(group.id);

                      return (
                        <Box key={group.id} border="1px solid" borderColor="gray.300" borderRadius="md" bg="gray.50">
                          {/* 分组标题 */}
                          <HStack
                            p={3}
                            cursor="pointer"
                            onClick={() => toggleGroup(group.id)}
                            _hover={{ bg: 'gray.100' }}
                            borderRadius="md"
                          >
                            {isGroupExpanded ? <FiChevronDown /> : <FiChevronRight />}
                            <Text fontSize="sm" fontWeight="medium" flex={1}>
                              {group.name}
                            </Text>
                            <HStack gap={2}>
                              <Badge colorPalette={group.type === 'screenshot' ? 'blue' : 'green'} size="sm">
                                {group.type === 'screenshot' ? '截图组' : '剪辑组'}
                              </Badge>
                              <Badge colorPalette="gray" size="sm">
                                {groupScreenshots.length + groupClips.length} 项
                              </Badge>
                              <Tooltip label="删除分组">
                                <IconButton
                                  size="sm"
                                  variant="ghost"
                                  colorPalette="red"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onRemoveGroup(group.id);
                                  }}
                                  aria-label="删除分组"
                                >
                                  <FiTrash2 />
                                </IconButton>
                              </Tooltip>
                            </HStack>
                          </HStack>

                          {/* 分组内容 */}
                          {isGroupExpanded && (
                            <Box p={3} borderTop="1px solid" borderColor="gray.200" bg="white">
                              <VStack gap={3} align="stretch">
                                {/* 分组截图 */}
                                {groupScreenshots.length > 0 && (
                                  <Box>
                                    <Text fontSize="sm" fontWeight="medium" mb={2}>截图</Text>
                                    <Grid templateColumns="repeat(auto-fill, minmax(120px, 1fr))" gap={3}>
                                      {groupScreenshots.map(screenshot => (
                                        <Box key={screenshot.id} position="relative" bg="white" borderRadius="md" overflow="hidden" border="1px solid" borderColor="gray.200">
                                          <Image
                                            src={`file://${screenshot.imagePath}`}
                                            alt={`截图 ${formatTime(screenshot.timestamp)}`}
                                            w="100%"
                                            h="80px"
                                            objectFit="cover"
                                            cursor="pointer"
                                            onClick={() => onOpenScreenshot(screenshot.imagePath)}
                                          />
                                          <Badge
                                            position="absolute"
                                            top={1}
                                            right={1}
                                            fontSize="xs"
                                            colorPalette="blackAlpha"
                                          >
                                            {formatTime(screenshot.timestamp)}
                                          </Badge>
                                          <HStack position="absolute" bottom={1} left={1} right={1} justify="space-between">
                                            <Tooltip label="在文件夹中显示">
                                              <IconButton
                                                size="xs"
                                                variant="solid"
                                                colorPalette="blue"
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onOpenInFolder(screenshot.imagePath);
                                                }}
                                                aria-label="在文件夹中显示"
                                              >
                                                <FiExternalLink />
                                              </IconButton>
                                            </Tooltip>
                                            <Tooltip label="删除">
                                              <IconButton
                                                size="xs"
                                                variant="solid"
                                                colorPalette="red"
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onRemoveScreenshot(screenshot.id);
                                                }}
                                                aria-label="删除"
                                              >
                                                <FiTrash2 />
                                              </IconButton>
                                            </Tooltip>
                                          </HStack>
                                        </Box>
                                      ))}
                                    </Grid>
                                  </Box>
                                )}

                                {/* 分组剪辑 */}
                                {groupClips.length > 0 && (
                                  <Box>
                                    <Text fontSize="sm" fontWeight="medium" mb={2}>剪辑</Text>
                                    <VStack gap={2} align="stretch">
                                      {groupClips.map(clip => (
                                        <HStack key={clip.id} p={3} bg="gray.50" borderRadius="md" border="1px solid" borderColor="gray.200">
                                          <FiVideo />
                                          <VStack align="start" flex={1} gap={0}>
                                            <Text fontSize="sm" fontWeight="medium">
                                              {formatTime(clip.startTime)} - {formatTime(clip.endTime)}
                                            </Text>
                                            <Text fontSize="xs" color="gray.500">
                                              {formatDate(clip.createdAt)} • {formatDuration(clip.duration)}
                                            </Text>
                                          </VStack>
                                          <HStack gap={1}>
                                            <Tooltip label="播放">
                                              <IconButton
                                                size="sm"
                                                variant="ghost"
                                                colorPalette="blue"
                                                onClick={() => onPlayClip(clip.outputPath)}
                                                aria-label="播放"
                                              >
                                                <FiPlay />
                                              </IconButton>
                                            </Tooltip>
                                            <Tooltip label="在文件夹中显示">
                                              <IconButton
                                                size="sm"
                                                variant="ghost"
                                                colorPalette="gray"
                                                onClick={() => onOpenInFolder(clip.outputPath)}
                                                aria-label="在文件夹中显示"
                                              >
                                                <FiFolder />
                                              </IconButton>
                                            </Tooltip>
                                            <Tooltip label="删除">
                                              <IconButton
                                                size="sm"
                                                variant="ghost"
                                                colorPalette="red"
                                                onClick={() => onRemoveClip(clip.id)}
                                                aria-label="删除"
                                              >
                                                <FiTrash2 />
                                              </IconButton>
                                            </Tooltip>
                                          </HStack>
                                        </HStack>
                                      ))}
                                    </VStack>
                                  </Box>
                                )}
                              </VStack>
                            </Box>
                          )}
                        </Box>
                      );
                    })}

                    {/* 未分组的截图 */}
                    {ungroupedScreenshots.length > 0 && (
                      <Box>
                        <Text fontSize="md" fontWeight="medium" mb={3} color="gray.700">
                          <FiImage style={{ display: 'inline', marginRight: '8px' }} />
                          截图 ({ungroupedScreenshots.length})
                        </Text>
                        <Grid templateColumns="repeat(auto-fill, minmax(150px, 1fr))" gap={3}>
                          {ungroupedScreenshots.map(screenshot => (
                            <Box key={screenshot.id} position="relative" bg="white" borderRadius="md" overflow="hidden" border="1px solid" borderColor="gray.200">
                              <Image
                                src={`file://${screenshot.imagePath}`}
                                alt={`截图 ${formatTime(screenshot.timestamp)}`}
                                w="100%"
                                h="100px"
                                objectFit="cover"
                                cursor="pointer"
                                onClick={() => onOpenScreenshot(screenshot.imagePath)}
                              />
                              <Badge
                                position="absolute"
                                top={2}
                                right={2}
                                fontSize="xs"
                                colorPalette="blackAlpha"
                              >
                                {formatTime(screenshot.timestamp)}
                              </Badge>
                              <HStack position="absolute" bottom={2} left={2} right={2} justify="space-between">
                                <Tooltip label="在文件夹中显示">
                                  <IconButton
                                    size="sm"
                                    variant="solid"
                                    colorPalette="blue"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onOpenInFolder(screenshot.imagePath);
                                    }}
                                    aria-label="在文件夹中显示"
                                  >
                                    <FiExternalLink />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip label="删除">
                                  <IconButton
                                    size="sm"
                                    variant="solid"
                                    colorPalette="red"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onRemoveScreenshot(screenshot.id);
                                    }}
                                    aria-label="删除"
                                  >
                                    <FiTrash2 />
                                  </IconButton>
                                </Tooltip>
                              </HStack>
                            </Box>
                          ))}
                        </Grid>
                      </Box>
                    )}

                    {/* 未分组的剪辑 */}
                    {ungroupedClips.length > 0 && (
                      <Box>
                        <Text fontSize="md" fontWeight="medium" mb={3} color="gray.700">
                          <FiVideo style={{ display: 'inline', marginRight: '8px' }} />
                          剪辑 ({ungroupedClips.length})
                        </Text>
                        <VStack gap={3} align="stretch">
                          {ungroupedClips.map(clip => (
                            <HStack key={clip.id} p={4} bg="white" borderRadius="md" border="1px solid" borderColor="gray.200">
                              <FiVideo size={20} />
                              <VStack align="start" flex={1} gap={1}>
                                <Text fontSize="md" fontWeight="medium">
                                  {formatTime(clip.startTime)} - {formatTime(clip.endTime)}
                                </Text>
                                <Text fontSize="sm" color="gray.500">
                                  {formatDate(clip.createdAt)} • {formatDuration(clip.duration)}
                                </Text>
                              </VStack>
                              <HStack gap={2}>
                                <Tooltip label="播放">
                                  <IconButton
                                    size="md"
                                    variant="ghost"
                                    colorPalette="blue"
                                    onClick={() => onPlayClip(clip.outputPath)}
                                    aria-label="播放"
                                  >
                                    <FiPlay />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip label="在文件夹中显示">
                                  <IconButton
                                    size="md"
                                    variant="ghost"
                                    colorPalette="gray"
                                    onClick={() => onOpenInFolder(clip.outputPath)}
                                    aria-label="在文件夹中显示"
                                  >
                                    <FiFolder />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip label="删除">
                                  <IconButton
                                    size="md"
                                    variant="ghost"
                                    colorPalette="red"
                                    onClick={() => onRemoveClip(clip.id)}
                                    aria-label="删除"
                                  >
                                    <FiTrash2 />
                                  </IconButton>
                                </Tooltip>
                              </HStack>
                            </HStack>
                          ))}
                        </VStack>
                      </Box>
                    )}

                    {/* 空状态 */}
                    {screenshots.length === 0 && clips.length === 0 && (
                      <Box textAlign="center" py={8}>
                        <Text color="gray.500">该视频还没有保存任何截图或剪辑</Text>
                      </Box>
                    )}
                  </VStack>
                </Tabs.Content>

                <Tabs.Content value="screenshots">
                  <VStack gap={4} align="stretch">
                    {screenshots.length > 0 ? (
                      <Grid templateColumns="repeat(auto-fill, minmax(180px, 1fr))" gap={4}>
                        {screenshots.map(screenshot => (
                          <Box key={screenshot.id} position="relative" bg="white" borderRadius="md" overflow="hidden" border="1px solid" borderColor="gray.200">
                            <Image
                              src={`file://${screenshot.imagePath}`}
                              alt={`截图 ${formatTime(screenshot.timestamp)}`}
                              w="100%"
                              h="120px"
                              objectFit="cover"
                              cursor="pointer"
                              onClick={() => onOpenScreenshot(screenshot.imagePath)}
                            />
                            <Badge
                              position="absolute"
                              top={2}
                              right={2}
                              fontSize="sm"
                              colorPalette="blackAlpha"
                            >
                              {formatTime(screenshot.timestamp)}
                            </Badge>
                            <HStack position="absolute" bottom={2} left={2} right={2} justify="space-between">
                              <Tooltip label="在文件夹中显示">
                                <IconButton
                                  size="sm"
                                  variant="solid"
                                  colorPalette="blue"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onOpenInFolder(screenshot.imagePath);
                                  }}
                                  aria-label="在文件夹中显示"
                                >
                                  <FiExternalLink />
                                </IconButton>
                              </Tooltip>
                              <Tooltip label="删除">
                                <IconButton
                                  size="sm"
                                  variant="solid"
                                  colorPalette="red"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onRemoveScreenshot(screenshot.id);
                                  }}
                                  aria-label="删除"
                                >
                                  <FiTrash2 />
                                </IconButton>
                              </Tooltip>
                            </HStack>
                          </Box>
                        ))}
                      </Grid>
                    ) : (
                      <Box textAlign="center" py={8}>
                        <Text color="gray.500">该视频还没有保存任何截图</Text>
                      </Box>
                    )}
                  </VStack>
                </Tabs.Content>

                <Tabs.Content value="clips">
                  <VStack gap={4} align="stretch">
                    {clips.length > 0 ? (
                      clips.map(clip => (
                        <HStack key={clip.id} p={4} bg="white" borderRadius="md" border="1px solid" borderColor="gray.200">
                          <FiVideo size={24} />
                          <VStack align="start" flex={1} gap={1}>
                            <Text fontSize="lg" fontWeight="medium">
                              {formatTime(clip.startTime)} - {formatTime(clip.endTime)}
                            </Text>
                            <Text fontSize="sm" color="gray.500">
                              {formatDate(clip.createdAt)} • {formatDuration(clip.duration)}
                            </Text>
                          </VStack>
                          <HStack gap={2}>
                            <Tooltip label="播放">
                              <IconButton
                                size="md"
                                variant="ghost"
                                colorPalette="blue"
                                onClick={() => onPlayClip(clip.outputPath)}
                                aria-label="播放"
                              >
                                <FiPlay />
                              </IconButton>
                            </Tooltip>
                            <Tooltip label="在文件夹中显示">
                              <IconButton
                                size="md"
                                variant="ghost"
                                colorPalette="gray"
                                onClick={() => onOpenInFolder(clip.outputPath)}
                                aria-label="在文件夹中显示"
                              >
                                <FiFolder />
                              </IconButton>
                            </Tooltip>
                            <Tooltip label="删除">
                              <IconButton
                                size="md"
                                variant="ghost"
                                colorPalette="red"
                                onClick={() => onRemoveClip(clip.id)}
                                aria-label="删除"
                              >
                                <FiTrash2 />
                              </IconButton>
                            </Tooltip>
                          </HStack>
                        </HStack>
                      ))
                    ) : (
                      <Box textAlign="center" py={8}>
                        <Text color="gray.500">该视频还没有保存任何剪辑</Text>
                      </Box>
                    )}
                  </VStack>
                </Tabs.Content>
              </Tabs.Root>
            </Dialog.Body>

            <Dialog.Footer>
              <Button variant="outline" onClick={() => onOpenChange({ open: false })}>
                关闭
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default MediaContentViewer;
