import {
  Dialog,
  Portal,
  Button,
  Input,
  Text,
  VStack,
  HStack,
  Box
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { FiInfo, FiX } from 'react-icons/fi';

interface ApiKeyConfigModalProps {
  open: boolean;
  onOpenChange: (details: { open: boolean }) => void;
  currentApiKey?: string | null;
  onApiKeyUpdate: (apiKey: string | null) => Promise<void>;
}

const ApiKeyConfigModal = ({
  open,
  onOpenChange,
  currentApiKey,
  onApiKeyUpdate
}: ApiKeyConfigModalProps) => {
  const [apiKey, setApiKey] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // 当弹窗打开时，设置当前的App Key值
  useEffect(() => {
    if (open) {
      setApiKey(currentApiKey || '');
    }
  }, [open, currentApiKey]);

  // 处理保存
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // 允许设置为空，传递空字符串或null
      await onApiKeyUpdate(apiKey.trim() || null);
      onOpenChange({ open: false });
    } catch (error) {
      console.error('保存App Key失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setApiKey(currentApiKey || '');
    onOpenChange({ open: false });
  };

  // 处理打开外部链接
  const handleOpenExternal = async (url: string) => {
    try {
      const success = await window.electronAPI.openExternal(url);
      if (!success) {
        console.error('打开外部链接失败');
      }
    } catch (error) {
      console.error('打开外部链接时出错:', error);
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange} size="md" placement="center">
      <Portal>
        <Dialog.Backdrop bg="blackAlpha.300" backdropFilter="blur(10px)" />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>高德地图配置</Dialog.Title>
            </Dialog.Header>
            <Dialog.CloseTrigger>
              <FiX />
            </Dialog.CloseTrigger>

            <Dialog.Body>
          <VStack gap={4} align="stretch">
            <Box p={3} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.500">
              <HStack gap={2} align="start">
                <FiInfo color="blue" size={16} style={{ marginTop: '2px', flexShrink: 0 }} />
                <VStack align="start" gap={1}>
                  <Text fontSize="sm" fontWeight="medium" color="blue.700">
                    配置说明
                  </Text>
                  <Text fontSize="xs" color="blue.600">
                    配置高德地图App Key以启用地图和GPS轨迹功能。App Key完全免费，用于访问高德地图服务。留空则禁用地图显示。
                  </Text>
                </VStack>
              </HStack>
            </Box>

            <VStack align="stretch" gap={2}>
              <HStack justify="space-between" align="center">
                <Text fontSize="sm" fontWeight="medium" color="gray.700">
                  App Key
                </Text>
                {apiKey && (
                  <Button
                    size="xs"
                    variant="ghost"
                    colorPalette="gray"
                    onClick={() => setApiKey('')}
                  >
                    清空
                  </Button>
                )}
              </HStack>
              {/* 添加粘贴事件监听，但不阻止默认行为 */}
              <input
                type="text"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                onPaste={(e) => {
                  console.log('🎯 粘贴事件触发');
                  console.log('🎯 clipboardData:', e.clipboardData);
                  console.log('🎯 clipboardData.getData("text"):', e.clipboardData?.getData('text'));
                  // 不调用 e.preventDefault()，让浏览器处理默认行为
                }}
                onKeyDown={(e) => {
                  if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
                    console.log('🎯 粘贴快捷键检测到');
                  }
                }}
                placeholder="例如：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none'
                }}
              />
              <Text fontSize="xs" color="gray.500">
                请按照下方引导获取免费的App Key，或留空禁用地图功能
              </Text>
            </VStack>

            {/* 获取App Key的引导信息 */}
            <Box p={4} bg="orange.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="orange.500">
              <VStack align="start" gap={3}>
                <HStack gap={2} align="start">
                  <Text fontSize="16px">🔑</Text>
                  <VStack align="start" gap={1}>
                    <Text fontSize="sm" fontWeight="medium" color="orange.700">
                      如何获取高德地图App Key
                    </Text>
                    <Text fontSize="xs" color="orange.600">
                      按照以下步骤免费申请您的专属App Key
                    </Text>
                  </VStack>
                </HStack>

                <VStack align="start" gap={2} pl={4}>
                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">1.</Text>
                    <VStack align="start" gap={1}>
                      <Text fontSize="xs" color="orange.700">访问高德开放平台</Text>
                      <Text
                        fontSize="xs"
                        color="blue.600"
                        textDecoration="underline"
                        cursor="pointer"
                        _hover={{ color: "blue.800" }}
                        onClick={() => handleOpenExternal('https://lbs.amap.com/')}
                      >
                        https://lbs.amap.com/ ↗
                      </Text>
                    </VStack>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">2.</Text>
                    <Text fontSize="xs" color="orange.700">注册并登录账号</Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">3.</Text>
                    <Text fontSize="xs" color="orange.700">进入控制台 → 应用管理 → 创建新应用</Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">4.</Text>
                    <Text fontSize="xs" color="orange.700">添加Key，服务平台选择 <Text as="span" fontWeight="medium" bg="orange.100" px={1} borderRadius="sm">"Web端(JS API)"</Text></Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">5.</Text>
                    <Text fontSize="xs" color="orange.700">复制生成的App Key到上方输入框</Text>
                  </HStack>
                </VStack>
              </VStack>
            </Box>
          </VStack>
            </Dialog.Body>

            <Dialog.Footer>
              <HStack gap={3}>
                <Dialog.ActionTrigger asChild>
                  <Button
                    variant="ghost"
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    取消
                  </Button>
                </Dialog.ActionTrigger>
                <Button
                  colorPalette="blue"
                  onClick={handleSave}
                  loading={isSaving}
                >
                  保存
                </Button>
              </HStack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default ApiKeyConfigModal;
