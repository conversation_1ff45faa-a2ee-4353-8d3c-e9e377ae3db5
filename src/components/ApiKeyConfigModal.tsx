import {
  Dialog,
  Portal,
  Button,
  Input,
  Text,
  VStack,
  HStack,
  Box
} from '@chakra-ui/react';
import { useState, useEffect, useRef } from 'react';
import { FiInfo, FiX, FiClipboard } from 'react-icons/fi';

interface ApiKeyConfigModalProps {
  open: boolean;
  onOpenChange: (details: { open: boolean }) => void;
  currentApiKey?: string | null;
  onApiKeyUpdate: (apiKey: string | null) => Promise<void>;
}

const ApiKeyConfigModal = ({
  open,
  onOpenChange,
  currentApiKey,
  onApiKeyUpdate
}: ApiKeyConfigModalProps) => {
  const [apiKey, setApiKey] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 当弹窗打开时，设置当前的App Key值
  useEffect(() => {
    if (open) {
      setApiKey(currentApiKey || '');
      // 延迟聚焦到输入框
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open, currentApiKey]);

  // 处理保存
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // 允许设置为空，传递空字符串或null
      await onApiKeyUpdate(apiKey.trim() || null);
      onOpenChange({ open: false });
    } catch (error) {
      console.error('保存App Key失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setApiKey(currentApiKey || '');
    onOpenChange({ open: false });
  };

  // 处理键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Ctrl+V 或 Cmd+V 粘贴
    if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
      // 让浏览器处理默认的粘贴行为
      return;
    }

    // Ctrl+A 或 Cmd+A 全选
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
      e.currentTarget.select();
      e.preventDefault();
    }

    // Enter 键保存
    if (e.key === 'Enter') {
      handleSave();
    }

    // Escape 键取消
    if (e.key === 'Escape') {
      handleCancel();
    }
  };

  // 处理粘贴按钮点击
  const handlePasteClick = async () => {
    try {
      // 尝试使用 Clipboard API
      if (navigator.clipboard && navigator.clipboard.readText) {
        const text = await navigator.clipboard.readText();
        if (text) {
          setApiKey(text.trim());
        }
      } else {
        // 如果 Clipboard API 不可用，提示用户使用键盘快捷键
        alert('请使用 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac) 粘贴');
      }
    } catch (error) {
      // 如果权限被拒绝或其他错误，提示用户使用键盘快捷键
      alert('请使用 Ctrl+V (Windows/Linux) 或 Cmd+V (Mac) 粘贴');
    }
  };

  // 处理打开外部链接
  const handleOpenExternal = async (url: string) => {
    try {
      const success = await window.electronAPI.openExternal(url);
      if (!success) {
        console.error('打开外部链接失败');
      }
    } catch (error) {
      console.error('打开外部链接时出错:', error);
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange} size="md" placement="center">
      <Portal>
        <Dialog.Backdrop bg="blackAlpha.300" backdropFilter="blur(10px)" />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>高德地图配置</Dialog.Title>
            </Dialog.Header>
            <Dialog.CloseTrigger>
              <FiX />
            </Dialog.CloseTrigger>

            <Dialog.Body>
          <VStack gap={4} align="stretch">
            <Box p={3} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.500">
              <HStack gap={2} align="start">
                <FiInfo color="blue" size={16} style={{ marginTop: '2px', flexShrink: 0 }} />
                <VStack align="start" gap={1}>
                  <Text fontSize="sm" fontWeight="medium" color="blue.700">
                    配置说明
                  </Text>
                  <Text fontSize="xs" color="blue.600">
                    配置高德地图App Key以启用地图和GPS轨迹功能。App Key完全免费，用于访问高德地图服务。留空则禁用地图显示。
                  </Text>
                </VStack>
              </HStack>
            </Box>

            <VStack align="stretch" gap={2}>
              <HStack justify="space-between" align="center">
                <Text fontSize="sm" fontWeight="medium" color="gray.700">
                  App Key
                </Text>
                <HStack gap={1}>
                  <Button
                    size="xs"
                    variant="ghost"
                    colorPalette="blue"
                    onClick={handlePasteClick}
                    title="从剪贴板粘贴"
                  >
                    <FiClipboard size={12} />
                    粘贴
                  </Button>
                  {apiKey && (
                    <Button
                      size="xs"
                      variant="ghost"
                      colorPalette="gray"
                      onClick={() => setApiKey('')}
                    >
                      清空
                    </Button>
                  )}
                </HStack>
              </HStack>
              <Input
                ref={inputRef}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                onKeyDown={handleKeyDown}
                onPaste={(e) => {
                  // 确保粘贴操作正常工作
                  const pastedText = e.clipboardData?.getData('text') || '';
                  if (pastedText) {
                    setApiKey(pastedText.trim());
                    e.preventDefault();
                  }
                }}
                placeholder="例如：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
                size="md"
                bg="white"
                borderColor="gray.300"
                _hover={{ borderColor: "gray.400" }}
                _focus={{ borderColor: "blue.500", boxShadow: "0 0 0 1px #3182ce" }}
                autoComplete="off"
                spellCheck={false}
                title="支持 Ctrl+V 粘贴、Ctrl+A 全选、Enter 保存、Escape 取消"
              />
              <Text fontSize="xs" color="gray.500">
                请按照下方引导获取免费的App Key，或留空禁用地图功能
              </Text>
              <Text fontSize="xs" color="blue.600">
                💡 提示：支持 Ctrl+V 粘贴、Ctrl+A 全选，或点击上方"粘贴"按钮
              </Text>
            </VStack>

            {/* 获取App Key的引导信息 */}
            <Box p={4} bg="orange.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="orange.500">
              <VStack align="start" gap={3}>
                <HStack gap={2} align="start">
                  <Text fontSize="16px">🔑</Text>
                  <VStack align="start" gap={1}>
                    <Text fontSize="sm" fontWeight="medium" color="orange.700">
                      如何获取高德地图App Key
                    </Text>
                    <Text fontSize="xs" color="orange.600">
                      按照以下步骤免费申请您的专属App Key
                    </Text>
                  </VStack>
                </HStack>

                <VStack align="start" gap={2} pl={4}>
                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">1.</Text>
                    <VStack align="start" gap={1}>
                      <Text fontSize="xs" color="orange.700">访问高德开放平台</Text>
                      <Text
                        fontSize="xs"
                        color="blue.600"
                        textDecoration="underline"
                        cursor="pointer"
                        _hover={{ color: "blue.800" }}
                        onClick={() => handleOpenExternal('https://lbs.amap.com/')}
                      >
                        https://lbs.amap.com/ ↗
                      </Text>
                    </VStack>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">2.</Text>
                    <Text fontSize="xs" color="orange.700">注册并登录账号</Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">3.</Text>
                    <Text fontSize="xs" color="orange.700">进入控制台 → 应用管理 → 创建新应用</Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">4.</Text>
                    <Text fontSize="xs" color="orange.700">添加Key，服务平台选择 <Text as="span" fontWeight="medium" bg="orange.100" px={1} borderRadius="sm">"Web端(JS API)"</Text></Text>
                  </HStack>

                  <HStack gap={2} align="start">
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" minWidth="12px">5.</Text>
                    <Text fontSize="xs" color="orange.700">复制生成的App Key到上方输入框</Text>
                  </HStack>
                </VStack>
              </VStack>
            </Box>
          </VStack>
            </Dialog.Body>

            <Dialog.Footer>
              <HStack gap={3}>
                <Dialog.ActionTrigger asChild>
                  <Button
                    variant="ghost"
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    取消
                  </Button>
                </Dialog.ActionTrigger>
                <Button
                  colorPalette="blue"
                  onClick={handleSave}
                  loading={isSaving}
                >
                  保存
                </Button>
              </HStack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default ApiKeyConfigModal;
