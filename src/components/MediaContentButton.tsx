import React from 'react';
import {
  Button,
  HStack,
  Badge,
  Tooltip
} from '@chakra-ui/react';
import { FiImage, FiVideo, FiFolder } from 'react-icons/fi';

interface MediaContentButtonProps {
  videoPath: string;
  screenshotCount: number;
  clipCount: number;
  onClick: () => void;
}

const MediaContentButton: React.FC<MediaContentButtonProps> = ({
  videoPath,
  screenshotCount,
  clipCount,
  onClick
}) => {
  const totalCount = screenshotCount + clipCount;

  // 如果没有任何媒体内容，不显示按钮
  if (totalCount === 0) {
    return null;
  }

  return (
    <Tooltip label={`查看媒体内容: ${screenshotCount} 个截图, ${clipCount} 个剪辑`}>
      <Button
        size="xs"
        variant="outline"
        colorPalette="blue"
        onClick={(e) => {
          e.stopPropagation(); // 防止触发文件选择
          onClick();
        }}
        _hover={{ bg: 'blue.50' }}
      >
        <HStack gap={1}>
          <FiFolder size={12} />
          {screenshotCount > 0 && (
            <HStack gap={0}>
              <FiImage size={10} />
              <Badge size="xs" colorPalette="blue" variant="solid">
                {screenshotCount}
              </Badge>
            </HStack>
          )}
          {clipCount > 0 && (
            <HStack gap={0}>
              <FiVideo size={10} />
              <Badge size="xs" colorPalette="green" variant="solid">
                {clipCount}
              </Badge>
            </HStack>
          )}
        </HStack>
      </Button>
    </Tooltip>
  );
};

export default MediaContentButton;
