import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Box,
  Flex,
  Button,
  Text,
  Stack,
  Badge,
  Portal,
  VStack
} from '@chakra-ui/react';
import { FiPlay, FiPause, FiSkipBack, FiSkipForward, FiVolume2, FiVolumeX, FiMaximize, FiMinimize, FiZoomIn, FiCamera, FiScissors } from 'react-icons/fi';
import ClipControls from './ClipControls';
import MultiVideoControlsMenu from './MultiVideoControlsMenu';

export interface VideoPlayerControlsProps {
  // 播放状态
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  isLoading?: boolean;

  // 控制函数
  onTogglePlay: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number) => void;
  onToggleMute: () => void;
  onToggleFullscreen: () => void;
  onSkipTime?: (seconds: number) => void;
  onToggleZoom?: () => void;

  // 缩放控制
  scale?: number;
  showZoomControls?: boolean;
  onScaleChange?: (scale: number) => void;
  onResetZoom?: () => void;

  // 多视频播放器扩展
  videoCount?: number;
  availableAngles?: string[];
  showAngleControls?: boolean;
  onToggleAngleControls?: () => void;
  isMultiVideo?: boolean; // 标识是否为多视频模式

  // 视频显示控制
  multiVideoFiles?: Array<{id: string; name: string}>;
  videoVisibility?: boolean[];
  onToggleVideoVisibility?: (index: number) => void;

  // 多视频模式下的额外控制
  multiVideoExtensions?: {
    showProgressBar?: boolean; // 是否显示进度条
    showTimeDisplay?: boolean; // 是否显示时间显示
    showVolumeSlider?: boolean; // 是否显示音量滑块
    customButtons?: React.ReactNode; // 自定义按钮
  };

  // 返回多视频模式
  showReturnToMultiVideo?: boolean; // 是否显示返回多视频模式按钮
  onReturnToMultiVideo?: () => void; // 返回多视频模式回调

  // 剪辑功能
  showClipControls?: boolean; // 是否显示剪辑控制按钮
  onCaptureFrame?: () => void; // 截取当前帧回调
  onStartClip?: () => void; // 开始剪辑回调
  videoPath?: string; // 视频文件路径，用于剪辑功能
  onScreenshotTaken?: (screenshot: any) => void; // 截图完成回调
  videoFiles?: any[]; // 多视频文件列表（用于多视频截图保存）

  // 剪辑模式
  isClipMode?: boolean; // 是否处于剪辑模式
  clipStartTime?: number; // 剪辑开始时间
  clipEndTime?: number; // 剪辑结束时间
  onToggleClipMode?: () => void; // 切换剪辑模式
  onClipTimeChange?: (startTime: number, endTime: number) => void; // 剪辑时间变化回调
  onSaveClip?: () => void; // 保存剪辑回调

  // 样式控制
  showVolumeControl?: boolean;
  showSkipButtons?: boolean;
  showFullscreenButton?: boolean;
  compact?: boolean;
}

const VideoPlayerControls = ({
  isPlaying,
  currentTime,
  duration,
  isMuted,
  isFullscreen,
  isLoading = false,
  onTogglePlay,
  onSeek,
  onToggleMute,
  onToggleFullscreen,
  onSkipTime,
  onToggleZoom,
  scale = 1,
  showZoomControls = false,
  onScaleChange,
  onResetZoom,
  videoCount,
  availableAngles = [],
  showAngleControls = false,
  onToggleAngleControls,
  isMultiVideo = false,
  multiVideoExtensions = {},
  multiVideoFiles = [],
  videoVisibility = [],
  onToggleVideoVisibility,
  showReturnToMultiVideo = false,
  onReturnToMultiVideo,
  showClipControls = true,
  onCaptureFrame,
  onStartClip,
  videoPath,
  onScreenshotTaken,
  videoFiles = [],
  showVolumeControl = true,
  showSkipButtons = true,
  showFullscreenButton = true,
  compact = false,
  // 剪辑模式相关
  isClipMode = false,
  clipStartTime = 0,
  clipEndTime = 0,
  onToggleClipMode,
  onClipTimeChange,
  onSaveClip
}: VideoPlayerControlsProps) => {
  const [isDraggingProgress, setIsDraggingProgress] = useState(false);
  const [isHoveringProgress, setIsHoveringProgress] = useState(false);
  const [hoverTime, setHoverTime] = useState<number>(0);
  const [isDraggingScale, setIsDraggingScale] = useState(false);

  // 剪辑模式相关状态
  const [isDraggingClipStart, setIsDraggingClipStart] = useState(false);
  const [isDraggingClipEnd, setIsDraggingClipEnd] = useState(false);

  const progressBarRef = useRef<HTMLDivElement>(null);
  const scaleSliderRef = useRef<HTMLDivElement>(null);
  const scaleSliderRef2 = useRef<HTMLDivElement>(null); // 非紧凑模式的缩放滑块
  const zoomControlRef = useRef<HTMLDivElement>(null); // 缩放控制容器
  const zoomControlRef2 = useRef<HTMLDivElement>(null); // 非紧凑模式的缩放控制容器

  // 格式化时间显示
  const formatTime = useCallback((time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // 计算时间对应的位置百分比
  const getPositionPercent = useCallback((time: number) => {
    return duration > 0 ? (time / duration) * 100 : 0;
  }, [duration]);

  // 根据鼠标位置计算时间
  const getTimeFromPosition = useCallback((clientX: number) => {
    if (!progressBarRef.current) return 0;
    const rect = progressBarRef.current.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    return percent * duration;
  }, [duration]);

  // 进度条事件处理
  const handleProgressMouseDown = useCallback((e: React.MouseEvent) => {
    if (!progressBarRef.current) return;
    e.preventDefault();

    const newTime = getTimeFromPosition(e.clientX);

    if (isClipMode) {
      // 剪辑模式下，点击轨道跳转播放位置，但不开始拖拽
      onSeek(Math.max(0, Math.min(duration, newTime)));
    } else {
      // 播放模式下，开始拖拽进度条
      setIsDraggingProgress(true);
      onSeek(Math.max(0, Math.min(duration, newTime)));
    }
  }, [duration, onSeek, isClipMode, getTimeFromPosition]);

  const handleProgressMouseMove = useCallback((e: React.MouseEvent) => {
    if (!progressBarRef.current) return;
    const rect = progressBarRef.current.getBoundingClientRect();
    const percent = (e.clientX - rect.left) / rect.width;
    const newTime = percent * duration;
    setHoverTime(Math.max(0, Math.min(duration, newTime)));

    if (isDraggingProgress) {
      onSeek(Math.max(0, Math.min(duration, newTime)));
    }
  }, [duration, isDraggingProgress, onSeek]);

  const handleProgressMouseUp = useCallback(() => {
    setIsDraggingProgress(false);
  }, []);

  // 剪辑开始时间拖拽处理
  const handleClipStartMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingClipStart(true);
    // 开始拖动时立即跳转到开始时间位置
    onSeek(clipStartTime);
  }, [clipStartTime, onSeek]);

  // 剪辑结束时间拖拽处理
  const handleClipEndMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingClipEnd(true);
    // 开始拖动时立即跳转到结束时间位置
    onSeek(clipEndTime);
  }, [clipEndTime, onSeek]);

  // 全局鼠标事件处理（用于拖动时鼠标移出元素的情况）
  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (!progressBarRef.current) return;

    const newTime = getTimeFromPosition(e.clientX);

    if (isDraggingProgress) {
      onSeek(Math.max(0, Math.min(duration, newTime)));
    } else if (isDraggingClipStart) {
      const clampedTime = Math.max(0, Math.min(clipEndTime - 0.1, newTime));
      onClipTimeChange?.(clampedTime, clipEndTime);
      // 剪辑模式下拖动开始时间时，同步视频播放进度
      onSeek(clampedTime);
    } else if (isDraggingClipEnd) {
      const clampedTime = Math.max(clipStartTime + 0.1, Math.min(duration, newTime));
      onClipTimeChange?.(clipStartTime, clampedTime);
      // 剪辑模式下拖动结束时间时，同步视频播放进度
      onSeek(clampedTime);
    }
  }, [isDraggingProgress, isDraggingClipStart, isDraggingClipEnd, duration, onSeek, getTimeFromPosition, clipStartTime, clipEndTime, onClipTimeChange]);

  const handleGlobalMouseUp = useCallback(() => {
    setIsDraggingProgress(false);
    setIsDraggingClipStart(false);
    setIsDraggingClipEnd(false);
  }, []);

  // 缩放滑块事件处理
  const handleScaleMouseDown = useCallback((e: React.MouseEvent, sliderRef: React.RefObject<HTMLDivElement | null>) => {
    if (!sliderRef.current || !onScaleChange) return;
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingScale(true);

    // 立即更新位置，提高响应性
    const rect = sliderRef.current.getBoundingClientRect();
    const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));
    const percentage = 1 - (y / rect.height); // 反转，顶部是最大值
    const newScale = 0.5 + (percentage * 4.5); // 0.5 到 5
    onScaleChange(Math.max(0.5, Math.min(5, newScale)));
  }, [onScaleChange]);

  // 全局缩放滑块拖动处理
  const handleGlobalScaleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDraggingScale || !onScaleChange) return;

    // 检查哪个滑块正在被拖动
    const activeSlider = scaleSliderRef.current || scaleSliderRef2.current;
    if (!activeSlider) return;

    const rect = activeSlider.getBoundingClientRect();
    const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));
    const percentage = 1 - (y / rect.height); // 反转，顶部是最大值
    const newScale = 0.5 + (percentage * 4.5); // 0.5 到 5
    onScaleChange(Math.max(0.5, Math.min(5, newScale)));
  }, [isDraggingScale, onScaleChange]);

  const handleGlobalScaleMouseUp = useCallback(() => {
    setIsDraggingScale(false);
  }, []);

  const handleProgressMouseEnter = useCallback(() => {
    setIsHoveringProgress(true);
  }, []);

  const handleProgressMouseLeave = useCallback(() => {
    setIsHoveringProgress(false);
    if (!isDraggingProgress) {
      setIsDraggingProgress(false);
    }
  }, [isDraggingProgress]);

  // 添加/移除全局事件监听器
  useEffect(() => {
    if (isDraggingProgress || isDraggingClipStart || isDraggingClipEnd) {
      document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false });
      document.addEventListener('mouseup', handleGlobalMouseUp, { passive: false });

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDraggingProgress, isDraggingClipStart, isDraggingClipEnd, handleGlobalMouseMove, handleGlobalMouseUp]);

  // 缩放滑块全局事件监听器
  useEffect(() => {
    if (isDraggingScale) {
      document.addEventListener('mousemove', handleGlobalScaleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleGlobalScaleMouseUp, { passive: false });

      return () => {
        document.removeEventListener('mousemove', handleGlobalScaleMouseMove);
        document.removeEventListener('mouseup', handleGlobalScaleMouseUp);
      };
    }
  }, [isDraggingScale, handleGlobalScaleMouseMove, handleGlobalScaleMouseUp]);

  // 点击外部区域隐藏缩放控制
  useEffect(() => {
    if (!showZoomControls || !onToggleZoom) return;

    const handleClickOutside = (event: MouseEvent) => {
      // 只处理鼠标点击事件，忽略键盘触发的事件
      if (event.detail === 0) return; // detail为0表示是键盘触发的事件

      const target = event.target as Node;
      const isInsideZoomControl1 = zoomControlRef.current?.contains(target);
      const isInsideZoomControl2 = zoomControlRef2.current?.contains(target);

      if (!isInsideZoomControl1 && !isInsideZoomControl2) {
        onToggleZoom();
      }
    };

    // 延迟添加事件监听器，避免立即触发
    const timeoutId = setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showZoomControls, onToggleZoom]);

  // ESC键隐藏缩放控制 - 使用捕获阶段确保优先级
  useEffect(() => {
    if (!showZoomControls || !onToggleZoom) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // 只处理ESC键，其他键盘事件不拦截
      if (event.code === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        onToggleZoom();
      }
      // 不处理其他键，让它们正常冒泡到其他事件处理器
    };

    // 使用捕获阶段，但只处理ESC键
    document.addEventListener('keydown', handleKeyDown, true);

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [showZoomControls, onToggleZoom]);

  // 解构多视频扩展配置
  const {
    showProgressBar = true,
    showTimeDisplay = true,
    customButtons
  } = multiVideoExtensions;

  // 视频显示控制菜单状态
  const [isVideoMenuOpen, setIsVideoMenuOpen] = useState(false);
  const videoMenuRef = useRef<HTMLDivElement>(null);
  const videoButtonRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取摄像头信息的函数 - 与文件列表保持一致
  const getCameraInfo = useCallback((fileName: string) => {
    const lastChar = fileName.charAt(fileName.lastIndexOf('.') - 1).toUpperCase();
    switch (lastChar) {
      case 'F': return {
        label: '前',
        bgColor: '#0d9488' // 深青色 - 前置摄像头，与文件列表一致
      };
      case 'R': return {
        label: '后',
        bgColor: '#ea580c' // 橙色 - 后置摄像头，与文件列表一致
      };
      case 'I': return {
        label: '内',
        bgColor: '#6b7280' // 灰色 - 内置摄像头，与文件列表一致
      };
      default: return {
        label: '',
        bgColor: '#6b7280'
      };
    }
  }, []);

  // 处理鼠标悬停事件
  const handleMouseEnter = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setIsVideoMenuOpen(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsVideoMenuOpen(false);
    }, 200); // 200ms 延迟，避免鼠标快速移动时闪烁
  }, []);

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isVideoMenuOpen &&
        videoMenuRef.current &&
        videoButtonRef.current &&
        !videoMenuRef.current.contains(event.target as Node) &&
        !videoButtonRef.current.contains(event.target as Node)
      ) {
        setIsVideoMenuOpen(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVideoMenuOpen) {
        setIsVideoMenuOpen(false);
      }
    };

    if (isVideoMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isVideoMenuOpen]);

  return (
    <Box
      bg="rgba(0,0,0,0.8)"
      p={compact ? 2 : 3}
      backdropFilter="blur(4px)"
      borderRadius="md"
      minWidth={compact ? "400px" : "auto"}
      width={isMultiVideo && !compact ? "100%" : "auto"}
    >
      {compact ? (
        // 紧凑模式：两行布局
        <Stack gap={2}>
          {/* 第一行：进度条 */}
          {(!isMultiVideo || showProgressBar) && (
            <Box
              ref={progressBarRef}
              position="relative"
              height={isClipMode ? "8px" : "4px"}
              bg="rgba(255,255,255,0.4)"
              borderRadius="3px"
              cursor={isClipMode ? "default" : "pointer"}
              onMouseDown={handleProgressMouseDown}
              onMouseMove={handleProgressMouseMove}
              onMouseUp={handleProgressMouseUp}
              onMouseEnter={handleProgressMouseEnter}
              onMouseLeave={handleProgressMouseLeave}
              _hover={{
                height: isClipMode ? "8px" : "6px",
                '& .progress-thumb': {
                  opacity: 1,
                  transform: 'translate(-50%, -50%) scale(1.2)'
                }
              }}
              transition="height 0.15s ease"
            >
              {/* 剪辑模式：选中区域 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  height="100%"
                  bg="blue.400"
                  borderRadius="3px"
                  left={`${getPositionPercent(clipStartTime)}%`}
                  width={`${getPositionPercent(clipEndTime - clipStartTime)}%`}
                  boxShadow="0 0 8px rgba(59, 130, 246, 0.6)"
                  zIndex={1}
                />
              )}

              {/* 播放模式：进度条填充 */}
              {!isClipMode && (
                <Box
                  position="absolute"
                  height="100%"
                  bg="teal.400"
                  borderRadius="3px"
                  width={`${duration > 0 ? (currentTime / duration) * 100 : 0}%`}
                  transition={isDraggingProgress ? "none" : "width 0.1s ease"}
                  boxShadow="0 0 8px rgba(56, 178, 172, 0.6)"
                />
              )}

              {/* 当前播放位置 */}
              <Box
                position="absolute"
                top="0"
                bottom="0"
                left={`${getPositionPercent(currentTime)}%`}
                width="2px"
                bg="red.500"
                transform="translateX(-50%)"
                zIndex={15}
              />

              {/* 剪辑模式：开始时间拖拽点 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  top="50%"
                  left={`${getPositionPercent(clipStartTime)}%`}
                  transform="translate(-50%, -50%)"
                  width="16px"
                  height="16px"
                  bg="blue.500"
                  borderRadius="50%"
                  border="2px solid white"
                  cursor="grab"
                  onMouseDown={handleClipStartMouseDown}
                  _active={{ cursor: 'grabbing' }}
                  boxShadow="0 2px 4px rgba(0,0,0,0.3)"
                  zIndex={20}
                />
              )}

              {/* 剪辑模式：结束时间拖拽点 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  top="50%"
                  left={`${getPositionPercent(clipEndTime)}%`}
                  transform="translate(-50%, -50%)"
                  width="16px"
                  height="16px"
                  bg="blue.500"
                  borderRadius="50%"
                  border="2px solid white"
                  cursor="grab"
                  onMouseDown={handleClipEndMouseDown}
                  _active={{ cursor: 'grabbing' }}
                  boxShadow="0 2px 4px rgba(0,0,0,0.3)"
                  zIndex={20}
                />
              )}

              {/* 播放模式：进度条拖拽点 */}
              {!isClipMode && (
                <Box
                  className="progress-thumb"
                  position="absolute"
                  top="50%"
                  left={`${duration > 0 ? (currentTime / duration) * 100 : 0}%`}
                  transform="translate(-50%, -50%)"
                  width="12px"
                  height="12px"
                  bg="white"
                  borderRadius="50%"
                  boxShadow="0 2px 6px rgba(0,0,0,0.25), 0 0 0 2px teal.500"
                  opacity={isDraggingProgress || isHoveringProgress ? 1 : 0}
                  transition={isDraggingProgress ? "opacity 0.1s ease" : "all 0.2s ease"}
                  cursor={isDraggingProgress ? 'grabbing' : 'grab'}
                  zIndex={10}
                />
              )}

              {/* 悬停时间提示 */}
              {isHoveringProgress && !isDraggingProgress && !isDraggingClipStart && !isDraggingClipEnd && (
                <Box
                  position="absolute"
                  bottom="20px"
                  left={`${duration > 0 ? (hoverTime / duration) * 100 : 0}%`}
                  transform="translateX(-50%)"
                  bg="rgba(0,0,0,0.9)"
                  color="white"
                  px={2}
                  py={1}
                  borderRadius="md"
                  fontSize="xs"
                  whiteSpace="nowrap"
                  zIndex={30}
                >
                  {formatTime(hoverTime)}
                </Box>
              )}
            </Box>
          )}

          {/* 第二行：控制按钮 */}
          <Flex alignItems="center" justifyContent="space-between">
            {/* 左侧控制按钮 */}
            <Stack direction="row" align="center" gap={2} flexShrink={0}>
              {/* 返回多视频模式按钮 */}
              {showReturnToMultiVideo && onReturnToMultiVideo && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  onClick={onReturnToMultiVideo}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  title="返回多视频播放"
                >
                  返回
                </Button>
              )}

              {/* 快退按钮 */}
              {showSkipButtons && onSkipTime && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  onClick={() => onSkipTime(-10)}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                >
                  <FiSkipBack />
                </Button>
              )}

              {/* 播放/暂停按钮 */}
              <Button
                size="md"
                variant="ghost"
                color="white"
                onClick={onTogglePlay}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
                loading={isLoading}
              >
                {isPlaying ? <FiPause /> : <FiPlay />}
              </Button>

              {/* 快进按钮 */}
              {showSkipButtons && onSkipTime && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  onClick={() => onSkipTime(10)}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                >
                  <FiSkipForward />
                </Button>
              )}
            </Stack>

            {/* 时间显示 */}
            {(!isMultiVideo || showTimeDisplay) && (
              <Text
                color="white"
                fontSize="sm"
                minW="120px"
                textAlign="center"
              >
                {isClipMode ? (
                  `剪辑: ${formatTime(clipEndTime - clipStartTime)}`
                ) : (
                  `${formatTime(currentTime)} / ${formatTime(duration)}`
                )}
              </Text>
            )}

            {/* 右侧控制按钮 */}
            <Stack direction="row" align="center" gap={2} flexShrink={0}>
              {/* 视频数量显示和控制菜单 */}
              {videoCount > 1 && (
                <Box
                  position="relative"
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                >
                  <Badge
                    ref={videoButtonRef}
                    colorPalette="blue"
                    variant="solid"
                    cursor="pointer"
                    _hover={{ bg: "blue.600" }}
                    title="悬停管理视频显示"
                  >
                    {videoCount} 个视频
                  </Badge>

                  {/* 下拉菜单 */}
                  {isVideoMenuOpen && multiVideoFiles.length > 1 && onToggleVideoVisibility && (
                    <Box
                      ref={videoMenuRef}
                      position="absolute"
                      bottom="100%"
                      right="0"
                      mb={2}
                      bg="blackAlpha.900"
                      backdropFilter="blur(8px)"
                      borderRadius="md"
                      border="1px solid"
                      borderColor="whiteAlpha.200"
                      p={3}
                      minW="200px"
                      maxW="250px"
                      zIndex={9999}
                      boxShadow="lg"
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                    >
                        <VStack align="stretch" gap={2}>
                          <Text fontSize="sm" fontWeight="bold" color="white" borderBottom="1px solid" borderColor="whiteAlpha.200" pb={2}>
                            视频显示控制
                          </Text>

                          {multiVideoFiles.map((file, index) => {
                            const cameraInfo = getCameraInfo(file.name);
                            const isVisible = videoVisibility[index];

                            return (
                              <Button
                                key={file.id}
                                size="sm"
                                variant="ghost"
                                color="white"
                                onClick={() => {
                                  onToggleVideoVisibility(index);
                                  setIsVideoMenuOpen(false);
                                }}
                                _hover={{ bg: "whiteAlpha.100" }}
                                justifyContent="flex-start"
                                w="100%"
                                fontWeight="normal"
                                display="flex"
                                alignItems="center"
                                gap={2}
                              >
                                <Box
                                  width="20px"
                                  height="20px"
                                  borderRadius="sm"
                                  bg={isVisible ? cameraInfo.bgColor : "gray.600"}
                                  color="white"
                                  fontSize="xs"
                                  fontWeight="bold"
                                  display="flex"
                                  alignItems="center"
                                  justifyContent="center"
                                  opacity={isVisible ? 1 : 0.6}
                                  border={isVisible ? "none" : "1px solid rgba(255,255,255,0.3)"}
                                >
                                  {cameraInfo.label}
                                </Box>
                                <Text flex={1} textAlign="left">
                                  {cameraInfo.label === 'F' ? '前置摄像头' :
                                   cameraInfo.label === 'R' ? '后置摄像头' :
                                   cameraInfo.label === 'L' ? '左侧摄像头' :
                                   cameraInfo.label === 'I' ? '内置摄像头' : '未知摄像头'}
                                </Text>
                                <Text fontSize="xs" color={isVisible ? "green.300" : "red.300"}>
                                  {isVisible ? '点击隐藏' : '点击显示'}
                                </Text>
                              </Button>
                            );
                          })}

                          {/* 还原所有视频按钮 */}
                          {videoVisibility.some(visible => !visible) && (
                            <>
                              <Box height="1px" bg="whiteAlpha.200" my={1} />
                              <Button
                                size="sm"
                                variant="ghost"
                                color="blue.300"
                                onClick={() => {
                                  // 显示所有视频
                                  multiVideoFiles.forEach((_, index) => {
                                    if (!videoVisibility[index]) {
                                      onToggleVideoVisibility(index);
                                    }
                                  });
                                  setIsVideoMenuOpen(false);
                                }}
                                _hover={{ bg: "whiteAlpha.100" }}
                                justifyContent="center"
                                w="100%"
                                fontWeight="normal"
                              >
                                还原所有视频
                              </Button>
                            </>
                          )}
                        </VStack>
                      </Box>
                    )}
                  </Box>
              )}

              {/* 音量控制 */}
              {showVolumeControl && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  onClick={onToggleMute}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                >
                  {isMuted ? <FiVolumeX /> : <FiVolume2 />}
                </Button>
              )}

              {/* 缩放控制 */}
              {onToggleZoom && (
                <Box position="relative" ref={zoomControlRef}>
                  <Button
                    size="sm"
                    variant="ghost"
                    color={showZoomControls ? "blue.300" : "white"}
                    onClick={(e) => {
                      onToggleZoom();
                      // 移除焦点，防止空格键触发按钮点击
                      e.currentTarget.blur();
                    }}
                    onKeyDown={(e) => {
                      // 阻止空格键在按钮上的默认行为
                      if (e.code === 'Space') {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    _hover={{ bg: "rgba(255,255,255,0.2)" }}
                    title="视频缩放"
                  >
                    <FiZoomIn />
                  </Button>

                  {/* 竖直缩放滑块 */}
                  {showZoomControls && (
                    <Box
                      position="absolute"
                      bottom="100%"
                      left="50%"
                      transform="translateX(-50%)"
                      mb={2}
                      bg="blackAlpha.800"
                      p={3}
                      borderRadius="md"
                      backdropFilter="blur(4px)"
                      minW="60px"
                      display="flex"
                      flexDirection="column"
                      alignItems="center"
                      gap={2}
                    >
                      {/* 缩放百分比显示 */}
                      <Text color="white" fontSize="xs" textAlign="center">
                        {(scale * 100).toFixed(0)}%
                      </Text>

                      {/* 竖直滑块 */}
                      <Box
                        ref={scaleSliderRef}
                        height="100px"
                        width="4px"
                        position="relative"
                        bg="whiteAlpha.400"
                        borderRadius="full"
                        cursor={isDraggingScale ? "grabbing" : "pointer"}
                        _hover={{ bg: "whiteAlpha.600" }}
                        transition="background-color 0.2s"
                        onMouseDown={(e) => handleScaleMouseDown(e, scaleSliderRef)}
                        onClick={(e) => {
                          if (isDraggingScale) return; // 如果正在拖动，不处理点击
                          if (!onScaleChange) return;
                          const rect = e.currentTarget.getBoundingClientRect();
                          const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));
                          const percentage = 1 - (y / rect.height); // 反转，顶部是最大值
                          const newScale = 0.5 + (percentage * 4.5); // 0.5 到 5
                          onScaleChange(Math.max(0.5, Math.min(5, newScale)));
                        }}
                      >
                        {/* 滑块轨道填充 */}
                        <Box
                          position="absolute"
                          bottom={0}
                          left={0}
                          right={0}
                          height={`${((scale - 0.5) / 4.5) * 100}%`}
                          bg="blue.400"
                          borderRadius="full"
                        />

                        {/* 滑块手柄 */}
                        <Box
                          position="absolute"
                          bottom={`${((scale - 0.5) / 4.5) * 100}%`}
                          left="50%"
                          transform="translate(-50%, 50%)"
                          width={isDraggingScale ? "14px" : "12px"}
                          height={isDraggingScale ? "14px" : "12px"}
                          bg="white"
                          borderRadius="full"
                          border="2px solid #3182ce"
                          boxShadow={isDraggingScale ? "0 4px 12px rgba(49,130,206,0.4)" : "0 2px 8px rgba(49,130,206,0.3)"}
                          cursor={isDraggingScale ? "grabbing" : "grab"}
                          transition="all 0.15s ease"
                          pointerEvents="none" // 防止手柄阻止滑块的鼠标事件
                          _hover={{ transform: "translate(-50%, 50%) scale(1.1)" }}
                        />
                      </Box>

                      {/* 重置按钮 */}
                      <Button
                        size="xs"
                        variant="ghost"
                        color="white"
                        onClick={onResetZoom}
                        _hover={{ bg: "whiteAlpha.200" }}
                        title="重置缩放"
                      >
                        1:1
                      </Button>
                    </Box>
                  )}
                </Box>
              )}

              {/* 剪辑控制 - 只有当有可见视频时才显示 */}
              {showClipControls && videoPath && (!isMultiVideo || videoVisibility.some(visible => visible)) && (
                <>
                  {/* 截图功能 */}
                  <ClipControls
                    videoPath={videoPath}
                    currentTime={currentTime}
                    onScreenshotTaken={onScreenshotTaken}
                    isMultiVideo={isMultiVideo}
                    onCaptureFrame={isMultiVideo ? onCaptureFrame : undefined}
                    videoFiles={videoFiles}
                    videoVisibility={videoVisibility}
                  />

                  {/* 剪辑模式切换按钮 */}
                  <Button
                    size="sm"
                    variant="ghost"
                    color={isClipMode ? "blue.300" : "white"}
                    onClick={onToggleClipMode}
                    title={isClipMode ? "退出剪辑模式" : "进入剪辑模式"}
                    _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  >
                    <FiScissors />
                  </Button>

                  {/* 剪辑模式下的保存按钮 */}
                  {isClipMode && (
                    <Button
                      size="sm"
                      variant="solid"
                      colorPalette="blue"
                      onClick={onSaveClip}
                      title="保存剪辑"
                    >
                      保存
                    </Button>
                  )}
                </>
              )}

              {/* 全屏按钮 */}
              {showFullscreenButton && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  onClick={() => {
                    console.log('Fullscreen button clicked in VideoPlayerControls');
                    onToggleFullscreen();
                  }}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  title={isFullscreen ? "退出全屏" : "全屏"}
                >
                  {isFullscreen ? <FiMinimize /> : <FiMaximize />}
                </Button>
              )}
            </Stack>
          </Flex>
        </Stack>
      ) : (
        // 正常模式：单行布局
        <Flex alignItems="center" gap={3}>
          {/* 左侧控制按钮 */}
          <Stack direction="row" align="center" gap={2} flexShrink={0}>
            {/* 返回多视频模式按钮 */}
            {showReturnToMultiVideo && onReturnToMultiVideo && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={onReturnToMultiVideo}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
                title="返回多视频播放"
              >
                返回
              </Button>
            )}

            {/* 快退按钮 */}
            {showSkipButtons && onSkipTime && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={() => onSkipTime(-10)}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
              >
                <FiSkipBack />
              </Button>
            )}

            {/* 播放/暂停按钮 */}
            <Button
              size="md"
              variant="ghost"
              color="white"
              onClick={onTogglePlay}
              _hover={{ bg: "rgba(255,255,255,0.2)" }}
              loading={isLoading}
            >
              {isPlaying ? <FiPause /> : <FiPlay />}
            </Button>

            {/* 快进按钮 */}
            {showSkipButtons && onSkipTime && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={() => onSkipTime(10)}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
              >
                <FiSkipForward />
              </Button>
            )}
          </Stack>

          {/* 中间进度条 - 根据配置显示 */}
          {(!isMultiVideo || showProgressBar) && (
            <Box
              ref={progressBarRef}
              position="relative"
              height={isClipMode ? "10px" : "6px"}
              bg="rgba(255,255,255,0.4)"
              borderRadius="3px"
              cursor={isClipMode ? "default" : "pointer"}
              flex={1}
              onMouseDown={handleProgressMouseDown}
              onMouseMove={handleProgressMouseMove}
              onMouseUp={handleProgressMouseUp}
              onMouseEnter={handleProgressMouseEnter}
              onMouseLeave={handleProgressMouseLeave}
              _hover={{
                height: isClipMode ? "10px" : "8px",
                '& .progress-thumb': {
                  opacity: 1,
                  transform: 'translate(-50%, -50%) scale(1.2)'
                }
              }}
              transition="height 0.15s ease"
              minWidth="200px"
            >
              {/* 剪辑模式：选中区域 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  height="100%"
                  bg="blue.400"
                  borderRadius="3px"
                  left={`${getPositionPercent(clipStartTime)}%`}
                  width={`${getPositionPercent(clipEndTime - clipStartTime)}%`}
                  boxShadow="0 0 8px rgba(59, 130, 246, 0.6)"
                  zIndex={1}
                />
              )}

              {/* 播放模式：进度条填充 */}
              {!isClipMode && (
                <Box
                  position="absolute"
                  height="100%"
                  bg="teal.400"
                  borderRadius="3px"
                  width={`${duration > 0 ? (currentTime / duration) * 100 : 0}%`}
                  transition={isDraggingProgress ? "none" : "width 0.1s ease"}
                  boxShadow="0 0 8px rgba(56, 178, 172, 0.6)"
                />
              )}

              {/* 当前播放位置 */}
              <Box
                position="absolute"
                top="0"
                bottom="0"
                left={`${getPositionPercent(currentTime)}%`}
                width="2px"
                bg="red.500"
                transform="translateX(-50%)"
                zIndex={15}
              />

              {/* 剪辑模式：开始时间拖拽点 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  top="50%"
                  left={`${getPositionPercent(clipStartTime)}%`}
                  transform="translate(-50%, -50%)"
                  width="18px"
                  height="18px"
                  bg="blue.500"
                  borderRadius="50%"
                  border="2px solid white"
                  cursor="grab"
                  onMouseDown={handleClipStartMouseDown}
                  _active={{ cursor: 'grabbing' }}
                  boxShadow="0 2px 4px rgba(0,0,0,0.3)"
                  zIndex={20}
                />
              )}

              {/* 剪辑模式：结束时间拖拽点 */}
              {isClipMode && (
                <Box
                  position="absolute"
                  top="50%"
                  left={`${getPositionPercent(clipEndTime)}%`}
                  transform="translate(-50%, -50%)"
                  width="18px"
                  height="18px"
                  bg="blue.500"
                  borderRadius="50%"
                  border="2px solid white"
                  cursor="grab"
                  onMouseDown={handleClipEndMouseDown}
                  _active={{ cursor: 'grabbing' }}
                  boxShadow="0 2px 4px rgba(0,0,0,0.3)"
                  zIndex={20}
                />
              )}

              {/* 播放模式：进度条拖拽点 */}
              {!isClipMode && (
                <Box
                  className="progress-thumb"
                  position="absolute"
                  top="50%"
                  left={`${duration > 0 ? (currentTime / duration) * 100 : 0}%`}
                  transform="translate(-50%, -50%)"
                  width="14px"
                  height="14px"
                  bg="white"
                  borderRadius="50%"
                  boxShadow="0 2px 6px rgba(0,0,0,0.25), 0 0 0 2px teal.500"
                  opacity={isDraggingProgress || isHoveringProgress ? 1 : 0}
                  transition={isDraggingProgress ? "opacity 0.1s ease" : "all 0.2s ease"}
                  cursor={isDraggingProgress ? 'grabbing' : 'grab'}
                  zIndex={10}
                />
              )}

              {/* 悬停时间提示 */}
              {isHoveringProgress && !isDraggingProgress && !isDraggingClipStart && !isDraggingClipEnd && (
                <Box
                  position="absolute"
                  bottom="20px"
                  left={`${duration > 0 ? (hoverTime / duration) * 100 : 0}%`}
                  transform="translateX(-50%)"
                  bg="rgba(0,0,0,0.9)"
                  color="white"
                  px={2}
                  py={1}
                  borderRadius="md"
                  fontSize="xs"
                  whiteSpace="nowrap"
                  zIndex={30}
                >
                  {formatTime(hoverTime)}
                </Box>
              )}
            </Box>
          )}

          {/* 时间显示 - 根据配置显示 */}
          {(!isMultiVideo || showTimeDisplay) && (
            <Text
              color="white"
              fontSize="sm"
              minW="120px"
              fontFamily="mono"
              flexShrink={0}
            >
              {isClipMode ? (
                `剪辑: ${formatTime(clipEndTime - clipStartTime)}`
              ) : (
                `${formatTime(currentTime)} / ${formatTime(duration)}`
              )}
            </Text>
          )}

          {/* 右侧控制按钮 */}
          <Stack direction="row" align="center" gap={2} flexShrink={0}>
            {/* 视频数量显示和控制菜单 - 多视频时始终显示 */}
            {videoCount > 1 && (
              <Box
                position="relative"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <Badge
                  ref={videoButtonRef}
                  colorPalette="blue"
                  variant="solid"
                  cursor="pointer"
                  _hover={{ bg: "blue.600" }}
                  title="悬停管理视频显示"
                >
                  {videoCount} 个视频
                </Badge>

                {/* 下拉菜单 */}
                {isVideoMenuOpen && multiVideoFiles.length > 1 && onToggleVideoVisibility && (
                  <Box
                    ref={videoMenuRef}
                    position="absolute"
                    bottom="100%"
                    right="0"
                    mb={2}
                    bg="blackAlpha.900"
                    backdropFilter="blur(8px)"
                    borderRadius="md"
                    border="1px solid"
                    borderColor="whiteAlpha.200"
                    p={3}
                    minW="200px"
                    maxW="250px"
                    zIndex={9999}
                    boxShadow="lg"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                  >
                      <VStack align="stretch" gap={2}>
                        {multiVideoFiles.map((file, index) => {
                          const cameraInfo = getCameraInfo(file.name);
                          const isVisible = videoVisibility[index];

                          return (
                            <Button
                              key={file.id}
                              size="sm"
                              variant="ghost"
                              color="white"
                              onClick={() => onToggleVideoVisibility(index)}
                              _hover={{ bg: "whiteAlpha.100" }}
                              justifyContent="flex-start"
                              w="100%"
                              fontWeight="normal"
                              display="flex"
                              alignItems="center"
                              gap={2}
                            >
                              <Box
                                width="20px"
                                height="20px"
                                borderRadius="sm"
                                bg={cameraInfo.bgColor}
                                color="white"
                                fontSize="xs"
                                fontWeight="bold"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                opacity={isVisible ? 1 : 0.6}
                                border={isVisible ? "none" : "1px solid rgba(255,255,255,0.3)"}
                              >
                                {cameraInfo.label}
                              </Box>
                              <Text flex={1} textAlign="left">
                                {cameraInfo.label === '前' ? '前置摄像头' :
                                 cameraInfo.label === '后' ? '后置摄像头' :
                                 cameraInfo.label === '内' ? '内置摄像头' : '未知摄像头'}
                              </Text>
                              <Text fontSize="xs" color={isVisible ? "green.300" : "red.300"}>
                                {isVisible ? '隐藏' : '显示'}
                              </Text>
                            </Button>
                          );
                        })}

                        {/* 还原所有视频按钮 */}
                        {videoVisibility.some(visible => !visible) && (
                          <>
                            <Box height="1px" bg="whiteAlpha.200" my={1} />
                            <Button
                              size="sm"
                              variant="ghost"
                              color="blue.300"
                              onClick={() => {
                                // 显示所有视频
                                multiVideoFiles.forEach((_, index) => {
                                  if (!videoVisibility[index]) {
                                    onToggleVideoVisibility(index);
                                  }
                                });
                              }}
                              _hover={{ bg: "whiteAlpha.100" }}
                              justifyContent="center"
                              w="100%"
                              fontWeight="normal"
                            >
                              还原所有视频
                            </Button>
                          </>
                        )}
                      </VStack>
                    </Box>
                  )}
                </Box>
            )}

            {/* 音量控制 */}
            {showVolumeControl && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={onToggleMute}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
              >
                {isMuted ? <FiVolumeX /> : <FiVolume2 />}
              </Button>
            )}

            {/* 角度控制按钮 */}
            {availableAngles.length > 1 && onToggleAngleControls && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={onToggleAngleControls}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
                opacity={showAngleControls ? 1 : 0.5}
              >
                角度控制
              </Button>
            )}

            {/* 多视频模式下的自定义按钮 */}
            {isMultiVideo && customButtons && customButtons}

            {/* 缩放控制 */}
            {onToggleZoom && (
              <Box position="relative" ref={zoomControlRef2}>
                <Button
                  size="sm"
                  variant="ghost"
                  color={showZoomControls ? "blue.300" : "white"}
                  onClick={(e) => {
                    onToggleZoom();
                    // 移除焦点，防止空格键触发按钮点击
                    e.currentTarget.blur();
                  }}
                  onKeyDown={(e) => {
                    // 阻止空格键在按钮上的默认行为
                    if (e.code === 'Space') {
                      e.preventDefault();
                      e.stopPropagation();
                    }
                  }}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  title="视频缩放"
                >
                  <FiZoomIn />
                </Button>

                {/* 竖直缩放滑块 */}
                {showZoomControls && (
                  <Box
                    position="absolute"
                    bottom="100%"
                    left="50%"
                    transform="translateX(-50%)"
                    mb={2}
                    bg="blackAlpha.800"
                    p={3}
                    borderRadius="md"
                    backdropFilter="blur(4px)"
                    minW="60px"
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    gap={2}
                  >
                    {/* 缩放百分比显示 */}
                    <Text color="white" fontSize="xs" textAlign="center">
                      {(scale * 100).toFixed(0)}%
                    </Text>

                    {/* 竖直滑块 */}
                    <Box
                      ref={scaleSliderRef2}
                      height="100px"
                      width="4px"
                      position="relative"
                      bg="whiteAlpha.400"
                      borderRadius="full"
                      cursor={isDraggingScale ? "grabbing" : "pointer"}
                      _hover={{ bg: "whiteAlpha.600" }}
                      transition="background-color 0.2s"
                      onMouseDown={(e) => handleScaleMouseDown(e, scaleSliderRef2)}
                      onClick={(e) => {
                        if (isDraggingScale) return; // 如果正在拖动，不处理点击
                        if (!onScaleChange) return;
                        const rect = e.currentTarget.getBoundingClientRect();
                        const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));
                        const percentage = 1 - (y / rect.height); // 反转，顶部是最大值
                        const newScale = 0.5 + (percentage * 4.5); // 0.5 到 5
                        onScaleChange(Math.max(0.5, Math.min(5, newScale)));
                      }}
                    >
                      {/* 滑块轨道填充 */}
                      <Box
                        position="absolute"
                        bottom={0}
                        left={0}
                        right={0}
                        height={`${((scale - 0.5) / 4.5) * 100}%`}
                        bg="blue.400"
                        borderRadius="full"
                      />

                      {/* 滑块手柄 */}
                      <Box
                        position="absolute"
                        bottom={`${((scale - 0.5) / 4.5) * 100}%`}
                        left="50%"
                        transform="translate(-50%, 50%)"
                        width={isDraggingScale ? "14px" : "12px"}
                        height={isDraggingScale ? "14px" : "12px"}
                        bg="white"
                        borderRadius="full"
                        border="2px solid #3182ce"
                        boxShadow={isDraggingScale ? "0 4px 12px rgba(49,130,206,0.4)" : "0 2px 8px rgba(49,130,206,0.3)"}
                        cursor={isDraggingScale ? "grabbing" : "grab"}
                        transition="all 0.15s ease"
                        pointerEvents="none" // 防止手柄阻止滑块的鼠标事件
                        _hover={{ transform: "translate(-50%, 50%) scale(1.1)" }}
                      />
                    </Box>

                    {/* 重置按钮 */}
                    <Button
                      size="xs"
                      variant="ghost"
                      color="white"
                      onClick={onResetZoom}
                      _hover={{ bg: "whiteAlpha.200" }}
                      title="重置缩放"
                    >
                      1:1
                    </Button>
                  </Box>
                )}
              </Box>
            )}

            {/* 剪辑控制 - 统一操作体验，只有当有可见视频时才显示 */}
            {showClipControls && videoPath && (!isMultiVideo || videoVisibility.some(visible => visible)) && (
              <>
                {/* 截图功能 */}
                <ClipControls
                  videoPath={videoPath}
                  currentTime={currentTime}
                  onScreenshotTaken={onScreenshotTaken}
                  isMultiVideo={isMultiVideo}
                  onCaptureFrame={isMultiVideo ? onCaptureFrame : undefined}
                  videoFiles={videoFiles}
                  videoVisibility={videoVisibility}
                />

                {/* 剪辑模式切换按钮 */}
                <Button
                  size="sm"
                  variant="ghost"
                  color={isClipMode ? "blue.300" : "white"}
                  onClick={onToggleClipMode}
                  title={isClipMode ? "退出剪辑模式" : "进入剪辑模式"}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                >
                  <FiScissors />
                </Button>

                {/* 剪辑模式下的保存按钮 */}
                {isClipMode && (
                  <Button
                    size="sm"
                    variant="solid"
                    colorPalette="blue"
                    onClick={onSaveClip}
                    title={`保存${isMultiVideo ? '多视频' : ''}剪辑`}
                  >
                    保存
                  </Button>
                )}
              </>
            )}

            {/* 全屏按钮 */}
            {showFullscreenButton && (
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={() => {
                  console.log('Fullscreen button clicked in VideoPlayerControls (compact mode)');
                  onToggleFullscreen();
                }}
                _hover={{ bg: "rgba(255,255,255,0.2)" }}
                title={isFullscreen ? "退出全屏" : "全屏"}
              >
                {isFullscreen ? <FiMinimize /> : <FiMaximize />}
              </Button>
            )}
          </Stack>
        </Flex>
      )}
    </Box>
  );
};

export default VideoPlayerControls;
