import { Box, Text, Stack, Icon, Grid } from '@chakra-ui/react';
import { useMemo } from 'react';
import { GPSPoint, GPSTrack } from '../types/electron';
import MetricCard from './MetricCard';
import SpeedChart from './SpeedChart';
import {
  FiNavigation,
  FiMapPin,
  FiTarget,
  FiActivity,
  FiMap
} from 'react-icons/fi';

interface GPSInfoPanelProps {
  currentGPSPoint?: GPSPoint;
  gpsTrack?: GPSTrack;
  isExtractingGPS?: boolean;
  currentTime?: number; // 当前播放时间（秒）
  videoDuration?: number; // 视频总时长（秒）
}

const GPSInfoPanel = ({ currentGPSPoint, gpsTrack, isExtractingGPS, currentTime = 0, videoDuration = 60 }: GPSInfoPanelProps) => {
  // 简化格式化函数
  const formatCoordinate = (value: number | undefined): string => {
    if (value === undefined) return '--';
    return value.toFixed(6);
  };

  const formatSpeed = (speed: number | undefined): string => {
    if (speed === undefined) return '--';
    return speed.toFixed(1);
  };

  const formatHeading = (bearing: number | undefined): string => {
    if (bearing === undefined) return '--';
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(bearing / 45) % 8;
    return `${bearing.toFixed(0)}° ${directions[index]}`;
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}`;
    } else {
      return `${(meters / 1000).toFixed(2)}`;
    }
  };

  const formatDistanceUnit = (meters: number): string => {
    return meters < 1000 ? 'm' : 'km';
  };



  // 判断GPS数据是否有效
  const hasValidGPS = useMemo(() => {
    return currentGPSPoint &&
           typeof currentGPSPoint.latitude === 'number' &&
           typeof currentGPSPoint.longitude === 'number' &&
           !isNaN(currentGPSPoint.latitude) &&
           !isNaN(currentGPSPoint.longitude);
  }, [currentGPSPoint]);

  // 获取统计信息
  const stats = gpsTrack?.statistics || {
    totalDistance: gpsTrack?.totalDistance || 0,
    duration: gpsTrack?.duration || 0,
    averageSpeed: gpsTrack?.averageSpeed || 0,
    maxSpeed: gpsTrack?.maxSpeed || 0,
    minSpeed: gpsTrack?.minSpeed || 0,
    validSpeedPoints: 0,
    totalPoints: gpsTrack?.pointCount || gpsTrack?.points?.length || 0
  };

  // 处理加载状态
  if (isExtractingGPS) {
    return (
      <Box p={4} height="100%">
        <Text fontSize="md" fontWeight="semibold" color="gray.700" mb={3}>
          GPS 信息
        </Text>
        <Box textAlign="center" py={8}>
          <Text color="gray.500">正在提取GPS数据...</Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box p={3} height="100%" overflow="auto">
      {hasValidGPS ? (
        <Stack gap={3}>
          {/* 当前位置信息 */}
          <Grid templateColumns="1fr 1fr" gap={2}>
            <MetricCard
              icon={<Icon as={FiMapPin} />}
              label="纬度"
              value={formatCoordinate(currentGPSPoint?.latitude)}
              color="gray.700"
              bgColor="gray.50"
              size="sm"
            />
            <MetricCard
              icon={<Icon as={FiMap} />}
              label="经度"
              value={formatCoordinate(currentGPSPoint?.longitude)}
              color="gray.700"
              bgColor="gray.50"
              size="sm"
            />
          </Grid>

          {/* 运动状态 - 方向、平均速度、总距离 */}
          <Grid templateColumns="1fr 1fr 1fr" gap={2}>
            <MetricCard
              icon={<Icon as={FiNavigation} />}
              label="方向"
              value={formatHeading(currentGPSPoint?.heading)}
              color="purple.600"
              bgColor="purple.50"
              size="sm"
            />
            {gpsTrack && gpsTrack.points && gpsTrack.points.length > 0 && (
              <MetricCard
                icon={<Icon as={FiActivity} />}
                label="平均速度"
                value={formatSpeed(stats.averageSpeed)}
                unit="km/h"
                color="blue.600"
                bgColor="blue.50"
                size="sm"
              />
            )}
            {gpsTrack && gpsTrack.points && gpsTrack.points.length > 0 && (
              <MetricCard
                icon={<Icon as={FiTarget} />}
                label="总距离"
                value={formatDistance(stats.totalDistance)}
                unit={formatDistanceUnit(stats.totalDistance)}
                color="green.600"
                bgColor="green.50"
                size="sm"
              />
            )}
          </Grid>

          {/* 速度趋势图 */}
          {gpsTrack && gpsTrack.points && gpsTrack.points.length > 0 && (
            <Box>
              <SpeedChart
                gpsPoints={gpsTrack.points}
                height={120}
                strokeColor="#3182ce"
                currentTime={currentTime}
                videoDuration={videoDuration}
                gpsTrackDuration={gpsTrack.duration || gpsTrack.statistics?.duration}
              />
            </Box>
          )}
        </Stack>
      ) : (
        <Box mt={3} p={3} bg="gray.50" borderRadius="md">
          <Text fontSize="xs" color="gray.500" textAlign="center">
            当前视频文件无GPS数据
          </Text>
        </Box>
      )}


    </Box>
  );
};

export default GPSInfoPanel;
