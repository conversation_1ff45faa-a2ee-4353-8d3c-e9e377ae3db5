import { Box, Text } from '@chakra-ui/react';
import { LineChart, Line, ResponsiveContainer, ReferenceDot, XAxis, YAxis } from 'recharts';
import { useMemo, useCallback } from 'react';

interface GPSPoint {
  latitude: number;
  longitude: number;
  altitude?: number;
  speed?: number;
  heading?: number;
  timestamp?: string;
  accuracy?: number;
}

interface SpeedChartProps {
  gpsPoints: GPSPoint[];
  height?: number;
  strokeColor?: string;
  currentTime?: number; // 当前播放时间（秒）
  videoDuration?: number; // 视频总时长（秒）
  gpsTrackDuration?: number; // GPS轨迹实际时长（秒）
}

const SpeedChart = ({
  gpsPoints,
  height = 120,
  strokeColor = '#3182ce',
  currentTime = 0,
  videoDuration = 60,
  gpsTrackDuration
}: SpeedChartProps) => {
  // 处理GPS数据，准备图表数据 - 优化以减少重新计算
  const { chartData, minSpeedPoint, maxSpeedPoint } = useMemo(() => {
    if (!gpsPoints || gpsPoints.length === 0) {
      return { chartData: [], minSpeedPoint: null, maxSpeedPoint: null };
    }

    // 过滤有效的速度数据点
    const validPoints = gpsPoints
      .map((point, index) => ({
        index,
        speed: point.speed || 0,
        timestamp: point.timestamp,
        hasValidSpeed: point.speed !== undefined && point.speed !== null && !isNaN(point.speed)
      }))
      .filter(point => point.hasValidSpeed);

    if (validPoints.length === 0) {
      return { chartData: [], minSpeedPoint: null, maxSpeedPoint: null };
    }

    // 转换为图表数据，使用百分比作为X轴
    const chartData = validPoints.map((point, idx) => ({
      time: (idx / (validPoints.length - 1)) * 100, // 转换为0-100的百分比
      speed: Math.round(point.speed * 10) / 10,
      originalIndex: point.index
    }));

    // 找到最快和最慢速度的点
    let minSpeedPoint = chartData[0];
    let maxSpeedPoint = chartData[0];

    chartData.forEach(point => {
      if (point.speed < minSpeedPoint.speed) {
        minSpeedPoint = point;
      }
      if (point.speed > maxSpeedPoint.speed) {
        maxSpeedPoint = point;
      }
    });

    // 确保最快和最慢点不是同一个点（避免重叠）
    if (minSpeedPoint === maxSpeedPoint && chartData.length > 1) {
      // 如果只有一个点或所有点速度相同，找到第二个不同的点
      for (const point of chartData) {
        if (point !== minSpeedPoint) {
          if (point.speed !== minSpeedPoint.speed) {
            if (point.speed > minSpeedPoint.speed) {
              maxSpeedPoint = point;
            } else {
              minSpeedPoint = point;
            }
            break;
          }
        }
      }
    }

    return { chartData, minSpeedPoint, maxSpeedPoint };
  }, [gpsPoints]); // 移除currentTime依赖，避免频繁重新计算

  // 单独计算当前播放位置点，避免整个图表重新渲染
  const currentSpeedPoint = useMemo(() => {
    if (!chartData || chartData.length === 0 || currentTime < 0 || videoDuration <= 0) {
      return null;
    }

    // 使用实际的视频时长计算进度
    const progress = Math.min(currentTime / videoDuration, 1);

    const targetIndex = Math.floor(progress * (chartData.length - 1));
    const clampedIndex = Math.min(Math.max(targetIndex, 0), chartData.length - 1);

    return chartData[clampedIndex];
  }, [chartData, currentTime, videoDuration, gpsTrackDuration]);

  // 如果没有数据，显示提示信息
  if (chartData.length === 0) {
    return (
      <Box
        height={`${height}px`}
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg="gray.50"
        borderRadius="md"
      >
        <Text fontSize="sm" color="gray.500">
          暂无速度数据
        </Text>
      </Box>
    );
  }

  // 使用useCallback优化渲染性能
  const renderChart = useCallback(() => (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{ top: 15, right: 15, left: 15, bottom: 15 }}
      >
        {/* 隐藏的坐标轴 - ReferenceDot需要这些来定位 */}
        <XAxis
          dataKey="time"
          type="number"
          domain={[0, 100]}
          hide
        />
        <YAxis
          type="number"
          domain={['dataMin', 'dataMax']}
          hide
        />

        {/* 主要速度曲线 */}
        <Line
          type="monotone"
          dataKey="speed"
          stroke={strokeColor}
          strokeWidth={2}
          dot={false}
          activeDot={false}
          isAnimationActive={false} // 禁用动画减少抖动
        />

        {/* 最慢速度标记 - 固定点 */}
        {minSpeedPoint && (
          <ReferenceDot
            x={minSpeedPoint.time}
            y={minSpeedPoint.speed}
            r={6}
            fill="#22c55e"
            stroke="white"
            strokeWidth={2}
          />
        )}

        {/* 最快速度标记 - 固定点 */}
        {maxSpeedPoint && (
          <ReferenceDot
            x={maxSpeedPoint.time}
            y={maxSpeedPoint.speed}
            r={6}
            fill="#ef4444"
            stroke="white"
            strokeWidth={2}
          />
        )}

        {/* 当前播放位置标记 - 跟随播放 */}
        {currentSpeedPoint && (
          <ReferenceDot
            x={currentSpeedPoint.time}
            y={currentSpeedPoint.speed}
            r={7}
            fill="#3182ce"
            stroke="white"
            strokeWidth={2}
          />
        )}
      </LineChart>
    </ResponsiveContainer>
  ), [chartData, minSpeedPoint, maxSpeedPoint, currentSpeedPoint, strokeColor]);

  return (
    <Box position="relative">
      {/* 简洁的图表容器 */}
      <Box
        height={`${height}px`}
        bg="gray.50"
        borderRadius="md"
        position="relative"
        overflow="hidden"
      >
        {renderChart()}

        {/* 最慢速度标签 - 固定位置 */}
        {minSpeedPoint && (
          <Box
            position="absolute"
            left={`${Math.min(Math.max(minSpeedPoint.time, 10), 90)}%`}
            top="8px"
            transform="translateX(-50%)"
            bg="rgba(34, 197, 94, 0.9)"
            color="white"
            px={2}
            py={1}
            borderRadius="sm"
            fontSize="10px"
            fontWeight="bold"
            pointerEvents="none"
            whiteSpace="nowrap"
          >
            最慢 {minSpeedPoint.speed} km/h
          </Box>
        )}

        {/* 最快速度标签 - 固定位置 */}
        {maxSpeedPoint && (
          <Box
            position="absolute"
            left={`${Math.min(Math.max(maxSpeedPoint.time, 10), 90)}%`}
            bottom="8px"
            transform="translateX(-50%)"
            bg="rgba(239, 68, 68, 0.9)"
            color="white"
            px={2}
            py={1}
            borderRadius="sm"
            fontSize="10px"
            fontWeight="bold"
            pointerEvents="none"
            whiteSpace="nowrap"
          >
            最快 {maxSpeedPoint.speed} km/h
          </Box>
        )}

        {/* 当前播放位置标签 - 跟随播放 */}
        {currentSpeedPoint && (
          <Box
            position="absolute"
            left={`${Math.min(Math.max(currentSpeedPoint.time, 10), 90)}%`}
            top="50%"
            transform="translate(-50%, -50%)"
            bg="rgba(49, 130, 206, 0.9)"
            color="white"
            px={2}
            py={1}
            borderRadius="sm"
            fontSize="10px"
            fontWeight="bold"
            pointerEvents="none"
            whiteSpace="nowrap"
          >
            {currentSpeedPoint.speed} km/h
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default SpeedChart;
