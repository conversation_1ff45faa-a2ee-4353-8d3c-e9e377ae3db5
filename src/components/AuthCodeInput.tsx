import React, { useState } from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  But<PERSON>,
  Text,
  Container,
  Card,
  PinInput,
  Alert,
  Image
} from '@chakra-ui/react';
// 使用 import 引用 assets 文件夹中的二维码图片
import pismQrCode from '../../assets/pism.jpg';


interface AuthCodeInputProps {
  onAuthSuccess: () => void;
}

const AuthCodeInput: React.FC<AuthCodeInputProps> = ({ onAuthSuccess }) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // 调试模式下的验证码
  const DEBUG_CODE = '123456';

  const handleCodeChange = (details: { value: string[] }) => {
    setCode(details.value);
  };

  const handleCodeComplete = (details: { value: string[] }) => {
    const codeString = details.value.join('');
    if (codeString.length === 6) {
      handleSubmit(codeString);
    }
  };

  const handleSubmit = async (inputCode?: string) => {
    const codeToVerify = inputCode || code.join('');
    
    if (codeToVerify.length !== 6) {
      setErrorMessage('请输入6位数字验证码');
      setShowErrorAlert(true);
      setShowSuccessAlert(false);

      // 3秒后自动隐藏错误提示
      setTimeout(() => {
        setShowErrorAlert(false);
      }, 3000);
      return;
    }

    setIsLoading(true);

    try {
      // 直接请求许可证（服务端会验证验证码）
      const licenseResult = await window.electronAPI.license.request(codeToVerify);

      if (licenseResult.success) {
        // 许可证获取成功
        setShowSuccessAlert(true);
        setErrorMessage(''); // 清除错误信息

        // 延迟一下再跳转，让用户看到成功提示
        setTimeout(() => {
          onAuthSuccess();
        }, 2000);
      } else {
        // 验证失败
        setErrorMessage('验证码错误，请重试');
        setShowErrorAlert(true);
        setShowSuccessAlert(false);
        setCode(['', '', '', '', '', '']);

        // 3秒后自动隐藏错误提示
        setTimeout(() => {
          setShowErrorAlert(false);
        }, 3000);
      }
    } catch (error) {
      console.error('验证过程中发生错误:', error);
      setErrorMessage('验证码错误，请重试');
      setShowErrorAlert(true);
      setShowSuccessAlert(false);

      // 3秒后自动隐藏错误提示
      setTimeout(() => {
        setShowErrorAlert(false);
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setCode(['', '', '', '', '', '']);
    setShowErrorAlert(false);
    setShowSuccessAlert(false);
  };

  return (
    <Box
      minHeight="100vh"
      bg="gray.50"
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      <Container maxW="4xl">
        {/* 头部提示 Alert - 仅在有错误或成功时显示 */}
        {showErrorAlert && (
          <Alert.Root status="error" variant="subtle" mb={4}>
            <Alert.Indicator />
            <Alert.Content>
              <Alert.Title>验证失败</Alert.Title>
              <Alert.Description>
                {errorMessage}
              </Alert.Description>
            </Alert.Content>
          </Alert.Root>
        )}

        {showSuccessAlert && (
          <Alert.Root status="success" variant="subtle" mb={4}>
            <Alert.Indicator />
            <Alert.Content>
              <Alert.Title>验证成功</Alert.Title>
              <Alert.Description>
                验证码正确，正在加载应用...
              </Alert.Description>
            </Alert.Content>
          </Alert.Root>
        )}

        <Card.Root>
          <Card.Body p={8}>
            <HStack
              gap={12}
              align="start"
              justify="center"
              flexWrap={{ base: "wrap", lg: "nowrap" }}
              w="100%"
            >
              {/* 左侧：验证码功能区域 */}
              <VStack
                gap={6}
                align="center"
                minW={{ base: "100%", lg: "400px" }}
                maxW={{ base: "100%", lg: "450px" }}
                flex={{ base: "none", lg: "0 0 auto" }}
              >
                <Text fontSize="md" color="gray.700" textAlign="center" fontWeight="medium">
                  请扫码关注微信公众号获取验证码
                </Text>

                <PinInput.Root
                  value={code}
                  onValueChange={handleCodeChange}
                  onValueComplete={handleCodeComplete}
                  size="lg"
                  disabled={isLoading}
                  otp
                >
                  <PinInput.HiddenInput />
                  <PinInput.Control>
                    <HStack gap={3}>
                      <PinInput.Input index={0} />
                      <PinInput.Input index={1} />
                      <PinInput.Input index={2} />
                      <PinInput.Input index={3} />
                      <PinInput.Input index={4} />
                      <PinInput.Input index={5} />
                    </HStack>
                  </PinInput.Control>
                </PinInput.Root>

                {/* 提示信息 */}
                <Text fontSize="xs" color="gray.500" textAlign="center" lineHeight="1.4">
                  MEEA-VIOFO 是完全免费的软件，验证码仅用于防止恶意传播
                </Text>

                {/* 操作按钮 */}
                <HStack gap={3} w="100%">
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    disabled={isLoading || code.every(digit => digit === '')}
                    flex={1}
                  >
                    重置
                  </Button>
                  <Button
                    colorScheme="blue"
                    onClick={() => handleSubmit()}
                    loading={isLoading}
                    loadingText="验证中..."
                    disabled={!code.every(digit => digit !== '') || code.length !== 6}
                    flex={1}
                  >
                    验证
                  </Button>
                </HStack>
              </VStack>

              {/* 右侧：二维码区域 */}
              <VStack
                gap={3}
                align="center"
                flex={{ base: "none", lg: "0 0 auto" }}
                w={{ base: "100%", lg: "auto" }}
                mt={{ base: 4, lg: 0 }}
              >
                <Box
                  p={4}
                  bg="white"
                  borderRadius="lg"
                  border="1px solid"
                  borderColor="gray.200"
                  boxShadow="sm"
                  w="fit-content"
                >
                  <Image
                    src={pismQrCode}
                    alt="微信公众号二维码"
                    width="160px"
                    height="160px"
                    objectFit="contain"
                    borderRadius="md"
                    onError={(e) => {
                      console.error('二维码图片加载失败:', e);
                    }}
                  />
                </Box>
                <Text fontSize="xs" color="gray.500" textAlign="center">
                  验证码服务由 PISM 提供
                </Text>
              </VStack>
            </HStack>

            {/* 调试信息 */}
            {process.env.NODE_ENV === 'development' && (
              <Box
                p={3}
                bg="yellow.50"
                borderRadius="md"
                border="1px solid"
                borderColor="yellow.200"
                w="100%"
                mt={6}
              >
                <Text fontSize="xs" color="yellow.800" textAlign="center">
                  调试模式：验证码为 {DEBUG_CODE}
                </Text>
              </Box>
            )}
          </Card.Body>
        </Card.Root>
      </Container>
    </Box>
  );
};

export default AuthCodeInput;
