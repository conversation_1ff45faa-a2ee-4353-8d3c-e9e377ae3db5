import React from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Button,
  Image,
  Grid,
  Badge,
  IconButton,
  Tooltip
} from '@chakra-ui/react';
import { FiTrash2, FiExternalLink, FiImage } from 'react-icons/fi';
import { Screenshot } from '../hooks/useScreenshots';

interface ScreenshotPanelProps {
  screenshots: Screenshot[];
  onRemoveScreenshot: (id: string) => void;
  onOpenScreenshot: (imagePath: string) => void;
}

const ScreenshotPanel: React.FC<ScreenshotPanelProps> = ({
  screenshots,
  onRemoveScreenshot,
  onOpenScreenshot
}) => {
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (screenshots.length === 0) {
    return (
      <Box p={4} textAlign="center">
        <VStack gap={3}>
          <FiImage size={48} color="gray" />
          <Text color="gray.500">暂无截图</Text>
          <Text fontSize="sm" color="gray.400">
            点击视频播放器上的相机图标来截取当前帧
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box p={4}>
      <VStack gap={4} align="stretch">
        <HStack justify="space-between" align="center">
          <Text fontWeight="bold" fontSize="lg">
            截图管理
          </Text>
          <Badge colorPalette="blue" variant="subtle">
            {screenshots.length} 张截图
          </Badge>
        </HStack>

        <Grid templateColumns="repeat(auto-fill, minmax(200px, 1fr))" gap={4}>
          {screenshots.map((screenshot) => (
            <Box
              key={screenshot.id}
              borderRadius="md"
              border="1px solid"
              borderColor="gray.200"
              overflow="hidden"
              bg="white"

              transition="all 0.2s"
            >
              {/* 截图预览 */}
              <Box position="relative" aspectRatio="16/9" bg="gray.100">
                <Image
                  src={`file://${screenshot.imagePath}`}
                  alt={`截图 ${formatTime(screenshot.timestamp)}`}
                  w="100%"
                  h="100%"
                  objectFit="cover"
                  cursor="pointer"
                  onClick={() => onOpenScreenshot(screenshot.imagePath)}
                />
                
                {/* 时间标签 */}
                <Badge
                  position="absolute"
                  top={2}
                  right={2}
                  colorPalette="blackAlpha"
                  variant="solid"
                  fontSize="xs"
                >
                  {formatTime(screenshot.timestamp)}
                </Badge>
              </Box>

              {/* 截图信息和操作 */}
              <Box p={3}>
                <VStack gap={2} align="stretch">
                  <Text fontSize="sm" color="gray.600" noOfLines={1}>
                    {formatDate(screenshot.createdAt)}
                  </Text>
                  
                  <HStack justify="space-between">
                    <Tooltip label="在文件管理器中打开">
                      <IconButton
                        size="sm"
                        variant="ghost"
                        colorPalette="blue"
                        onClick={() => onOpenScreenshot(screenshot.imagePath)}
                        aria-label="打开截图"
                      >
                        <FiExternalLink />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip label="删除截图">
                      <IconButton
                        size="sm"
                        variant="ghost"
                        colorPalette="red"
                        onClick={() => onRemoveScreenshot(screenshot.id)}
                        aria-label="删除截图"
                      >
                        <FiTrash2 />
                      </IconButton>
                    </Tooltip>
                  </HStack>
                </VStack>
              </Box>
            </Box>
          ))}
        </Grid>
      </VStack>
    </Box>
  );
};

export default ScreenshotPanel;
