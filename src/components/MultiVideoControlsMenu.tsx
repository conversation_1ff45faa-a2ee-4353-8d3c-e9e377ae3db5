import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Portal
} from '@chakra-ui/react';
import {
  FiMoreHorizontal,
  FiCamera,
  FiScissors,
  FiSave,
  FiX
} from 'react-icons/fi';
import ClipControls from './ClipControls';
import VideoClipTimeline from './VideoClipTimeline';

export interface MultiVideoControlsMenuProps {
  // 剪辑相关
  showClipControls?: boolean;
  videoPath?: string;
  currentTime: number;
  duration?: number;
  onScreenshotTaken?: (screenshot: any) => void;
  isMultiVideo?: boolean;
  onCaptureFrame?: () => Promise<any>;

  // 剪辑模式相关
  isClipMode?: boolean;
  clipStartTime?: number;
  clipEndTime?: number;
  onToggleClipMode?: () => void;
  onClipTimeChange?: (startTime: number, endTime: number) => void;
  onSaveClip?: () => void;
}

const MultiVideoControlsMenu: React.FC<MultiVideoControlsMenuProps> = ({
  showClipControls = true,
  videoPath,
  currentTime,
  duration = 0,
  onScreenshotTaken,
  isMultiVideo = false,
  onCaptureFrame,
  isClipMode = false,
  clipStartTime = 0,
  clipEndTime = 0,
  onToggleClipMode,
  onClipTimeChange,
  onSaveClip
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [menuPosition, setMenuPosition] = useState<{
    bottom?: string;
    top?: string;
    right?: string;
    left?: string;
  }>({ bottom: '80px', right: '20px' });
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 点击外部关闭菜单和键盘事件处理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isMenuOpen]);

  const toggleMenu = () => {
    if (!isMenuOpen && buttonRef.current) {
      // 计算菜单位置
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const menuWidth = 250; // 菜单预估宽度
      const menuHeight = isClipMode ? 300 : 200; // 菜单预估高度

      // 计算最佳位置
      let bottom, right, left, top;

      // 优先在按钮上方显示
      if (buttonRect.top > menuHeight + 20) {
        bottom = viewportHeight - buttonRect.top + 10;
        top = undefined;
      } else {
        // 上方空间不够，在下方显示
        top = buttonRect.bottom + 10;
        bottom = undefined;
      }

      // 水平位置：优先右对齐，如果空间不够则左对齐
      if (buttonRect.right >= menuWidth) {
        right = viewportWidth - buttonRect.right;
        left = undefined;
      } else {
        // 右侧空间不够，左对齐
        left = Math.max(10, buttonRect.left);
        right = undefined;
      }

      setMenuPosition({
        bottom: bottom ? `${bottom}px` : undefined,
        top: top ? `${top}px` : undefined,
        right: right !== undefined ? `${right}px` : undefined,
        left: left !== undefined ? `${left}px` : undefined
      });
    }
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuItemClick = (action: () => void) => {
    action();
    // 某些操作后不关闭菜单（如切换剪辑模式）
    // setIsMenuOpen(false);
  };

  const handleScreenshotClick = async () => {
    if (onCaptureFrame && !isProcessing) {
      try {
        setIsProcessing(true);
        await onCaptureFrame();
        setIsMenuOpen(false);
      } catch (error) {
        console.error('截图失败:', error);
        // 即使失败也关闭菜单
        setIsMenuOpen(false);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const handleSaveClipClick = async () => {
    if (onSaveClip && !isProcessing) {
      try {
        setIsProcessing(true);
        await onSaveClip();
        setIsMenuOpen(false);
      } catch (error) {
        console.error('保存剪辑失败:', error);
        setIsMenuOpen(false);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  return (
    <Box position="relative">
      {/* 菜单触发按钮 */}
      <Button
        ref={buttonRef}
        size="sm"
        variant="ghost"
        color="white"
        onClick={toggleMenu}
        _hover={{ bg: "rgba(255,255,255,0.2)" }}
        title="更多操作"
        bg={isMenuOpen ? "rgba(255,255,255,0.2)" : "transparent"}
      >
        <FiMoreHorizontal />
      </Button>

      {/* 下拉菜单 */}
      {isMenuOpen && (
        <Portal>
          <Box
            ref={menuRef}
            position="fixed"
            bottom={menuPosition.bottom}
            top={menuPosition.top}
            right={menuPosition.right}
            left={menuPosition.left}
            bg="blackAlpha.900"
            backdropFilter="blur(8px)"
            borderRadius="md"
            border="1px solid"
            borderColor="whiteAlpha.200"
            p={3}
            minW="200px"
            maxW="250px"
            zIndex={9999}
            boxShadow="lg"
            transform="translateY(0)"
            transition="all 0.2s ease-in-out"
          >
            <VStack align="stretch" gap={2}>
              {/* 菜单标题 */}
              <HStack justify="space-between" align="center">
                <Text color="white" fontSize="sm" fontWeight="medium">
                  视频操作
                </Text>
                <Button
                  size="xs"
                  variant="ghost"
                  color="white"
                  onClick={() => setIsMenuOpen(false)}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                >
                  <FiX />
                </Button>
              </HStack>

              <Box height="1px" bg="whiteAlpha.200" width="100%" />

              {/* 截图功能 */}
              {showClipControls && videoPath && (
                <Button
                  size="sm"
                  variant="ghost"
                  color="white"
                  leftIcon={<FiCamera />}
                  onClick={handleScreenshotClick}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  justifyContent="flex-start"
                  w="100%"
                  fontWeight="normal"
                  loading={isProcessing}
                  disabled={isProcessing}
                >
                  截图
                </Button>
              )}

              {/* 剪辑模式切换 */}
              {showClipControls && onToggleClipMode && (
                <Button
                  size="sm"
                  variant="ghost"
                  color={isClipMode ? "blue.300" : "white"}
                  leftIcon={<FiScissors />}
                  onClick={() => handleMenuItemClick(onToggleClipMode)}
                  _hover={{ bg: "rgba(255,255,255,0.2)" }}
                  justifyContent="flex-start"
                  w="100%"
                  fontWeight="normal"
                >
                  剪辑
                </Button>
              )}

              {/* 保存剪辑 */}
              {isClipMode && onSaveClip && (
                <>
                  <Box height="1px" bg="whiteAlpha.200" width="100%" />
                  <Button
                    size="sm"
                    variant="solid"
                    colorPalette="blue"
                    leftIcon={<FiSave />}
                    onClick={handleSaveClipClick}
                    w="100%"
                    loading={isProcessing}
                    disabled={isProcessing}
                  >
                    保存
                  </Button>
                </>
              )}

              {/* 剪辑时间选择器 */}
              {isClipMode && onClipTimeChange && (
                <Box p={2} bg="whiteAlpha.100" borderRadius="sm">
                  <Text color="white" fontSize="xs" mb={2} textAlign="center">
                    剪辑时长: {Math.max(0, clipEndTime - clipStartTime).toFixed(1)}s
                  </Text>
                  <VideoClipTimeline
                    duration={duration}
                    currentTime={currentTime}
                    startTime={clipStartTime}
                    endTime={clipEndTime}
                    onStartTimeChange={(time) => onClipTimeChange(time, clipEndTime)}
                    onEndTimeChange={(time) => onClipTimeChange(clipStartTime, time)}
                    onSeek={onSeek} // 传递seek功能，实现视频联动
                  />
                </Box>
              )}
            </VStack>
          </Box>
        </Portal>
      )}
    </Box>
  );
};

export default MultiVideoControlsMenu;
