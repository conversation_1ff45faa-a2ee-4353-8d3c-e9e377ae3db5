import { useState, useCallback } from 'react';

export interface Screenshot {
  id: string;
  videoPath: string;
  timestamp: number;
  imagePath: string;
  createdAt: Date;
  thumbnailPath?: string;
}

export interface UseScreenshotsReturn {
  screenshots: Screenshot[];
  addScreenshot: (screenshot: Omit<Screenshot, 'id' | 'createdAt'>) => void;
  removeScreenshot: (id: string) => void;
  getScreenshotsForVideo: (videoPath: string) => Screenshot[];
  clearScreenshots: () => void;
}

export const useScreenshots = (): UseScreenshotsReturn => {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([]);

  const addScreenshot = useCallback((screenshot: Omit<Screenshot, 'id' | 'createdAt'>) => {
    const newScreenshot: Screenshot = {
      ...screenshot,
      id: `screenshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };
    
    setScreenshots(prev => [...prev, newScreenshot]);
  }, []);

  const removeScreenshot = useCallback((id: string) => {
    setScreenshots(prev => prev.filter(s => s.id !== id));
  }, []);

  const getScreenshotsForVideo = useCallback((videoPath: string) => {
    return screenshots.filter(s => s.videoPath === videoPath);
  }, [screenshots]);

  const clearScreenshots = useCallback(() => {
    setScreenshots([]);
  }, []);

  return {
    screenshots,
    addScreenshot,
    removeScreenshot,
    getScreenshotsForVideo,
    clearScreenshots,
  };
};
