import { useState, useCallback, useRef } from 'react';

export interface VideoPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
  playbackRate: number;
}

export interface VideoPlayerActions {
  togglePlay: () => void;
  play: () => void;
  pause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  skipTime: (seconds: number) => void;
  toggleFullscreen: () => void;
  setPlaybackRate: (rate: number) => void;
  setDuration: (duration: number) => void;
  setCurrentTime: (time: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string) => void;
  clearError: () => void;
}

export interface UseVideoPlayerStateOptions {
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number) => void;
  onError?: (error: string) => void;
}

export const useVideoPlayerState = (options: UseVideoPlayerStateOptions = {}) => {
  const {
    onTimeUpdate,
    onPlay,
    onPause,
    onSeek,
    onVolumeChange,
    onError
  } = options;

  const [state, setState] = useState<VideoPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isFullscreen: false,
    isLoading: false,
    hasError: false,
    errorMessage: '',
    playbackRate: 1
  });

  const videoRef = useRef<HTMLVideoElement | null>(null);

  // 播放控制
  const play = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error);
    }
    setState(prev => ({ ...prev, isPlaying: true }));
    onPlay?.();
  }, [onPlay]);

  const pause = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause();
    }
    setState(prev => ({ ...prev, isPlaying: false }));
    onPause?.();
  }, [onPause]);

  const togglePlay = useCallback(() => {
    if (state.isPlaying) {
      pause();
    } else {
      play();
    }
  }, [state.isPlaying, play, pause]);

  // 时间控制
  const seek = useCallback((time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
    }
    setState(prev => ({ ...prev, currentTime: time }));
    onSeek?.(time);
    onTimeUpdate?.(time, state.duration);
  }, [state.duration, onSeek, onTimeUpdate]);

  const skipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(state.duration, state.currentTime + seconds));
    seek(newTime);
  }, [state.currentTime, state.duration, seek]);

  const setCurrentTime = useCallback((time: number) => {
    setState(prev => ({ ...prev, currentTime: time }));
    onTimeUpdate?.(time, state.duration);
  }, [state.duration, onTimeUpdate]);

  const setDuration = useCallback((duration: number) => {
    setState(prev => ({ ...prev, duration }));
  }, []);

  // 音量控制
  const setVolume = useCallback((volume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = volume;
    }
    setState(prev => ({ ...prev, volume, isMuted: volume === 0 }));
    onVolumeChange?.(volume);
  }, [onVolumeChange]);

  const toggleMute = useCallback(() => {
    if (videoRef.current) {
      const newMuted = !state.isMuted;
      videoRef.current.muted = newMuted;
      setState(prev => ({ ...prev, isMuted: newMuted }));
    }
  }, [state.isMuted]);

  // 全屏控制
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      if (videoRef.current?.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    } else {
      document.exitFullscreen();
    }
  }, []);

  // 播放速度控制
  const setPlaybackRate = useCallback((rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
    }
    setState(prev => ({ ...prev, playbackRate: rate }));
  }, []);

  // 状态设置
  const setIsPlaying = useCallback((playing: boolean) => {
    setState(prev => ({ ...prev, isPlaying: playing }));
  }, []);

  const setIsLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string) => {
    setState(prev => ({ ...prev, hasError: true, errorMessage: error, isLoading: false }));
    onError?.(error);
  }, [onError]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, hasError: false, errorMessage: '' }));
  }, []);

  // 视频元素事件处理器
  const getVideoEventHandlers = useCallback(() => {
    return {
      onTimeUpdate: (e: React.SyntheticEvent<HTMLVideoElement>) => {
        const video = e.currentTarget;
        setCurrentTime(video.currentTime);
      },
      onLoadedMetadata: (e: React.SyntheticEvent<HTMLVideoElement>) => {
        const video = e.currentTarget;
        setDuration(video.duration);
        setState(prev => ({ ...prev, isLoading: false }));
      },
      onPlay: () => setIsPlaying(true),
      onPause: () => setIsPlaying(false),
      onLoadStart: () => {
        setState(prev => ({ 
          ...prev, 
          isLoading: true, 
          hasError: false, 
          errorMessage: '' 
        }));
      },
      onCanPlay: () => setIsLoading(false),
      onError: (e: React.SyntheticEvent<HTMLVideoElement>) => {
        const video = e.currentTarget;
        setError(`视频加载失败: ${video.error?.message || '未知错误'}`);
      },
      onVolumeChange: (e: React.SyntheticEvent<HTMLVideoElement>) => {
        const video = e.currentTarget;
        setState(prev => ({ 
          ...prev, 
          volume: video.volume, 
          isMuted: video.muted 
        }));
      }
    };
  }, [setCurrentTime, setDuration, setIsPlaying, setIsLoading, setError]);

  // 全屏状态监听
  const handleFullscreenChange = useCallback(() => {
    setState(prev => ({ ...prev, isFullscreen: !!document.fullscreenElement }));
  }, []);

  const actions: VideoPlayerActions = {
    togglePlay,
    play,
    pause,
    seek,
    setVolume,
    toggleMute,
    skipTime,
    toggleFullscreen,
    setPlaybackRate,
    setDuration,
    setCurrentTime,
    setIsPlaying,
    setIsLoading,
    setError,
    clearError
  };

  return {
    state,
    actions,
    videoRef,
    getVideoEventHandlers,
    handleFullscreenChange
  };
};
