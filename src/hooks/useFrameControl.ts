import { useCallback, useRef, useEffect } from 'react';

interface UseFrameControlOptions {
  onSeek: (time: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  frameRate?: number; // 默认30fps
}

interface FrameControlActions {
  stepForward: () => void;
  stepBackward: () => void;
  startContinuousForward: () => void;
  startContinuousBackward: () => void;
  stopContinuous: () => void;
}

export const useFrameControl = ({
  onSeek,
  getCurrentTime,
  getDuration,
  frameRate = 30
}: UseFrameControlOptions): FrameControlActions => {
  const continuousIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const longPressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressRef = useRef(false);

  // 计算一帧的时间（秒）
  const frameTime = 1 / frameRate;

  // 单步前进一帧
  const stepForward = useCallback(() => {
    const currentTime = getCurrentTime();
    const duration = getDuration();
    const newTime = Math.min(duration, currentTime + frameTime);
    onSeek(newTime);
  }, [getCurrentTime, getDuration, onSeek, frameTime]);

  // 单步后退一帧
  const stepBackward = useCallback(() => {
    const currentTime = getCurrentTime();
    const newTime = Math.max(0, currentTime - frameTime);
    onSeek(newTime);
  }, [getCurrentTime, onSeek, frameTime]);

  // 开始连续前进
  const startContinuousForward = useCallback(() => {
    // 清除之前的定时器
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
    }

    // 设置连续移动定时器，每帧间隔执行
    continuousIntervalRef.current = setInterval(() => {
      const currentTime = getCurrentTime();
      const duration = getDuration();
      const newTime = Math.min(duration, currentTime + frameTime);
      
      if (newTime >= duration) {
        // 到达结尾，停止连续移动
        if (continuousIntervalRef.current) {
          clearInterval(continuousIntervalRef.current);
          continuousIntervalRef.current = null;
        }
        return;
      }
      
      onSeek(newTime);
    }, frameTime * 1000); // 转换为毫秒
  }, [getCurrentTime, getDuration, onSeek, frameTime]);

  // 开始连续后退
  const startContinuousBackward = useCallback(() => {
    // 清除之前的定时器
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
    }

    // 设置连续移动定时器，每帧间隔执行
    continuousIntervalRef.current = setInterval(() => {
      const currentTime = getCurrentTime();
      const newTime = Math.max(0, currentTime - frameTime);
      
      if (newTime <= 0) {
        // 到达开头，停止连续移动
        if (continuousIntervalRef.current) {
          clearInterval(continuousIntervalRef.current);
          continuousIntervalRef.current = null;
        }
        return;
      }
      
      onSeek(newTime);
    }, frameTime * 1000); // 转换为毫秒
  }, [getCurrentTime, onSeek, frameTime]);

  // 停止连续移动
  const stopContinuous = useCallback(() => {
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
      continuousIntervalRef.current = null;
    }
    if (longPressTimeoutRef.current) {
      clearTimeout(longPressTimeoutRef.current);
      longPressTimeoutRef.current = null;
    }
    isLongPressRef.current = false;
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (continuousIntervalRef.current) {
        clearInterval(continuousIntervalRef.current);
      }
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
      }
    };
  }, []);

  return {
    stepForward,
    stepBackward,
    startContinuousForward,
    startContinuousBackward,
    stopContinuous
  };
};
