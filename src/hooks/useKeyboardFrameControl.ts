import { useEffect, useRef, useCallback } from 'react';
import { useFrameControl } from './useFrameControl';

interface UseKeyboardFrameControlOptions {
  onSeek: (time: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  frameRate?: number;
  longPressDelay?: number; // 长按检测延迟（毫秒）
  enabled?: boolean; // 是否启用键盘控制
  containerRef?: React.RefObject<HTMLElement | null>; // 容器引用，用于检查焦点
}

export const useKeyboardFrameControl = ({
  onSeek,
  getCurrentTime,
  getDuration,
  frameRate = 30,
  longPressDelay = 300,
  enabled = true,
  containerRef
}: UseKeyboardFrameControlOptions) => {
  const frameControl = useFrameControl({
    onSeek,
    getCurrentTime,
    getDuration,
    frameRate
  });

  const longPressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressRef = useRef(false);
  const pressedKeysRef = useRef<Set<string>>(new Set());

  // 处理键盘按下事件
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!enabled) return;

    // 检查是否在输入框中，如果是则不处理快捷键
    const target = e.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return;
    }

    // 如果提供了容器引用，检查焦点是否在容器内
    if (containerRef?.current && !containerRef.current.contains(document.activeElement)) {
      return;
    }

    // 避免重复处理同一个按键
    if (pressedKeysRef.current.has(e.code)) {
      return;
    }

    pressedKeysRef.current.add(e.code);

    switch (e.code) {
      case 'ArrowLeft':
        e.preventDefault();
        // 立即执行一次单步后退
        frameControl.stepBackward();
        
        // 设置长按检测
        isLongPressRef.current = false;
        longPressTimeoutRef.current = setTimeout(() => {
          isLongPressRef.current = true;
          frameControl.startContinuousBackward();
        }, longPressDelay);
        break;

      case 'ArrowRight':
        e.preventDefault();
        // 立即执行一次单步前进
        frameControl.stepForward();
        
        // 设置长按检测
        isLongPressRef.current = false;
        longPressTimeoutRef.current = setTimeout(() => {
          isLongPressRef.current = true;
          frameControl.startContinuousForward();
        }, longPressDelay);
        break;
    }
  }, [enabled, frameControl, longPressDelay, containerRef]);

  // 处理键盘释放事件
  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    if (!enabled) return;

    pressedKeysRef.current.delete(e.code);

    switch (e.code) {
      case 'ArrowLeft':
      case 'ArrowRight':
        e.preventDefault();
        
        // 清除长按检测定时器
        if (longPressTimeoutRef.current) {
          clearTimeout(longPressTimeoutRef.current);
          longPressTimeoutRef.current = null;
        }
        
        // 如果是长按状态，停止连续移动
        if (isLongPressRef.current) {
          frameControl.stopContinuous();
          isLongPressRef.current = false;
        }
        break;
    }
  }, [enabled, frameControl]);

  // 注册键盘事件监听器
  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      
      // 清理定时器
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
      }
      frameControl.stopContinuous();
      pressedKeysRef.current.clear();
    };
  }, [enabled, handleKeyDown, handleKeyUp, frameControl]);

  // 当组件卸载或禁用时清理状态
  useEffect(() => {
    if (!enabled) {
      frameControl.stopContinuous();
      pressedKeysRef.current.clear();
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
        longPressTimeoutRef.current = null;
      }
    }
  }, [enabled, frameControl]);

  return {
    // 暴露手动控制方法，供其他组件使用
    stepForward: frameControl.stepForward,
    stepBackward: frameControl.stepBackward,
    startContinuousForward: frameControl.startContinuousForward,
    startContinuousBackward: frameControl.startContinuousBackward,
    stopContinuous: frameControl.stopContinuous
  };
};
