import { createToaster, Toaster as Cha<PERSON>Toaster, Toast, Portal } from "@chakra-ui/react"

export const toaster = createToaster({
  placement: "top",
  pauseOnPageIdle: true,
  offsets: '10px',
})

export const Toaster = () => {
  return (
    <Portal>
      <ChakraToaster toaster={toaster}>
        {(toast) => (
          <Toast.Root
            key={toast.id}
            style={{
              position: 'fixed',
              top: '10px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 999999,
              maxWidth: '300px',
              backgroundColor: toast.status === 'success' ? '#48bb78' : '#f56565',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              fontSize: '14px',
              textAlign: 'center',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              cursor: 'pointer'
            }}
            onClick={() => toaster.dismiss(toast.id)}
          >
            {toast.description && (
              <Toast.Description>
                {String(toast.description)}
              </Toast.Description>
            )}
            <Toast.CloseTrigger />
          </Toast.Root>
        )}
      </ChakraToaster>
    </Portal>
  )
}
