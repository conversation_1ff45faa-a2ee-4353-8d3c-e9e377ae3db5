{"appId": "com.meea.viofo", "productName": "MEEA-VIOFO", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron/**/*", "package.json", "!node_modules/**/*", "node_modules/electron-log/**/*", "node_modules/electron-updater/**/*"], "win": {"icon": "build/icons/icon.ico", "files": ["!ffmpeg/mac-*/**/*", "!ffmpeg/linux-*/**/*", "!ffmpeg/win-arm64/**/*"], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "extraResources": [{"from": "ffmpeg/win-x64", "to": "ffmpeg/win-x64", "filter": ["**/*"]}, {"from": "exiftool/win-x64", "to": "exiftool/win-x64", "filter": ["**/*"]}, {"from": "exiftool/win-arm64", "to": "exiftool/win-arm64", "filter": ["**/*"]}], "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MEEA-VIOFO", "artifactName": "${productName}-Setup-${version}-win32-x64.${ext}", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Video", "include": "build/installer.nsh"}}