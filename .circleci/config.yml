# CircleCI 配置 - 时间戳版本号线性构建流程
# 合并到 main 分支时自动构建所有平台并上传到 OSS

version: 2.1

orbs:
  node: circleci/node@5.1.0

# 使用 Node.js 22.12 以满足 Vite 7.0.3 的要求
executors:
  node-executor:
    docker:
      - image: cimg/node:22.12
    working_directory: ~/project

commands:
  install-dependencies:
    description: "安装项目依赖并生成时间戳版本号"
    steps:
      - checkout
      - run:
          name: 生成时间戳版本号
          command: |
            echo "🕐 生成时间戳版本号..."
            # 生成时间戳版本号 (格式: YY.MM.DD.HHmm)
            YEAR=$(date +%y)
            MONTH=$(date +%m)
            DAY=$(date +%d)
            HOUR=$(date +%H)
            MINUTE=$(date +%M)
            VERSION="$YEAR.$MONTH.$DAY.$HOUR$MINUTE"
            echo "生成的版本号: $VERSION"

            # 设置环境变量供后续步骤使用
            echo "export CI_VERSION=$VERSION" >> $BASH_ENV

            echo "✅ 版本号: $VERSION"
      - node/install-packages:
          pkg-manager: yarn
          cache-path: ~/.cache/yarn
      - run:
          name: 更新 package.json 版本号
          command: |
            echo "📝 更新 package.json 版本号为: $CI_VERSION"
            node scripts/generate-version.js
            echo "✅ 版本号更新完成"

  setup-linux-deps:
    description: "安装Linux构建依赖"
    steps:
      - run:
          name: 安装系统依赖
          command: |
            sudo apt-get update
            sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

  upload-to-oss:
    description: "上传构建产物到阿里云OSS"
    parameters:
      platform:
        type: string
        description: "平台名称 (mac/windows/linux)"
    steps:
      - run:
          name: 安装阿里云CLI
          command: |
            echo "📦 安装阿里云CLI..."
            # 检测系统架构和类型
            ARCH=$(uname -m)
            OS=$(uname -s)

            echo "系统信息: $OS $ARCH"

            # 下载并安装阿里云CLI
            if [[ "$OS" == "Linux" ]]; then
              if [[ "$ARCH" == "x86_64" ]]; then
                wget https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz
                tar -xzf aliyun-cli-linux-latest-amd64.tgz
              elif [[ "$ARCH" == "aarch64" || "$ARCH" == "arm64" ]]; then
                wget https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-arm64.tgz
                tar -xzf aliyun-cli-linux-latest-arm64.tgz
              fi
              sudo mv aliyun /usr/local/bin/
            elif [[ "$OS" == "Darwin" ]]; then
              if [[ "$ARCH" == "x86_64" ]]; then
                curl -O https://aliyuncli.alicdn.com/aliyun-cli-macosx-latest-amd64.tgz
                tar -xzf aliyun-cli-macosx-latest-amd64.tgz
              elif [[ "$ARCH" == "arm64" ]]; then
                curl -O https://aliyuncli.alicdn.com/aliyun-cli-macosx-latest-arm64.tgz
                tar -xzf aliyun-cli-macosx-latest-arm64.tgz
              fi
              sudo mv aliyun /usr/local/bin/
            fi

            # 验证安装
            aliyun version
      - run:
          name: 配置阿里云CLI并上传
          command: |
            echo "🔧 配置阿里云CLI..."
            # 固定使用深圳区域 (oss-cn-shenzhen.aliyuncs.com)
            aliyun configure set \
              --profile default \
              --mode AK \
              --region cn-shenzhen \
              --access-key-id $ALIYUN_ACCESS_KEY_ID \
              --access-key-secret $ALIYUN_ACCESS_KEY_SECRET

            echo "✅ 配置完成，使用深圳区域: cn-shenzhen"

            # 测试权限
            echo "🔍 测试OSS访问权限..."
            if aliyun oss ls oss://meea-viofo-builds/ 2>/dev/null; then
              echo "✅ Bucket访问权限正常"
            else
              echo "❌ Bucket访问权限不足，请检查配置"
              exit 1
            fi
            echo "🚀 开始上传 << parameters.platform >> 构建产物到阿里云OSS..."

            # 检查构建产物目录
            if [ ! -d "dist" ]; then
              echo "❌ 构建产物目录不存在: dist"
              exit 1
            fi

            # 使用环境变量中的版本信息
            VERSION=${CI_VERSION:-"$(date +%y.%m.%d.%H%M)"}
            COMMIT_SHORT=${CIRCLE_SHA1:0:8}

            # OSS路径配置
            OSS_BUCKET="meea-viofo-builds"
            OSS_PATH="builds/$VERSION/<< parameters.platform >>"

            echo "📋 上传信息:"
            echo "  - 版本: $VERSION"
            echo "  - 平台: << parameters.platform >>"
            echo "  - 提交: $COMMIT_SHORT"
            echo "  - OSS路径: oss://$OSS_BUCKET/$OSS_PATH"

            # 列出要上传的文件
            echo "📁 构建产物列表:"
            ls -la dist/

            # 查找安装包文件
            echo "🔍 查找 << parameters.platform >> 安装包文件..."
            PACKAGE_FILES_LIST=$(find dist -type f \( -name "*.AppImage" -o -name "*.deb" -o -name "*.rpm" -o -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.tar.gz" \) \
              ! -name "*.blockmap" \
              ! -name "*.yaml" \
              ! -name "*.yml" \
              ! -name "*-unpacked*")

            PACKAGE_COUNT=$(echo "$PACKAGE_FILES_LIST" | grep -c . || echo "0")
            echo "🔍 找到 $PACKAGE_COUNT 个安装包文件:"
            echo "$PACKAGE_FILES_LIST" | while read file; do
              [ -n "$file" ] && echo "  - $(basename "$file")"
            done

            if [ "$PACKAGE_COUNT" -eq 0 ]; then
              echo "⚠️  没有找到符合条件的安装包文件"
              echo "📁 所有文件列表:"
              find dist -type f -exec basename {} \; | sed 's/^/  - /'
              exit 1
            fi

            # 上传文件
            echo "⬆️  开始上传安装包文件..."
            echo "$PACKAGE_FILES_LIST" | while read file; do
              if [ -n "$file" ]; then
                filename=$(basename "$file")
                echo "📤 上传安装包: $filename"

                # 上传文件到OSS
                if aliyun oss cp "$file" "oss://$OSS_BUCKET/$OSS_PATH/$filename"; then
                  echo "✅ 上传成功: $filename"
                  echo "📍 OSS路径: oss://$OSS_BUCKET/$OSS_PATH/$filename"
                else
                  echo "❌ 上传失败: $filename"
                  exit 1
                fi
              fi
            done

            # 创建构建信息文件
            BUILD_INFO_FILE="build-info-<< parameters.platform >>.json"
            PACKAGE_FILES=$(find dist -type f \( -name "*.AppImage" -o -name "*.deb" -o -name "*.rpm" -o -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.tar.gz" \) \
              ! -name "*.blockmap" \
              ! -name "*.yaml" \
              ! -name "*.yml" \
              ! -name "*-unpacked*" \
              -exec basename {} \; | sed 's/.*/"&"/' | paste -sd ',' -)

            cat > "$BUILD_INFO_FILE" \<< EOF
            {
              "platform": "<< parameters.platform >>",
              "version": "$VERSION",
              "commit": "$COMMIT_SHORT",
              "timestamp": "$(date +%y.%m.%d.%H%M)",
              "build_time": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "files": [$PACKAGE_FILES]
            }
            EOF

            # 上传构建信息
            echo "📋 上传构建信息..."
            aliyun oss cp "$BUILD_INFO_FILE" "oss://$OSS_BUCKET/$OSS_PATH/$BUILD_INFO_FILE"

            echo "🎉 << parameters.platform >> 平台构建产物上传完成!"
            echo "📍 OSS路径: oss://$OSS_BUCKET/$OSS_PATH"

jobs:
  # 构建前端和设置 FFmpeg
  build:
    executor: node-executor
    steps:
      - install-dependencies
      - run:
          name: 验证 FFmpeg
          command: |
            echo "🔧 验证 FFmpeg 二进制文件..."
            yarn test:ffmpeg
            echo "✅ FFmpeg 验证完成"
      - run:
          name: 构建前端
          command: yarn build
      - persist_to_workspace:
          root: ~/project
          paths:
            - dist
            - build
            - assets
            - ffmpeg
            - node_modules
            - package.json
            - electron
            - scripts

  # macOS 打包 (第一个构建)
  package-macos:
    macos:
      xcode: 15.0.0
      resource_class: macos.m1.medium.gen1
    working_directory: ~/project
    environment:
      PATH: "/opt/homebrew/opt/node@22/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin"
      NODE_PATH: "/opt/homebrew/opt/node@22/lib/node_modules"
    steps:
      - attach_workspace:
          at: ~/project
      - run:
          name: 安装 Node.js 和 Yarn
          command: |
            echo "🔧 安装 Node.js 22..."
            brew install node@22
            export PATH="/opt/homebrew/opt/node@22/bin:$PATH"
            echo "Node.js 版本: $(node --version)"

            echo "🔧 安装 Yarn..."
            npm install -g yarn --force
            echo "Yarn 版本: $(yarn --version)"
      - run:
          name: 打包 macOS 应用
          command: |
            echo "🔨 开始构建 macOS 应用..."
            export PATH="/opt/homebrew/opt/node@22/bin:$PATH"

            # 使用新的 dist 脚本构建 macOS
            yarn dist:mac

            echo "📦 构建完成，检查生成的文件..."
            ls -la dist/

            # 检查是否有 macOS 安装包
            MACOS_PACKAGES=$(find dist -name "*.dmg" -o -name "*.zip" | wc -l)
            echo "找到 $MACOS_PACKAGES 个 macOS 安装包"

            if [ "$MACOS_PACKAGES" -eq 0 ]; then
              echo "❌ 没有找到 macOS 安装包文件"
              exit 1
            fi

            echo "✅ macOS 构建成功"
      - upload-to-oss:
          platform: "mac"

  # Windows 打包 (第二个构建)
  package-windows:
    machine:
      image: windows-server-2022-gui:current
    resource_class: windows.medium
    working_directory: ~/project
    steps:
      - attach_workspace:
          at: ~/project
      - run:
          name: 安装 Node.js 和 Yarn
          shell: powershell.exe
          command: |
            # 使用 Chocolatey 安装 Node.js
            choco install nodejs --version=22.12.0 -y
            # 重新加载环境变量
            $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
            # 验证安装
            Write-Host "Node.js 版本: $(node --version)"

            # 安装 Yarn
            npm install -g yarn
            Write-Host "Yarn 版本: $(yarn --version)"
      - run:
          name: 安装依赖
          shell: powershell.exe
          command: yarn install --frozen-lockfile
      - run:
          name: 打包 Windows 应用
          shell: powershell.exe
          command: |
            Write-Host "🔨 开始构建 Windows 应用..."
            yarn dist:win
            Write-Host "📦 构建完成，检查生成的文件..."

            # 列出所有文件
            Write-Host "📁 dist 目录内容:"
            Get-ChildItem "dist" -Recurse

            # 检查是否有 Windows 安装包
            $winPackages = @(Get-ChildItem "dist" -Recurse -Include "*.exe", "*.msi", "*.zip").Count
            Write-Host "找到 $winPackages 个 Windows 安装包"

            if ($winPackages -eq 0) {
              Write-Host "❌ 没有找到 Windows 安装包文件"
              exit 1
            }

            Write-Host "✅ Windows 构建成功"
      - run:
          name: 安装阿里云CLI并上传 (Windows)
          shell: powershell.exe
          command: |
            Write-Host "📦 安装阿里云CLI..."
            # 下载Windows版本的阿里云CLI
            Invoke-WebRequest -Uri "https://aliyuncli.alicdn.com/aliyun-cli-windows-latest-amd64.zip" -OutFile "aliyun-cli.zip"
            Expand-Archive -Path "aliyun-cli.zip" -DestinationPath "."

            Write-Host "🔧 配置阿里云CLI..."
            .\aliyun.exe configure set --profile default --mode AK --region cn-shenzhen --access-key-id $env:ALIYUN_ACCESS_KEY_ID --access-key-secret $env:ALIYUN_ACCESS_KEY_SECRET

            Write-Host "🚀 开始上传 Windows 构建产物..."

            # 使用环境变量中的版本信息
            $version = if ($env:CI_VERSION) { $env:CI_VERSION } else { "$(Get-Date -Format 'yy.MM.dd.HHmm')" }
            $commitShort = $env:CIRCLE_SHA1.Substring(0,8)
            $ossBucket = "meea-viofo-builds"
            $ossPath = "builds/$version/windows"

            Write-Host "📋 上传信息: 版本=$version, 平台=windows"

            # 查找并上传安装包文件
            $packageFiles = Get-ChildItem "dist" -File | Where-Object {
              ($_.Name -like "*.exe" -or $_.Name -like "*.msi" -or $_.Name -like "*.zip") -and
              $_.Name -notlike "*.blockmap"
            }

            $packageFiles | ForEach-Object {
              Write-Host "📤 上传: $($_.Name)"
              .\aliyun.exe oss cp $_.FullName "oss://$ossBucket/$ossPath/$($_.Name)"
              if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 上传成功: $($_.Name)"
              } else {
                Write-Host "❌ 上传失败: $($_.Name)"
                exit 1
              }
            }

            Write-Host "🎉 Windows 构建产物上传完成!"

  # Linux 打包 (第三个构建)
  package-linux:
    executor: node-executor
    steps:
      - attach_workspace:
          at: ~/project
      - setup-linux-deps
      - run:
          name: 打包 Linux 应用
          command: |
            echo "🔨 开始构建 Linux 应用..."
            yarn dist:linux

            echo "📦 构建完成，检查生成的文件..."
            ls -la dist/

            # 检查是否有 Linux 安装包
            LINUX_PACKAGES=$(find dist -name "*.AppImage" -o -name "*.deb" -o -name "*.rpm" -o -name "*.tar.gz" | wc -l)
            echo "找到 $LINUX_PACKAGES 个 Linux 安装包"

            if [ "$LINUX_PACKAGES" -eq 0 ]; then
              echo "❌ 没有找到 Linux 安装包文件"
              exit 1
            fi

            echo "✅ Linux 构建成功"
      - upload-to-oss:
          platform: "linux"



workflows:
  version: 2

  # 统一构建流程 - 在 main 分支触发
  build-and-release:
    jobs:
      # 第一步：构建前端和准备环境
      - build:
          filters:
            branches:
              only: main

      # 第二步：构建 macOS (依赖 build)
      - package-macos:
          requires:
            - build
          filters:
            branches:
              only: main

      # 第三步：构建 Windows (依赖 macOS)
      - package-windows:
          requires:
            - package-macos
          filters:
            branches:
              only: main

      # 第四步：构建 Linux (依赖 Windows)
      - package-linux:
          requires:
            - package-windows
          filters:
            branches:
              only: main
