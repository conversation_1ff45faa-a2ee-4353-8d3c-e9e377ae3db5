# 构建说明

## 快速开始

### 安装依赖
```bash
yarn install
```

### 开发模式
```bash
yarn dev
```

## 构建发布版本

### 单平台构建

**Windows**
```bash
# Windows x64 (仅 x64 架构，使用专门配置)
yarn build:windows-x64

# Windows ARM64 (仅 ARM64 架构，使用专门配置)
yarn build:windows-arm64

# Windows x64 + ARM64 (两种架构)
yarn build:windows-x64-arm64

# 所有 Windows 版本 (x64 + ARM64)
yarn build:windows
```

> ⚠️ **重要**: `build:windows-x64` 和 `build:windows-arm64` 使用专门的配置文件，确保构建产物只包含对应架构的资源，不会出现架构混合的问题。

**macOS**
```bash
# macOS Intel (x64)
yarn build:macos-x64

# macOS Apple Silicon (ARM64)
yarn build:macos-arm64

# 所有 macOS 版本
yarn build:macos
```

**Linux**
```bash
# Linux x64
yarn build:linux-x64

# Linux ARM64
yarn build:linux-arm64

# 所有 Linux 版本
yarn build:linux
```

### 多平台构建

**构建所有平台**
```bash
yarn build:all
```

**使用构建工具**
```bash
# 交互式构建工具
node scripts/build-platforms.js

# 构建特定平台
node scripts/build-platforms.js windows-x64
node scripts/build-platforms.js macos
node scripts/build-platforms.js all
```

## 构建产物

构建完成后，安装包将生成在 `dist/` 目录中：

- **Windows**: `MEEA-VIOFO-Setup-{version}-windows-{arch}.exe`
- **macOS**: `MEEA-VIOFO-{version}-mac-{arch}.dmg`
- **Linux**: `MEEA-VIOFO-{version}-linux-{arch}.AppImage`

> 📋 **命名说明**: 我们使用 `windows` 而不是 `win32` 来提高用户友好性。详见 [docs/BUILD_NAMING_CONVENTION.md](docs/BUILD_NAMING_CONVENTION.md)

## 常见问题

### 构建失败
1. 确保已安装所有依赖：`yarn install`
2. 清理缓存：`yarn cleanup`
3. 重新构建：`yarn build:windows-x64`

### 运行时错误
如果构建的应用启动时出现 "Cannot find module" 错误：
1. 检查是否使用了正确的构建配置
2. 确保所有生产依赖都在 `package.json` 的 `dependencies` 中
3. 重新构建应用

> 📋 **依赖打包说明**: 查看 [docs/DEPENDENCY_PACKAGING_FIX.md](docs/DEPENDENCY_PACKAGING_FIX.md) 了解依赖打包问题的解决方案

### 跨平台构建
- 在 macOS 上可以构建所有平台
- 在 Windows 上可以构建 Windows 和 Linux
- 在 Linux 上可以构建 Windows 和 Linux

### 构建时间
- 单平台构建：约 3-5 分钟
- 全平台构建：约 15-20 分钟

## 推荐构建流程

### 开发者
```bash
# 1. 开发测试
yarn dev

# 2. 构建当前平台测试
yarn build:windows-x64  # 或对应平台

# 3. 发布前构建所有平台
yarn build:all
```

### CI/CD
```bash
# 分别构建不同平台，提高并行效率
yarn build:windows-x64 &
yarn build:macos-x64 &
yarn build:linux-x64 &
wait
```

## 调试功能

### 已启用的调试功能
构建版本中已启用以下调试功能，便于问题排查：

- ✅ **开发者工具**: 按 `F12` 或 `Ctrl+Shift+I` 打开
- ✅ **Console 控制台**: 查看应用日志和错误信息
- ✅ **右键菜单**: 包含"检查元素"等调试选项
- ✅ **调试快捷键**: 所有标准调试快捷键都可用

> 📋 **详细说明**: 查看 [docs/DEBUG_FEATURES.md](docs/DEBUG_FEATURES.md) 了解如何使用调试功能

## 构建优化

### 加速构建
- 使用 SSD 硬盘
- 增加内存分配
- 使用多核 CPU

### 减小包体积
- 已启用代码压缩
- 已排除开发依赖
- 已优化资源文件
